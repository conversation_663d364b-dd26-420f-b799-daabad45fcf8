"use client";

import { useForm } from "react-hook-form";
import * as z from "zod";
import axios from "axios";
import { zodResolver } from "@hookform/resolvers/zod";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Course } from "@prisma/client";
import DynamicInputList from "@/components/shared/dynamic-input-list";

interface PrerequisitesFormProps {
  initialData: Course;
  courseId: string;
}

const formSchema = z.object({
  prerequisites: z
    .array(
      z.string().min(1, {
        message: "Le prérequis ne peut pas être vide",
      })
    )
    .min(1, {
      message: "Au moins un prérequis est nécessaire",
    }),
});

export const PrerequisitesForm = ({
  initialData,
  courseId,
}: PrerequisitesFormProps) => {
  const router = useRouter();
  const { toast } = useToast();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      prerequisites: initialData?.prerequisites || [""],
    },
  });

  const { isSubmitting, isValid } = form.formState;

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      await axios.patch(`/api/courses/${courseId}`, {
        prerequisites: values.prerequisites.filter(
          (prereq) => prereq.trim() !== ""
        ),
      });

      toast({
        title: "Prérequis mis à jour",
        description: "Les prérequis du cours ont été enregistrés",
      });
      router.refresh();
    } catch {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la mise à jour",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="w-full">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="prerequisites"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <DynamicInputList
                    items={field.value}
                    onAdd={(item) => field.onChange([...field.value, item])}
                    onEdit={(index, newItem) => {
                      const updatedItems = [...field.value];
                      updatedItems[index] = newItem;
                      field.onChange(updatedItems);
                    }}
                    onDelete={(index) => {
                      const updatedItems = field.value.filter(
                        (_, i) => i !== index
                      );
                      field.onChange(
                        updatedItems.length > 0 ? updatedItems : [""]
                      );
                    }}
                    placeholder="Quelles connaissances sont nécessaires avant de suivre ce cours ?"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </form>
      </Form>
    </div>
  );
};
