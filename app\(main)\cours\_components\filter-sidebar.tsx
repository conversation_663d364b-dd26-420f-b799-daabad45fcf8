"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Separator } from "@/components/ui/separator";
import { Category } from "@prisma/client";

const FilterSidebar = ({ categories }: { categories: Category[] }) => {
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<string>("mostRecent");

  const router = useRouter();
  const searchParams = useSearchParams();

  // Sync with URL params
  useEffect(() => {
    const categoryId = searchParams.get("categoryId") || "";
    const sort = searchParams.get("sortBy") || "mostRecent";

    setSelectedCategories(categoryId ? [categoryId] : []);
    setSortBy(sort);
  }, [searchParams]);

  const updateFilters = (newCategories: string[], newSortBy: string) => {
    const params = new URLSearchParams();

    if (newCategories.length > 0) {
      params.set("categoryId", newCategories.join(","));
    }
    params.set("sortBy", newSortBy);

    router.push(`?${params.toString()}`);
  };

  const handleCategoryChange = (categoryId: string) => {
    const updatedCategories = selectedCategories.includes(categoryId)
      ? selectedCategories.filter((id) => id !== categoryId)
      : [...selectedCategories, categoryId];

    setSelectedCategories(updatedCategories);
    updateFilters(updatedCategories, sortBy);
  };

  const handleSortChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newSortBy = event.target.value;
    setSortBy(newSortBy);
    updateFilters(selectedCategories, newSortBy);
  };

  const resetFilters = () => {
    setSelectedCategories([]);
    setSortBy("mostRecent");
    router.push(`?`);
  };

  return (
    <div className="w-[720px] h-full flex flex-col gap-4 max-xl:hidden">
      {/* TITLE */}
      <p className="text-3xl font-semibold text-left">Filtrer les cours</p>

      {/* SORT BY */}
      <div className="flex items-center justify-between">
        <label htmlFor="sortBy" className="text-lg font-medium">
          Trier par:
        </label>
        <select
          id="sortBy"
          value={sortBy}
          onChange={handleSortChange}
          className="p-2 border rounded-md"
        >
          <option value="mostRecent">Le plus récent</option>
          <option value="mostPopular">Le plus populaire</option>
        </select>
      </div>

      <Separator />

      {/* CATEGORIES FILTER */}
      <div>
        <p className="text-lg font-medium mb-2">Catégories</p>
        {categories.map((category) => (
          <div key={category.id} className="flex items-center gap-2 mb-2">
            <input
              type="checkbox"
              id={category.id}
              checked={selectedCategories.includes(category.id)}
              onChange={() => handleCategoryChange(category.id)}
            />
            <label htmlFor={category.id} className="text-sm">
              {category.name}
            </label>
          </div>
        ))}
      </div>

      <Separator />

      {/* RESET BUTTON */}
      <button
        onClick={resetFilters}
        className="p-2 bg-gray-200 rounded-md hover:bg-gray-300"
      >
        Réinitialiser les filtres
      </button>
    </div>
  );
};

export default FilterSidebar;
