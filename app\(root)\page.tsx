// COMPONENTS
import Hero from "@/components/home/<USER>";
import About from "@/components/home/<USER>";
import PopularCourses from "@/components/home/<USER>";
import Gallery from "@/components/home/<USER>";
import Testimonials from "@/components/home/<USER>";
import Contact from "@/components/home/<USER>";
import FAQ from "@/components/home/<USER>";
import Footer from "@/components/shared/Footer";

export default function Home() {
  return (
    <main className="flex min-h-screen flex-col items-center justify-between relative overflow-hidden">
      {/* Global unified background */}
      <div className="fixed inset-0 w-full h-full bg-white dark:bg-neutral-950 bg-dot-black/[0.05] dark:bg-dot-white/[0.05] -z-10"></div>

      {/* Global floating glows */}
      <div className="fixed top-20 left-20 w-96 h-96 bg-orange-400/15 rounded-full blur-3xl animate-pulse -z-10"></div>
      <div className="fixed top-1/3 right-10 w-80 h-80 bg-purple-400/15 rounded-full blur-3xl animate-pulse delay-1000 -z-10"></div>
      <div className="fixed bottom-1/4 left-1/3 w-72 h-72 bg-white/10 rounded-full blur-3xl animate-pulse delay-500 -z-10"></div>
      <div className="fixed bottom-20 right-20 w-64 h-64 bg-orange-400/10 rounded-full blur-3xl animate-pulse delay-2000 -z-10"></div>
      <div className="fixed top-2/3 left-10 w-56 h-56 bg-purple-400/10 rounded-full blur-3xl animate-pulse delay-1500 -z-10"></div>

      <Hero />
      <About />
      <PopularCourses />
      <Gallery />
      <Testimonials />
      <Contact />
      <FAQ />
      <Footer />
    </main>
  );
}
