"use client";

import { useState } from "react";
import axios from "axios";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { ConfirmModal } from "@/components/modals/confirm-modal";
import { Trash, Eye, EyeOff } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface ChapterActionsProps {
  disabled: boolean;
  courseId: string;
  sectionId: string;
  chapterId: string;
  isPublished: boolean;
}

export const ChapterActions = ({
  disabled,
  courseId,
  sectionId,
  chapterId,
  isPublished,
}: ChapterActionsProps) => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const { toast } = useToast(); // Add this at the top of your component

  const onClick = async () => {
    try {
      setIsLoading(true);

      if (isPublished) {
        await axios.patch(
          `/api/courses/${courseId}/sections/${sectionId}/chapters/${chapterId}/unpublish`
        );
        toast({
          title: "Chapitre dépublié",
          description: "Ce chapitre n'est plus visible par les étudiants",
        });
      } else {
        await axios.patch(
          `/api/courses/${courseId}/sections/${sectionId}/chapters/${chapterId}/publish`
        );
        toast({
          title: "Chapitre publié",
          description: "Ce chapitre est maintenant visible dans le cours",
        });
      }

      router.refresh();
    } catch {
      toast({
        title: "Erreur",
        description: "Impossible de modifier l'état de publication",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const onDelete = async () => {
    try {
      setIsLoading(true);

      await axios.delete(
        `/api/courses/${courseId}/sections/${sectionId}/chapters/${chapterId}`
      );

      toast({
        title: "Suppression réussie",
        description: "Le chapitre a été supprimé définitivement",
      });
      router.refresh();
      router.push(`/teacher/courses/${courseId}/sections/${sectionId}`);
    } catch {
      toast({
        title: "Erreur",
        description: "Échec de la suppression du chapitre",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center gap-x-2">
      <Button
        onClick={onClick}
        disabled={disabled || isLoading}
        variant={isPublished ? "outline" : "default"}
        size="sm"
      >
        {isPublished ? (
          <>
            <EyeOff className="h-4 w-4" />
            Dépublier
          </>
        ) : (
          <>
            <Eye className="h-4 w-4" />
            Publier
          </>
        )}
      </Button>
      <ConfirmModal onConfirm={onDelete}>
        <Button size="sm" disabled={isLoading} variant={"destructive"}>
          <Trash className="h-4 w-4" />
          Supprimer
        </Button>
      </ConfirmModal>
    </div>
  );
};
