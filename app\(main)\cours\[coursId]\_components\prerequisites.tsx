import React from "react";
import { AlertCircle } from "lucide-react";

interface PrerequisitesProps {
  items: string[] | undefined;
}

const Prerequisites = ({ items }: PrerequisitesProps) => {
  return (
    <section className="py-14">
      <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-300 mb-6">
        Course Prerequisites
      </h1>
      <div className="course-container">
        <div className="glass-card p-6 md:p-8 border-l-4 border-color border">
          <div className="flex items-start mb-4">
            <AlertCircle className="h-5 w-5 text-course-blue mr-2 mt-0.5" />
            <p className="text-lg font-medium text-course-dark">
              Before you begin, you should have:
            </p>
          </div>

          <ul className="space-y-3 ml-7">
            {items?.map((item, index) => (
              <li
                key={index}
                className="text-course-gray list-disc marker:text-course-blue"
              >
                {item}
              </li>
            ))}
          </ul>

          {items?.length === 0 && (
            <p className="text-course-gray ml-7">
              No prior knowledge required! This course is designed for
              beginners.
            </p>
          )}
        </div>
      </div>
    </section>
  );
};

export default Prerequisites;
