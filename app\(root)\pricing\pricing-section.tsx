import React from "react";

// Définir le type pour l'objet Feature
interface Feature {
  included: boolean;
  text: string;
}

// Définir le type des props pour PricingCard
interface PricingCardProps {
  title: string;
  description: string;
  price: number;
  isPopular: boolean;
  features: Feature[];
  buttonText: string;
  buttonClass: string;
}

const PricingCard: React.FC<PricingCardProps> = ({
  title,
  description,
  price,
  isPopular,
  features,
  buttonText,
  buttonClass,
}) => (
  <div
    className={`relative z-10 bg-white border rounded-xl md:p-10 dark:bg-neutral-900 dark:border-neutral-800 ${
      isPopular
        ? "shadow-xl shadow-gray-200 dark:shadow-gray-900/20"
        : "shadow-md shadow-gray-300 dark:shadow-gray-800"
    }`}
  >
    {isPopular && (
      <span className="absolute top-0 end-0 rounded-se-xl rounded-es-xl text-xs font-medium bg-gray-800 text-white py-1.5 px-3 dark:bg-white dark:text-neutral-800">
        Le plus populaire
      </span>
    )}
    <h3 className="text-xl font-bold text-gray-800 dark:text-neutral-200">
      {title}
    </h3>
    <div className="text-sm text-gray-500 dark:text-neutral-500">
      {description}
    </div>
    <div className="mt-5">
      <span className="text-6xl font-bold text-gray-800 dark:text-neutral-200">
        {price} MAD
      </span>
      <span className="text-lg font-bold text-gray-800 dark:text-neutral-200">
        / mois
      </span>
    </div>
    <div className="mt-5 flex py-4">
      <ul className="space-y-3 text-sm sm:text-base w-full">
        {features.map((feature, index) => (
          <li key={index} className="flex gap-x-3">
            <span
              className={`mt-0.5 size-5 flex justify-center items-center rounded-full ${
                feature.included
                  ? "bg-orange-50 text-orange-600 dark:bg-orange-800/30 dark:text-orange-500"
                  : "bg-gray-50 text-gray-500 dark:bg-neutral-800 dark:text-neutral-500"
              }`}
            >
              <svg
                className="shrink-0 size-3.5"
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                {feature.included ? (
                  <polyline points="20 6 9 17 4 12" />
                ) : (
                  <>
                    <path d="M18 6 6 18" />
                    <path d="m6 6 12 12" />
                  </>
                )}
              </svg>
            </span>
            <span className="text-gray-800 dark:text-neutral-200">
              {feature.text}
            </span>
          </li>
        ))}
      </ul>
    </div>
    <div className="mt-5 flex items-center justify-between gap-x-4 py-4 first:pt-0 last:pb-0">
      <div>
        <p className="text-sm text-gray-500 dark:text-neutral-500">
          Annulation à tout moment
        </p>
        <p className="text-sm text-gray-500 dark:text-neutral-500">
          Carte bancaire requise.
        </p>
      </div>
      <div className="flex justify-end">
        <button
          type="button"
          className={`py-3 px-4 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg  ${buttonClass}`}
        >
          {buttonText}
        </button>
      </div>
    </div>
  </div>
);

const PricingSection: React.FC = () => (
  <div className="flex max-lg:flex-col items-center justify-center gap-6 lg:gap-8">
    <PricingCard
      title="Parcours du Savant"
      description="Accès complet aux cours et ressources."
      price={75}
      isPopular={false}
      features={[
        {
          included: true,
          text: "Accès complet aux cours et sessions en direct",
        },
        {
          included: true,
          text: "Feuilles de triche améliorées",
        },
        {
          included: true,
          text: "Exercices pratiques illimités",
        },
        {
          included: true,
          text: "Fonctionnalités interactives du blog et articles exclusifs",
        },
        {
          included: true,
          text: "Tableau de bord personnalisé",
        },
        { included: true, text: "Support par email prioritaire" },
        {
          included: true,
          text: "Participation aux défis mensuels avec récompenses",
        },
      ]}
      buttonText="Essai gratuit"
      buttonClass="border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
    />
    <PricingCard
      title="Coffre du Maître"
      description="Pour une expérience complète et personnalisée."
      price={150}
      isPopular={true}
      features={[
        {
          included: true,
          text: "Accès complet à tous les cours",
        },
        {
          included: true,
          text: "Feuilles de triche premium",
        },
        {
          included: true,
          text: "Séances de tutorat personnalisées mensuelles",
        },
        {
          included: true,
          text: "Exercices pratiques illimités",
        },
        {
          included: true,
          text: "Contenu de blog exclusif",
        },
        {
          included: true,
          text: "Plans d'apprentissage personnalisés",
        },
        {
          included: true,
          text: "Accès direct aux instructeurs",
        },
      ]}
      buttonText="Essai gratuit"
      buttonClass="bg-orange-600 text-white hover:bg-orange-700 focus:outline-none focus:bg-orange-700 disabled:opacity-50 disabled:pointer-events-none"
    />
  </div>
);

export default PricingSection;
