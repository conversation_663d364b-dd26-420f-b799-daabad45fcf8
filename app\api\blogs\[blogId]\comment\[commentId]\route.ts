import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/auth";

export async function DELETE(
  req: Request,
  { params }: { params: { commentId: string } }
) {
  try {
    const session = await auth();
    const userId = session?.user.id;

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Recursive function to delete a comment and its replies
    const deleteCommentAndReplies = async (commentId: string) => {
      // Find all replies to the comment
      const replies = await db.comment.findMany({
        where: {
          parentId: commentId,
        },
      });

      // Recursively delete replies
      for (const reply of replies) {
        await deleteCommentAndReplies(reply.id);
      }

      // Delete the comment itself
      await db.comment.delete({
        where: {
          id: commentId,
        },
      });
    };

    // Check if the comment belongs to the user
    const comment = await db.comment.findUnique({
      where: {
        id: params.commentId,
      },
    });

    if (!comment || comment.userId !== userId) {
      return new NextResponse("Unauthorized", { status: 403 });
    }

    // Delete the comment and its replies
    await deleteCommentAndReplies(params.commentId);

    return NextResponse.json({ message: "Comment deleted" });
  } catch (error) {
    console.error("[DELETE_COMMENT]", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
