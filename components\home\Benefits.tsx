import Image from "next/image";

const Benefits = () => {
  return (
    <div className="flex w-[90%] h-[100%] flex-col items-center justify-center gap-3 p-20 max-lg:p-16 max-md:p-8">
      <p className="lil-head-text">Nos Avantages</p>
      <p className="title-text">
        Pour qui est{" "}
        <span className="text-orange-900 dark:text-orange-400 font-bold">
          ALEPHNULL
        </span>{" "}
        ?
      </p>
      <p className="p-text text-orange-700 dark:text-orange-300 mt-5">
        Découvrez les parcours inspirants de nos étudiants et comprenez comment
        notre plateforme éducative a joué un rôle clé dans leur réussite
        académique.
      </p>
      <div className="flex items-start justify-around w-full mt-12 max-lg:flex-col max-lg:items-center ">
        <div className="p-12 border-2 rounded-3xl max-w-[45%] max-lg:max-w-[90%]">
          <h1 className="big-text text-center">
            Vous vous intégrerez parfaitement si :
          </h1>
          <ul className="mt-12 flex flex-col gap-8">
            <li className="flex gap-8 items-start ">
              <Image
                src="/assets/icons/check.svg"
                alt="check"
                width={32}
                height={32}
              />
              <p className="p-text text-left leading-7">
                <span className="text-black dark:text-white">
                  Vous êtes avide de connaissances :
                </span>
                Notre site éducatif offre un contenu de qualité pour ceux qui
                cherchent à approfondir leurs connaissances.
              </p>
            </li>
            <li className="flex gap-8 items-start ">
              <Image
                src="/assets/icons/check.svg"
                alt="check"
                width={32}
                height={32}
              />
              <p className="p-text text-left leading-7">
                <span className="text-black dark:text-white">
                  Vous recherchez une expérience interactive :{" "}
                </span>
                Avec des éléments tels que des lives, des forums et des
                exercices, notre site encourage activement la participation des
                utilisateurs.
              </p>
            </li>
            <li className="flex gap-8 items-start ">
              <Image
                src="/assets/icons/check.svg"
                alt="check"
                width={32}
                height={32}
              />
              <p className="p-text text-left leading-7">
                <span className="text-black dark:text-white">
                  Vous appréciez une navigation fluide :{" "}
                </span>
                Une interface conviviale rend la recherche d'informations aisée,
                assurant une expérience utilisateur agréable.
              </p>
            </li>
            <li className="flex gap-8 items-start ">
              <Image
                src="/assets/icons/check.svg"
                alt="check"
                width={32}
                height={32}
              />
              <p className="p-text text-left leading-7">
                <span className="text-black dark:text-white">
                  Vous êtes motivé par la reconnaissance :{" "}
                </span>
                Des récompenses telles que des certifications soulignent vos
                accomplissements, renforçant ainsi votre engagement envers notre
                plateforme éducative.
              </p>
            </li>
          </ul>
          <div className="items-start"></div>
        </div>
        {/* Second one */}
        <div className="p-12 rounded-3xl max-w-[45%] max-lg:max-w-full max-lg:px-4">
          <h1 className="big-text text-center">
            Nous ne sommes pas faits pour vous si :
          </h1>
          <ul className="mt-12 flex flex-col gap-8">
            <li className="flex gap-8 items-start ">
              <Image
                src="/assets/icons/x.svg"
                alt="check"
                width={32}
                height={32}
              />
              <p className="p-text text-left leading-7">
                <span className="text-black dark:text-white">
                  Vous préférez des contenus peu informatifs :{" "}
                </span>
                Notre site met l'accent sur des contenus éducatifs de qualité,
                et si vous préférez des informations moins détaillées, notre
                plateforme pourrait ne pas correspondre à vos attentes.
              </p>
            </li>
            <li className="flex gap-8 items-start ">
              <Image
                src="/assets/icons/x.svg"
                alt="check"
                width={32}
                height={32}
              />
              <p className="p-text text-left leading-7">
                <span className="text-black dark:text-white">
                  L'interactivité n'est pas votre priorité :{" "}
                </span>
                Si vous n'êtes pas intéressé par des activités interactives
                telles que des quiz, des forums et des exercices pratiques, vous
                pourriez ne pas bénéficier pleinement de notre approche
                d'apprentissage.
              </p>
            </li>
            <li className="flex gap-8 items-start ">
              <Image
                src="/assets/icons/x.svg"
                alt="check"
                width={32}
                height={32}
              />
              <p className="p-text text-left leading-7">
                <span className="text-black dark:text-white">
                  Vous trouvez une navigation complexe acceptable :{" "}
                </span>
                Pour ceux qui tolèrent des interfaces difficiles à naviguer,
                notre site axé sur la facilité d'utilisation pourrait ne pas
                répondre à vos préférences.
              </p>
            </li>
            <li className="flex gap-8 items-start ">
              <Image
                src="/assets/icons/x.svg"
                alt="check"
                width={32}
                height={32}
              />
              <p className="p-text text-left leading-7">
                <span className="text-black dark:text-white">
                  La reconnaissance n'est pas motivante pour vous :{" "}
                </span>
                Si vous n'êtes pas intéressé par la reconnaissance à travers des
                certifications ou des badges, notre système de récompenses
                pourrait ne pas correspondre à votre style d'apprentissage.
              </p>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default Benefits;
