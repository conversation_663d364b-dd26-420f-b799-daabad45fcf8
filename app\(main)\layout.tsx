import { auth } from "@/auth";
import "../globals.css";

// PROVIDERS
import { ThemeProvider } from "@/utils/theme-provider";
import { SessionProvider } from "next-auth/react";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/shared/sidebar";

// COMPONENTS
import Navbar from "@/components/shared/Navbar";
import Footer from "@/components/shared/Footer";
import { ToastProvider } from "@/components/providers/toaster-provider";
import { Spotlight } from "@/components/ui/spotlight-new";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="relative z-0">
      {" "}
      {/* Ensures the root container is properly layered */}
      <SidebarProvider defaultOpen={false}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <ToastProvider />

          {/* Sidebar with a lower z-index so it doesn't overlap navbar */}
          <div className="h-full flex-col fixed inset-y-0 z-20">
            <AppSidebar />
          </div>

          <main className="relative z-10 w-full">
            <Navbar />
            {/* Main content area with proper z-index layering */}
            <div className="overflow-hidden dark:bg-grid-white/[0.04] bg-grid-black/[0.04] relative w-full flex flex-col items-center gap-3 p-32 max-2xl:px-24 max-xl:px-12 max-lg:py-24 max-md:px-8 max-sm:px-4 z-10">
              <div className="absolute pointer-events-none inset-0 flex items-center justify-center dark:bg-black bg-white [mask-image:radial-gradient(ellipse_70%_7%_at_top,transparent_30%,black)]"></div>
              <Spotlight />
              <div className="mx-auto relative z-10 w-full">{children}</div>
            </div>
            <Footer />
          </main>
        </ThemeProvider>
      </SidebarProvider>
    </div>
  );
}
