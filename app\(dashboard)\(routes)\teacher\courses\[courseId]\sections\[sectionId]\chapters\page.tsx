import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { db } from "@/lib/db";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import DashboardPageTitle from "@/app/(dashboard)/_components/dashboard-page-title";
import { ChaptersForm } from "../_components/chapters-form";

const page = async ({
  params,
}: {
  params: { courseId: string; sectionId: string };
}) => {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return redirect("/");
  }

  const section = await db.section.findUnique({
    where: {
      id: params.sectionId,
      courseId: params.courseId,
    },
    include: {
      chapters: {
        orderBy: {
          position: "asc",
        },
      },
    },
  });

  if (!section) {
    return redirect("/");
  }

  return (
    <div className="w-full z-20 flex flex-col">
      <Link
        href={`/teacher/courses/${params.courseId}/sections/${params.sectionId}`}
        className="flex items-center text-sm hover:opacity-75 transition mb-6"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to Section
      </Link>
      <div className="flex items-start justify-between gap-y-4 max-2xl:flex-col">
        <DashboardPageTitle
          title="Chapters"
          description="Manage chapters for this section"
        />
      </div>
      <div className="mt-8">
        <ChaptersForm
          initialData={section}
          courseId={params.courseId}
          sectionId={params.sectionId}
        />
      </div>
    </div>
  );
};

export default page;
