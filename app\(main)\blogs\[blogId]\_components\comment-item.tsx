// "use client";

// import { useState } from "react";
// import ReplyForm from "./reply-form";
// import { Comment, User } from "@prisma/client";

// interface CommentItemProps {
//   comment: Comment;
//   blogId: string;
//   currentUserId?: string;
//   onDelete: (commentId: string) => void;
//   onReply: (parentId: string, content: string) => void;
// }

// const formatDate = (dateString: string): string => {
//   const date = new Date(dateString);
//   const now = new Date();
//   const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

//   if (diffInSeconds < 60) {
//     return `${diffInSeconds} seconds ago`;
//   } else if (diffInSeconds < 3600) {
//     const minutes = Math.floor(diffInSeconds / 60);
//     return `${minutes} minute${minutes > 1 ? "s" : ""} ago`;
//   } else if (diffInSeconds < 86400) {
//     const hours = Math.floor(diffInSeconds / 3600);
//     return `${hours} hour${hours > 1 ? "s" : ""} ago`;
//   } else {
//     const days = Math.floor(diffInSeconds / 86400);
//     return `${days} day${days > 1 ? "s" : ""} ago`;
//   }
// };

// const CommentItem = ({
//   comment,
//   blogId,
//   currentUserId,
//   onDelete,
//   onReply,
// }: CommentItemProps) => {
//   const [showReplyForm, setShowReplyForm] = useState(false);

//   const handleDelete = () => {
//     onDelete(comment.id); // Trigger the onDelete callback
//   };

//   const handleReply = (content: string) => {
//     onReply(comment.id, content); // Trigger the onReply callback
//     setShowReplyForm(false); // Hide the reply form after submission
//   };

//   return (
//     <div className="border-l-2 pl-4">
//       <div className="flex items-start gap-3">
//         <img
//           src={comment.user.image || ""}
//           alt={`${comment.user.firstName} ${comment.user.lastName}`}
//           className="w-8 h-8 rounded-full"
//         />
//         <div className="flex-1">
//           <div className="flex items-center gap-2">
//             <p className="font-semibold">
//               {comment.user.firstName} {comment.user.lastName}
//             </p>
//             <p className="text-xs text-gray-500">
//               {formatDate(comment.createdAt)}
//             </p>
//           </div>
//           <p className="text-sm text-white mt-1">{comment.content}</p>
//           <div className="flex gap-2 mt-1">
//             <button
//               onClick={() => setShowReplyForm(!showReplyForm)}
//               className="text-sm text-blue-500"
//             >
//               Reply
//             </button>
//             {comment.user.id === currentUserId && (
//               <button onClick={handleDelete} className="text-sm text-red-500">
//                 Delete
//               </button>
//             )}
//           </div>
//         </div>
//       </div>
//       {showReplyForm && (
//         <ReplyForm
//           onCancel={() => setShowReplyForm(false)} // Hide the reply form on cancel
//           onSubmit={handleReply} // Handle reply submission
//         />
//       )}
//       {comment.replies.length > 0 && (
//         <div className="ml-8 mt-4 space-y-4">
//           {comment.replies.map((reply) => (
//             <CommentItem
//               key={reply.id}
//               comment={reply}
//               blogId={blogId}
//               currentUserId={currentUserId}
//               onDelete={onDelete}
//               onReply={onReply}
//             />
//           ))}
//         </div>
//       )}
//     </div>
//   );
// };

// export default CommentItem;
