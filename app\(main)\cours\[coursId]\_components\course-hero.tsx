import React from "react";
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Award } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface CourseHeroProps {
  title: string | undefined;
  description: string | undefined;
  imageUrl: string | undefined;
  rating?: number;
  students?: number;
  duration?: string;
  level?: string;
}

const CourseHero = ({
  title,
  description,
  imageUrl,
  rating = 4.8,
  students = 10542,
  duration = "42 hours",
  level = "All Levels",
}: CourseHeroProps) => {
  return (
    <section className="hero-gradient overflow-hidden">
      <div className="course-container">
        <div className="grid md:grid-cols-2 gap-10 items-center">
          <div className="space-y-6 staggered-fade-in">
            <Badge>Best Seller</Badge>

            <h1 className="text-4xl md:text-5xl font-bold leading-tight">
              {title}
            </h1>

            <p className="text-lg text-gray-600 dark:text-gray-400 text-pretty">
              {description}
            </p>

            <div className="flex items-center flex-wrap gap-4">
              <div className="flex items-center">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-5 w-5 ${
                        i < Math.floor(rating)
                          ? "text-yellow-400 fill-yellow-400"
                          : "text-gray-300"
                      }`}
                    />
                  ))}
                </div>
                <span className="ml-2 text-course-dark font-medium">
                  {rating}
                </span>
                <span className="ml-1 text-course-gray">
                  ({students.toLocaleString()} students)
                </span>
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex items-center text-course-gray">
                  <Clock className="h-4 w-4 mr-1" />
                  <span>{duration}</span>
                </div>
                <div className="flex items-center text-course-gray">
                  <BookOpen className="h-4 w-4 mr-1" />
                  <span>{level}</span>
                </div>
                <div className="flex items-center text-course-gray">
                  <Award className="h-4 w-4 mr-1" />
                  <span>Certificate</span>
                </div>
              </div>
            </div>

            <div className="flex flex-wrap gap-4 pt-2">
              <Button variant={"default"}>Enroll Now</Button>
              <Button variant={"outline"}>Try Free Preview</Button>
            </div>
          </div>

          <div className="relative animate-fade-in-right">
            <div className="relative rounded-2xl overflow-hidden shadow-xl">
              <img
                src={imageUrl}
                alt={title}
                className="w-full object-cover aspect-video"
              />

              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-transparent mix-blend-overlay"></div>

              <div className="absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-t from-black/70 to-transparent text-white">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-green-500"></div>
                  <span>Updated January 2023</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CourseHero;
