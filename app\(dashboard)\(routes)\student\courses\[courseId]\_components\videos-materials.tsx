"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { 
  Video, 
  FileText, 
  Download, 
  Search, 
  Play, 
  Clock, 
  Eye,
  BookOpen,
  FileImage,
  File
} from "lucide-react";

interface Material {
  id: string;
  title: string;
  type: "video" | "pdf" | "slides" | "document" | "image";
  duration?: string;
  size?: string;
  downloadUrl: string;
  thumbnailUrl?: string;
  description?: string;
  chapter: string;
  isWatched?: boolean;
  watchTime?: string;
}

interface VideosMaterialsProps {
  materials: Material[];
}

const VideosMaterials = ({ materials }: VideosMaterialsProps) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedType, setSelectedType] = useState("all");

  // Dummy data
  const dummyMaterials: Material[] = [
    {
      id: "video-1",
      title: "Introduction aux Mathématiques - Partie 1",
      type: "video",
      duration: "15:30",
      downloadUrl: "#",
      thumbnailUrl: "/images/video-thumb-1.jpg",
      description: "Vue d'ensemble des concepts mathématiques fondamentaux",
      chapter: "Chapitre 1",
      isWatched: true,
      watchTime: "15:30"
    },
    {
      id: "video-2",
      title: "Histoire des mathématiques",
      type: "video",
      duration: "20:45",
      downloadUrl: "#",
      thumbnailUrl: "/images/video-thumb-2.jpg",
      description: "Évolution historique des mathématiques",
      chapter: "Chapitre 1",
      isWatched: true,
      watchTime: "12:30"
    },
    {
      id: "pdf-1",
      title: "Guide des Systèmes de Numération",
      type: "pdf",
      size: "2.5 MB",
      downloadUrl: "#",
      description: "Document complet sur les différents systèmes de numération",
      chapter: "Chapitre 1"
    },
    {
      id: "slides-1",
      title: "Présentation: Algèbre Fondamentale",
      type: "slides",
      size: "1.8 MB",
      downloadUrl: "#",
      description: "Diapositives du cours sur l'algèbre de base",
      chapter: "Chapitre 2"
    },
    {
      id: "video-3",
      title: "Résolution d'équations linéaires",
      type: "video",
      duration: "30:15",
      downloadUrl: "#",
      thumbnailUrl: "/images/video-thumb-3.jpg",
      description: "Méthodes de résolution des équations du premier degré",
      chapter: "Chapitre 2",
      isWatched: false
    },
    {
      id: "document-1",
      title: "Exercices corrigés - Algèbre",
      type: "document",
      size: "1.2 MB",
      downloadUrl: "#",
      description: "Collection d'exercices avec solutions détaillées",
      chapter: "Chapitre 2"
    }
  ];

  const displayMaterials = materials.length > 0 ? materials : dummyMaterials;

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "video": return <Video className="w-5 h-5 text-blue-500" />;
      case "pdf": return <FileText className="w-5 h-5 text-red-500" />;
      case "slides": return <FileImage className="w-5 h-5 text-purple-500" />;
      case "document": return <File className="w-5 h-5 text-green-500" />;
      case "image": return <FileImage className="w-5 h-5 text-orange-500" />;
      default: return <BookOpen className="w-5 h-5 text-gray-500" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "video": return "Vidéo";
      case "pdf": return "PDF";
      case "slides": return "Diapositives";
      case "document": return "Document";
      case "image": return "Image";
      default: return "Fichier";
    }
  };

  const getTypeBadgeColor = (type: string) => {
    switch (type) {
      case "video": return "bg-blue-100 text-blue-800 dark:bg-blue-950/20 dark:text-blue-400";
      case "pdf": return "bg-red-100 text-red-800 dark:bg-red-950/20 dark:text-red-400";
      case "slides": return "bg-purple-100 text-purple-800 dark:bg-purple-950/20 dark:text-purple-400";
      case "document": return "bg-green-100 text-green-800 dark:bg-green-950/20 dark:text-green-400";
      case "image": return "bg-orange-100 text-orange-800 dark:bg-orange-950/20 dark:text-orange-400";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-950/20 dark:text-gray-400";
    }
  };

  const filteredMaterials = displayMaterials.filter(material => {
    const matchesSearch = !searchQuery || 
      material.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      material.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      material.chapter.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesType = selectedType === "all" || material.type === selectedType;
    
    return matchesSearch && matchesType;
  });

  const videoMaterials = filteredMaterials.filter(m => m.type === "video");
  const documentMaterials = filteredMaterials.filter(m => m.type !== "video");

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Video className="w-5 h-5 text-orange-500" />
          Vidéos & Supports de Cours
        </CardTitle>
        
        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4 mt-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Rechercher dans les supports..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <Button
              variant={selectedType === "all" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedType("all")}
            >
              Tout
            </Button>
            <Button
              variant={selectedType === "video" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedType("video")}
            >
              Vidéos
            </Button>
            <Button
              variant={selectedType === "pdf" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedType("pdf")}
            >
              Documents
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <Tabs defaultValue="videos" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="videos">
              Vidéos ({videoMaterials.length})
            </TabsTrigger>
            <TabsTrigger value="documents">
              Documents ({documentMaterials.length})
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="videos" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {videoMaterials.map((material) => (
                <Card key={material.id} className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                  <div className="relative aspect-video bg-gray-100 dark:bg-gray-800">
                    {material.thumbnailUrl ? (
                      <img
                        src={material.thumbnailUrl}
                        alt={material.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Video className="w-16 h-16 text-gray-400" />
                      </div>
                    )}
                    
                    {/* Play Button Overlay */}
                    <div className="absolute inset-0 flex items-center justify-center bg-black/20 hover:bg-black/30 transition-colors duration-200 cursor-pointer">
                      <div className="w-16 h-16 bg-white/90 rounded-full flex items-center justify-center shadow-lg">
                        <Play className="w-6 h-6 text-orange-500 ml-1" />
                      </div>
                    </div>
                    
                    {/* Duration Badge */}
                    {material.duration && (
                      <div className="absolute bottom-2 right-2 bg-black/70 text-white px-2 py-1 rounded text-sm">
                        {material.duration}
                      </div>
                    )}
                    
                    {/* Watch Status */}
                    {material.isWatched && (
                      <div className="absolute top-2 left-2 bg-green-500 text-white px-2 py-1 rounded text-xs flex items-center gap-1">
                        <Eye className="w-3 h-3" />
                        Vu
                      </div>
                    )}
                  </div>
                  
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between gap-2 mb-2">
                      <h3 className="font-semibold text-gray-900 dark:text-white line-clamp-2">
                        {material.title}
                      </h3>
                      <Badge variant="outline" className={getTypeBadgeColor(material.type)}>
                        {getTypeLabel(material.type)}
                      </Badge>
                    </div>
                    
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-3 line-clamp-2">
                      {material.description}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span>{material.chapter}</span>
                        {material.watchTime && material.duration && (
                          <>
                            <span>•</span>
                            <div className="flex items-center gap-1">
                              <Clock className="w-3 h-3" />
                              <span>{material.watchTime} / {material.duration}</span>
                            </div>
                          </>
                        )}
                      </div>
                      <Button size="sm">
                        {material.isWatched ? "Revoir" : "Regarder"}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="documents" className="mt-6">
            <div className="space-y-4">
              {documentMaterials.map((material) => (
                <Card key={material.id} className="hover:shadow-md transition-shadow duration-200">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-4">
                      <div className="flex-shrink-0">
                        <div className="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
                          {getTypeIcon(material.type)}
                        </div>
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-2 mb-1">
                          <h3 className="font-semibold text-gray-900 dark:text-white">
                            {material.title}
                          </h3>
                          <Badge variant="outline" className={getTypeBadgeColor(material.type)}>
                            {getTypeLabel(material.type)}
                          </Badge>
                        </div>
                        
                        <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                          {material.description}
                        </p>
                        
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span>{material.chapter}</span>
                          {material.size && (
                            <>
                              <span>•</span>
                              <span>{material.size}</span>
                            </>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">
                          <Eye className="w-4 h-4 mr-1" />
                          Voir
                        </Button>
                        <Button size="sm">
                          <Download className="w-4 h-4 mr-1" />
                          Télécharger
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default VideosMaterials;
