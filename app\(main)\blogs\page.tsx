import { getBlogs } from "@/actions/get-blogs";

import { auth } from "@/auth";
import { BlogsList } from "@/components/shared/blogs-list";
import Title from "@/components/shared/Title";
import { db } from "@/lib/db";
import { User } from "@prisma/client";
import { redirect } from "next/navigation";
import React from "react";
import FilterSection from "./_components/filter-section";
import { Separator } from "@/components/ui/separator";

interface SearchPageProps {
  searchParams: {
    title: string;
    user: User;
    categoryId: string;
  };
}

const page = async ({ searchParams }: SearchPageProps) => {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return redirect("/");
  }

  const categories = await db.category.findMany({
    orderBy: {
      name: "asc",
    },
  });

  const blogs = await getBlogs({
    userId,
    ...searchParams,
  });

  return (
    <div className="h-full w-full z-20 flex flex-col items-center">
      <Title
        title="Blogues d'ALEPHNULL"
        label="Ressources"
        description="Cette page regroupe les articles de blog publiés par les enseignants d'ALEPHNULL, destinés à partager des ressources professionnelles et pédagogiques avec la communauté."
      />
      <div className="w-full flex my-12 gap-12 max-lg:flex-col">
        <FilterSection />
        <Separator orientation="vertical" className="max-lg:hidden" />
        <BlogsList items={blogs} />
      </div>
    </div>
  );
};

export default page;
