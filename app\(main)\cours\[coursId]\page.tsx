import React from "react";
import axios from "axios";
import Course<PERSON><PERSON> from "./_components/course-hero";
import VideoPlayer from "./_components/video-player";
import PriceCard from "./_components/price-card";
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "./_components/what-youll-learn";
import CourseContent from "./_components/course-content";
import CourseIncludes from "./_components/course-includes";
import Prerequisites from "./_components/prerequisites";
import CourseOverview from "./_components/course-overview";
import AboutInstructor from "./_components/about-instructor";
import Testimonials from "./_components/testimonials";
import { useCurrentUser } from "@/hooks/use-current-user";
import { getCourse } from "@/actions/get-course";
import { getUser } from "@/actions/get-user";

type LessonType = "video" | "article" | "quiz";

interface Lesson {
  id: string;
  title: string;
  duration: string;
  type: LessonType;
  isPreview?: boolean;
}

interface Chapter {
  id: string;
  title: string;
  lessons: Lesson[];
}

interface Testimonial {
  id: string;
  name: string;
  role: string;
  content: string;
  rating: number;
  avatarUrl: string;
}

const page = async ({ params }: { params: { coursId: string } }) => {
  const course = await getCourse(params.coursId);

  const courseData = {
    id: "001",
    userId: "instructor-001",
    title: "The Complete Web Development Bootcamp 2023",
    description:
      "Master HTML, CSS, JavaScript, React, Node.js, MongoDB and more with practical projects. Go from zero to full-stack developer with this comprehensive course.",
    imageUrl:
      "https://images.unsplash.com/photo-1498050108023-c5249f4df085?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2072&q=80",
    price: 89.99,
    originalPrice: 199.99,
    discount: 55,
    isPublished: true,
    introVideoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ",
    courseIncludes: [
      "42 hours of on-demand video",
      "12 articles and resources",
      "15 coding challenges",
      "4 real-world projects",
      "Certificate of completion",
      "Lifetime access",
    ],
    prerequisites: [
      "Basic computer skills",
      "No prior programming experience needed",
      "A computer with internet connection",
      "Eagerness to learn and practice",
    ],
    whatYoullLearn: [
      "Build modern, responsive websites with HTML, CSS and JavaScript",
      "Develop full-stack web applications using React.js and Node.js",
      "Work with databases like MongoDB and build RESTful APIs",
      "Implement user authentication and authorization",
      "Deploy your applications to the web",
      "Use Git for version control and collaborate with other developers",
      "Debug and troubleshoot common development issues",
      "Apply best practices for web security and performance optimization",
    ],
    overview:
      "This comprehensive course is designed to take you from a complete beginner to a confident full-stack web developer. Starting with the fundamentals of HTML, CSS, and JavaScript, you'll progressively build your skills through hands-on projects and real-world applications. By the end of the course, you'll have the knowledge and confidence to create your own web applications from scratch, implement modern features, and deploy them to the world. Whether you're looking to start a career in web development, build your own startup, or enhance your current technical skills, this course provides everything you need to succeed in today's digital landscape.",
    instructorId: "inst-001",
    instructor: {
      name: "Sarah Johnson",
      bio: "Sarah Johnson is a senior software engineer with over 10 years of experience in web development. She's worked with Fortune 500 companies and startups alike, helping them build scalable and maintainable web applications. Sarah is passionate about teaching and has helped thousands of students transition into successful careers in tech. Her teaching approach focuses on practical, hands-on learning with real-world projects.",
      imageUrl:
        "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
      coursesCount: 12,
      studentsCount: 45000,
      rating: 4.9,
    },
    testimonials: [
      {
        id: "t-001",
        name: "Alex Patterson",
        role: "Frontend Developer",
        content:
          "This course completely changed my career trajectory. I went from a complete beginner to landing my first job as a frontend developer within 6 months. Sarah's teaching style makes complex concepts easy to understand.",
        rating: 5,
        avatarUrl:
          "https://images.unsplash.com/photo-1563833717765-00462b6d6e42?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=256&q=80",
      },
      {
        id: "t-002",
        name: "Maria Rodriguez",
        role: "Student",
        content:
          "As someone with zero programming background, I was worried this would be too advanced for me. But the step-by-step approach and clear explanations made it accessible. I'm now building my own projects with confidence!",
        rating: 5,
        avatarUrl:
          "https://images.unsplash.com/photo-1536317203120-3b6300beebe8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=256&q=80",
      },
      {
        id: "t-003",
        name: "David Chen",
        role: "Software Engineer",
        content:
          "Even as an experienced developer, I found tremendous value in this course. The sections on modern React patterns and backend architecture were particularly helpful for leveling up my skills.",
        rating: 4,
        avatarUrl:
          "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=256&q=80",
      },
      {
        id: "t-004",
        name: "Emma Taylor",
        role: "Product Manager",
        content:
          "I took this course to better understand what my development team was talking about, and it exceeded my expectations. Now I can contribute more effectively to technical discussions and make more informed product decisions.",
        rating: 5,
        avatarUrl:
          "https://images.unsplash.com/photo-1573497019940-1c28c88b4f3e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=256&q=80",
      },
    ],
    chapters: [
      {
        id: "ch-001",
        title: "Introduction to Web Development",
        lessons: [
          {
            id: "l-001",
            title: "Welcome to the Course",
            duration: "5:23",
            type: "video" as LessonType,
            isPreview: true,
          },
          {
            id: "l-002",
            title: "Setting Up Your Development Environment",
            duration: "12:45",
            type: "video" as LessonType,
            isPreview: true,
          },
          {
            id: "l-003",
            title: "Web Development Overview",
            duration: "8:30",
            type: "video" as LessonType,
          },
          {
            id: "l-004",
            title: "How the Internet Works",
            duration: "10:15",
            type: "video" as LessonType,
          },
          {
            id: "l-005",
            title: "Introduction Quiz",
            duration: "15 min",
            type: "quiz" as LessonType,
          },
        ],
      },
      {
        id: "ch-002",
        title: "HTML Fundamentals",
        lessons: [
          {
            id: "l-006",
            title: "HTML Structure and Syntax",
            duration: "14:20",
            type: "video" as LessonType,
          },
          {
            id: "l-007",
            title: "Working with Text and Images",
            duration: "18:30",
            type: "video" as LessonType,
          },
          {
            id: "l-008",
            title: "HTML Forms",
            duration: "22:15",
            type: "video" as LessonType,
          },
          {
            id: "l-009",
            title: "HTML5 Semantic Elements",
            duration: "16:40",
            type: "video" as LessonType,
          },
          {
            id: "l-010",
            title: "Building Your First Webpage",
            duration: "25:10",
            type: "video" as LessonType,
          },
          {
            id: "l-011",
            title: "HTML Cheat Sheet",
            duration: "5 min read",
            type: "article" as LessonType,
          },
        ],
      },
      {
        id: "ch-003",
        title: "CSS Styling",
        lessons: [
          {
            id: "l-012",
            title: "CSS Basics and Selectors",
            duration: "19:45",
            type: "video" as LessonType,
          },
          {
            id: "l-013",
            title: "Box Model and Layouts",
            duration: "23:10",
            type: "video" as LessonType,
          },
          {
            id: "l-014",
            title: "Flexbox and Grid",
            duration: "28:35",
            type: "video" as LessonType,
          },
          {
            id: "l-015",
            title: "Responsive Design Principles",
            duration: "26:20",
            type: "video" as LessonType,
          },
          {
            id: "l-016",
            title: "CSS Animations and Transitions",
            duration: "20:15",
            type: "video" as LessonType,
          },
          {
            id: "l-017",
            title: "Modern CSS Frameworks",
            duration: "22:40",
            type: "video" as LessonType,
          },
        ],
      },
      {
        id: "ch-004",
        title: "JavaScript Essentials",
        lessons: [
          {
            id: "l-018",
            title: "JavaScript Fundamentals",
            duration: "24:30",
            type: "video" as LessonType,
          },
          {
            id: "l-019",
            title: "Working with the DOM",
            duration: "27:15",
            type: "video" as LessonType,
          },
          {
            id: "l-020",
            title: "Event Handling",
            duration: "18:45",
            type: "video" as LessonType,
          },
          {
            id: "l-021",
            title: "ES6+ Features",
            duration: "25:10",
            type: "video" as LessonType,
          },
          {
            id: "l-022",
            title: "Asynchronous JavaScript",
            duration: "30:20",
            type: "video" as LessonType,
          },
          {
            id: "l-023",
            title: "JavaScript Coding Challenge",
            duration: "45 min",
            type: "quiz" as LessonType,
          },
        ],
      },
    ],
  };

  return (
    <main>
      <CourseHero
        title={course?.title || ""}
        description={course?.description || ""}
        imageUrl={course?.imageUrl || ""}
      />

      <section className="py-14">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-300 mb-6">
          Course Introduction
        </h1>
        <div className="grid lg:grid-cols-3 gap-10">
          <div className="lg:col-span-2">
            <VideoPlayer
              videoUrl={courseData.introVideoUrl}
              title="Course Introduction"
            />
          </div>

          <div className="lg:col-span-1">
            <PriceCard
              price={course?.price || 0}
              items={course?.courseIncludes || []}
            />
          </div>
        </div>
      </section>

      <WhatYoullLearn items={course?.whatYoullLearn} />

      <CourseContent chapters={courseData.chapters} />

      <CourseIncludes items={course?.courseIncludes || []} />

      <Prerequisites items={course?.prerequisites} />

      <CourseOverview overview={course?.overview} />

      <AboutInstructor
        name={course?.instructorId || ""}
        bio={course?.instructorId || ""}
        imageUrl={course?.instructorId || ""}
        // coursesCount={course?.instructorId? || ""}
        // studentsCount={course?.instructorId || ""}
        // rating={course?.instructorId || ""}
      />

      <Testimonials testimonials={courseData.testimonials} />
    </main>
  );
};

export default page;
