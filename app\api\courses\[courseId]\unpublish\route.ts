import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/lib/db";

export async function PATCH(
  req: Request,
  { params }: { params: { courseId: string } }
) {
  try {
    const session = await auth();
    const userId = session?.user.id;

    // Validate authentication
    if (!userId) {
      return new NextResponse("Unauthorized - Login required", { status: 401 });
    }

    // Verify course ownership
    const course = await db.course.findUnique({
      where: {
        id: params.courseId,
        userId,
      },
      include: {
        sections: {
          where: {
            isPublished: true,
          },
        },
      },
    });

    if (!course) {
      return new NextResponse("Course not found or access denied", {
        status: 404,
      });
    }

    // Unpublish the course
    const unpublishedCourse = await db.course.update({
      where: {
        id: params.courseId,
      },
      data: {
        isPublished: false,
      },
    });

    // Automatically unpublish all sections in this course
    await db.section.updateMany({
      where: {
        courseId: params.courseId,
        isPublished: true,
      },
      data: {
        isPublished: false,
      },
    });

    return NextResponse.json({
      ...unpublishedCourse,
      message: "Course and all its sections have been unpublished",
    });
  } catch (error) {
    console.error("[COURSE_UNPUBLISH_ERROR]", error);
    return new NextResponse("An unexpected error occurred", { status: 500 });
  }
}
