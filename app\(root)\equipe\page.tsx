import Title from "@/components/shared/Title";
import { Spotlight } from "@/components/ui/spotlight";
import React from "react";

// Team member type definition
interface TeamMember {
  name: string;
  role: string;
  imageUrl: string;
}

// Sample data for team members
const teamMembers: TeamMember[] = [
  {
    name: "<PERSON><PERSON> Ihind",
    role: "Founder / CEO",
    imageUrl: "/assets/instructors/ihind.jpg",
  },
];

const page: React.FC = () => {
  return (
    <div className="overflow-hidden dark:bg-grid-white/[0.04] bg-grid-black/[0.04] relative w-full flex flex-col items-center justify-center gap-3 p-32 max-lg:p-8">
      <div className="absolute pointer-events-none inset-0 flex items-center justify-center dark:bg-black bg-white [mask-image:radial-gradient(ellipse_50%_30%_at_top,transparent_40%,black)]"></div>
      <Spotlight
        className="-top-100 left-0 md:left-60 md:-top-20 opacity-10"
        fill="orange"
      />
      <div className="max-w-[100rem]">
        <Title
          label="Experts Dédiés"
          title="Ensemble, nous bâtissons l'avenir"
          description=""
        />

        <div className="relative xl:w-10/12 xl:mx-auto mt-12">
          <div className="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-1 gap-8 md:gap-12 col-auto">
            {teamMembers.map((member, index) => (
              <div className="text-center" key={index}>
                <img
                  className="rounded-full w-64 mx-auto"
                  src={member.imageUrl}
                  alt={`Avatar of ${member.name}`}
                />
                <div className="mt-2 sm:mt-4">
                  <h3 className="font-medium text-gray-800 dark:text-neutral-200">
                    {member.name}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-neutral-400">
                    {member.role}
                  </p>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-12 flex justify-center">
            <div className="border border-gray-200 py-2 px-3 rounded-full dark:border-neutral-700">
              <div className="flex items-center gap-x-3">
                <span className="text-sm text-gray-500 dark:text-neutral-500">
                  Want to work with us?
                </span>
                <a
                  className="inline-flex items-center gap-x-2 text-sm font-medium text-orange-600 hover:text-orange-500 focus:outline-none focus:text-orange-500 dark:text-orange-500 dark:hover:text-orange-600 dark:focus:text-orange-600"
                  href="#"
                >
                  We are hiring
                  <svg
                    className="shrink-0 size-4"
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="m9 18 6-6-6-6" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default page;
