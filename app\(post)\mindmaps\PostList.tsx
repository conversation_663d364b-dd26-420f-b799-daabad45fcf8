import { PostItem } from "./post-item";
import { NewsletterCard } from "./NewsletterCard";

interface PostListProps {
  posts: any[];
}

export function PostList({ posts }: PostListProps) {
  return (
    <ul className="grid grid-cols-3 gap-16 max-lg:grid-cols-2 max-md:grid-cols-1">
      {posts.map((post, index) => {
        const { slug, date, title, description, tags, image } = post;

        // Insert the newsletter card in the 5th position
        if (index === 3) {
          return <NewsletterCard key="newsletter" />;
        }

        return (
          <PostItem
            key={slug}
            image={image}
            slug={slug}
            date={date}
            title={title}
            description={description}
            tags={tags}
          />
        );
      })}

      {/* If fewer than 5 posts, add newsletter card at the end */}
      {posts.length < 4 && <NewsletterCard key="newsletter-end" />}
    </ul>
  );
}
