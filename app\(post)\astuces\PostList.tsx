import { PostItem } from "./post-item";
import { NewsletterCard } from "./NewsletterCard";

interface PostListProps {
  posts: any[];
}

export function PostList({ posts }: PostListProps) {
  return (
    <div className="w-full">
      <ul className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 lg:gap-12">
        {posts.map((post, index) => {
          const { slug, date, title, description, tags, image } = post;

          // Insert the newsletter card in the 5th position
          if (index === 3) {
            return (
              <li key="newsletter" className="animate-fade-in-up" style={{ animationDelay: `${index * 150}ms` }}>
                <NewsletterCard />
              </li>
            );
          }

          return (
            <li
              key={slug}
              className="animate-fade-in-up"
              style={{ animationDelay: `${index * 150}ms` }}
            >
              <PostItem
                image={image}
                slug={slug}
                date={date}
                title={title}
                description={description}
                tags={tags}
              />
            </li>
          );
        })}

        {/* If fewer than 5 posts, add newsletter card at the end */}
        {posts.length < 4 && (
          <li key="newsletter-end" className="animate-fade-in-up" style={{ animationDelay: `${posts.length * 150}ms` }}>
            <NewsletterCard />
          </li>
        )}
      </ul>
    </div>
  );
}
