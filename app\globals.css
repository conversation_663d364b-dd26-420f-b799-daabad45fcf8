@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations for the home page */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-right {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fade-in-left {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}

.animate-fade-in-right {
  animation: fade-in-right 0.8s ease-out forwards;
}

.animate-fade-in-left {
  animation: fade-in-left 0.8s ease-out forwards;
}

.delay-200 {
  animation-delay: 200ms;
}

.delay-400 {
  animation-delay: 400ms;
}

.delay-600 {
  animation-delay: 600ms;
}

.delay-800 {
  animation-delay: 800ms;
}

.delay-1000 {
  animation-delay: 1000ms;
}

.delay-1200 {
  animation-delay: 1200ms;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 20 14.3% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 20 14.3% 4.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 20 14.3% 4.1%;
    --primary: 24.6 95% 53.1%;
    --primary-foreground: 60 9.1% 97.8%;
    --secondary: 60 4.8% 95.9%;
    --secondary-foreground: 24 9.8% 10%;
    --muted: 60 4.8% 95.9%;
    --muted-foreground: 25 5.3% 44.7%;
    --accent: 60 4.8% 95.9%;
    --accent-foreground: 24 9.8% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 60 9.1% 97.8%;
    --border: 20 5.9% 90%;
    --input: 20 5.9% 90%;
    --ring: 24.6 95% 53.1%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --chart-1: 30 100% 95%; /* Light Orange */
    --chart-2: 30 90% 80%; /* Pale Orange */
    --chart-3: 30 80% 60%; /* Soft Orange */
    --chart-4: 30 70% 50%; /* Deep Orange */
    --chart-5: 30 80% 40%; /* Dark Orange */
    --novel-highlight-default: #ffffff;
    --novel-highlight-purple: #f6f3f8;
    --novel-highlight-red: #fdebeb;
    --novel-highlight-yellow: #fbf4a2;
    --novel-highlight-blue: #c1ecf9;
    --novel-highlight-green: #acf79f;
    --novel-highlight-orange: #faebdd;
    --novel-highlight-pink: #faf1f5;
    --novel-highlight-gray: #f1f1ef;
  }

  .dark {
    --background: 20 14.3% 4.1%;
    --foreground: 60 9.1% 97.8%;
    --card: 20 14.3% 4.1%;
    --card-foreground: 60 9.1% 97.8%;
    --popover: 20 14.3% 4.1%;
    --popover-foreground: 60 9.1% 97.8%;
    --primary: 20.5 90.2% 48.2%;
    --primary-foreground: 60 9.1% 97.8%;
    --secondary: 12 6.5% 15.1%;
    --secondary-foreground: 60 9.1% 97.8%;
    --muted: 12 6.5% 15.1%;
    --muted-foreground: 24 5.4% 63.9%;
    --accent: 12 6.5% 15.1%;
    --accent-foreground: 60 9.1% 97.8%;
    --destructive: 0 72.2% 50.6%;
    --destructive-foreground: 60 9.1% 97.8%;
    --border: 12 6.5% 15.1%;
    --input: 12 6.5% 15.1%;
    --ring: 20.5 90.2% 48.2%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --chart-1: 30 100% 85%; /* Light Orange for Dark Mode */
    --chart-2: 30 90% 70%; /* Pale Orange */
    --chart-3: 30 80% 55%; /* Soft Orange */
    --chart-4: 30 70% 40%; /* Deep Orange */
    --chart-5: 30 80% 30%; /* Dark Orange */
    --novel-highlight-default: #000000;
    --novel-highlight-purple: #3f2c4b;
    --novel-highlight-red: #5c1a1a;
    --novel-highlight-yellow: #5c4b1a;
    --novel-highlight-blue: #1a3d5c;
    --novel-highlight-green: #1a5c20;
    --novel-highlight-orange: #5c3a1a;
    --novel-highlight-pink: #5c1a3a;
    --novel-highlight-gray: #3a3a3a;
  }
}

@layer utilities {
  .field-form {
    @apply w-80 self-stretch h-[66px] flex-col justify-start items-start gap-1.5 flex;
  }
  .label-form {
    @apply text-slate-600 text-sm font-medium leading-tight;
  }
  .input-form {
    @apply h-12 text-gray-600 text-base font-normal flex self-stretch px-3 py-2 bg-white rounded-lg shadow border border-gray-300 justify-start items-center gap-2 focus:outline-none;
  }

  /* Pages */
  .page-centered {
    @apply w-full flex items-center justify-center;
  }

  .paddings {
    @apply py-32 max-md:py-24 px-32 max-lg:px-16 max-md:px-8 max-sm:px-4;
  }

  /* COLORS */
  .border-color {
    @apply dark:border-neutral-800 border-neutral-400;
  }

  /* LINE CLAMP UTILITIES */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

@layer components {
  /* TEXT */
  .blog-title {
    @apply text-left font-bold text-5xl dark:text-white max-md:text-4xl max-sm:text-3xl;
  }

  .head-text {
    @apply text-left font-bold text-6xl dark:text-white max-md:text-4xl;
  }

  .title-text {
    @apply text-gray-900 dark:text-gray-100 text-center text-6xl font-bold mt-4 max-lg:text-4xl;
  }

  .big-text {
    @apply text-black dark:text-white text-left text-3xl font-bold;
  }

  .side-text {
    @apply text-gray-900 dark:text-gray-100 text-left text-2xl font-semibold max-md:text-xl max-sm:text-lg;
  }

  /* FILTERS */
  .filter-title {
    @apply text-xl font-semibold text-black dark:text-white;
  }

  .filter-description {
    @apply text-sm text-left text-gray-600 dark:text-gray-300;
  }

  /* DASHBOARD */
  .dashboard-section-title {
    @apply text-gray-900 dark:text-gray-100 text-left text-2xl font-semibold max-md:text-xl max-sm:text-lg;
  }

  .dashboard-section-desc {
    @apply text-gray-400 text-base max-sm:text-sm font-normal dark:text-gray-500;
  }

  .p-text {
    @apply text-center text-lg max-md:text-base max-sm:text-sm text-gray-500 leading-7 dark:text-gray-400;
  }

  .desc-text {
    @apply text-gray-400 text-base max-sm:text-sm font-normal dark:text-gray-500;
  }

  .lil-head-text {
    @apply text-orange-600 dark:text-orange-300 text-center text-xl font-medium;
  }

  .cta-text {
    @apply text-gray-600 text-lg font-semibold leading-6;
  }

  .nav_element {
    @apply text-base text-gray-500 font-semibold dark:text-gray-300;
  }

  /* BUTTON GROUP */
  .button-group {
    @apply text-slate-600 text-sm font-semibold leading-tight;
  }

  .button-regular {
    @apply px-3.5 py-2.5 bg-white rounded-lg shadow border border-gray-300 justify-center items-center gap-2 inline-flex text-gray-500 text-sm font-semibold leading-tight;
  }

  /* LABELS AND BADGES */
  .label {
    @apply flex p-0.5 px-4 justify-center items-center rounded-2xl bg-white text-orange-600 text-base font-semibold max-md:text-xs;
  }

  .badge {
    @apply inline-flex p-1 pl-4 items-center gap-3 rounded-2xl bg-orange-50 text-orange-600 text-base font-semibold mb-12 max-md:text-xs;
  }

  /* CARD */
  .card-head {
    @apply text-orange-500 text-sm font-semibold max-sm:text-xs;
  }

  .card-title {
    @apply text-gray-900 dark:text-white text-2xl max-sm:text-xl font-semibold;
  }

  .card-desc {
    @apply text-gray-500 dark:text-gray-400 text-base max-sm:text-sm font-normal;
  }
}

@import "~@uploadthing/react/styles.css";

@layer base {
  * {
    @apply border-border list-none;
  }

  body {
    @apply bg-background text-foreground;
  }
  .mdx {
    /* General */
    @apply prose-lg prose-invert;

    /* Headings */
    @apply prose-h1:text-3xl prose-h1:font-bold prose-h1:mt-4 prose-h1:mb-2;
    @apply prose-h2:text-2xl prose-h2:font-semibold prose-h2:mt-3 prose-h2:mb-2;
    @apply prose-h3:text-xl prose-h3:font-medium prose-h3:mt-3 prose-h3:mb-2;
    @apply prose-h4:text-lg prose-h4:font-medium prose-h4:mt-2 prose-h4:mb-1;
    @apply prose-h5:text-base prose-h5:font-medium prose-h5:mt-2 prose-h5:mb-1;
    @apply prose-h6:text-sm prose-h6:font-medium prose-h6:mt-2 prose-h6:mb-1;

    /* Paragraphs */
    @apply prose-p:text-base prose-p:leading-relaxed prose-p:mt-2 prose-p:mb-4;

    /* Lists */
    @apply prose-ul:list-disc prose-ul:pl-5 prose-ul:mt-2 prose-ul:mb-4;
    @apply prose-ol:list-decimal prose-ol:pl-5 prose-ol:mt-2 prose-ol:mb-4;

    /* Blockquotes */
    @apply prose-blockquote:border-l-4 prose-blockquote:border-gray-300 prose-blockquote:pl-4;
    @apply prose-blockquote:text-gray-600 prose-blockquote:italic prose-blockquote:mt-4 prose-blockquote:mb-4;

    /* Links */
    @apply prose-a:text-blue-500 prose-a:underline prose-a:hover:text-blue-700;

    /* Code */
    @apply prose-code:bg-gray-100 prose-code:text-sm prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:font-mono prose-code:font-medium;

    /* Tables */
    @apply prose-table:border prose-table:border-collapse prose-table:w-full prose-table:mt-4 prose-table:mb-4;
    @apply prose-th:bg-gray-200 prose-th:border prose-th:px-2 prose-th:py-1 prose-th:text-left prose-th:font-medium;
    @apply prose-td:border prose-td:px-2 prose-td:py-1 prose-td:text-left;

    /* Images */
    @apply prose-img:rounded-md prose-img:shadow-sm prose-img:mt-4 prose-img:mb-4;
  }
}
