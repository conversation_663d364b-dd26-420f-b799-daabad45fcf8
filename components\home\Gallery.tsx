"use client";

import React, { useState } from "react";
import Image from "next/image";

const Gallery = () => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  const galleryImages = [
    {
      id: 1,
      src: "/assets/gallery/students-1.jpg",
      alt: "Étudiants en cours",
      title: "Apprentissage collaboratif",
      description: "Nos étudiants travaillent ensemble pour atteindre l'excellence"
    },
    {
      id: 2,
      src: "/assets/gallery/classroom-1.jpg",
      alt: "Salle de classe moderne",
      title: "Environnement d'apprentissage",
      description: "Des espaces modernes équipés des dernières technologies"
    },
    {
      id: 3,
      src: "/assets/gallery/lab-1.jpg",
      alt: "Laboratoire informatique",
      title: "Pratique hands-on",
      description: "Laboratoires équipés pour la pratique et l'expérimentation"
    },
    {
      id: 4,
      src: "/assets/gallery/graduation-1.jpg",
      alt: "Cérémonie de remise des diplômes",
      title: "Réussite et célébration",
      description: "Célébration des réussites de nos apprenants"
    },
    {
      id: 5,
      src: "/assets/gallery/online-learning.jpg",
      alt: "Apprentissage en ligne",
      title: "Formation à distance",
      description: "Flexibilité et accessibilité pour tous"
    },
    {
      id: 6,
      src: "/assets/gallery/workshop.jpg",
      alt: "Atelier pratique",
      title: "Ateliers spécialisés",
      description: "Sessions pratiques avec des experts du domaine"
    }
  ];

  return (
    <section className="w-full min-h-screen py-24 px-8 relative">

      <div className="relative z-10 max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-16 animate-fade-in-up">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 text-sm font-medium mb-4">
            Notre univers
          </div>
          <h2 className="text-5xl font-bold mb-6 bg-gradient-to-r from-gray-900 via-orange-400 to-gray-900 dark:from-white dark:via-orange-400 dark:to-white bg-clip-text text-transparent">
            Galerie
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Découvrez l'atmosphère unique d'ALEPHNULL à travers ces moments capturés. 
            Une communauté dynamique où l'apprentissage prend vie.
          </p>
        </div>

        {/* Gallery Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
          {galleryImages.map((image, index) => (
            <div 
              key={image.id}
              className="group relative animate-fade-in-up cursor-pointer"
              style={{ animationDelay: `${index * 150}ms` }}
              onClick={() => setSelectedImage(image.src)}
            >
              <div className="relative overflow-hidden rounded-2xl bg-white dark:bg-neutral-800 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                {/* Image placeholder - replace with actual images */}
                <div className="relative aspect-[4/3] bg-gradient-to-br from-orange-200 to-orange-300 dark:from-orange-800 dark:to-orange-900">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center text-white">
                      <div className="text-4xl mb-2">📸</div>
                      <div className="text-sm font-medium">{image.title}</div>
                    </div>
                  </div>
                  
                  {/* Overlay */}
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300"></div>
                  
                  {/* Hover content */}
                  <div className="absolute inset-0 flex items-end p-6 opacity-0 group-hover:opacity-100 transition-all duration-300">
                    <div className="text-white">
                      <h3 className="font-bold text-lg mb-2">{image.title}</h3>
                      <p className="text-sm text-gray-200">{image.description}</p>
                    </div>
                  </div>
                </div>

                {/* Glow effect */}
                <div className="absolute -inset-1 bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl blur opacity-0 group-hover:opacity-25 transition duration-500 -z-10"></div>
              </div>
            </div>
          ))}
        </div>

        {/* Features showcase */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 animate-fade-in-up delay-1000">
          <div className="text-center p-8 bg-white/70 dark:bg-neutral-800/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-neutral-700/50 shadow-lg">
            <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-2xl text-white">🎓</span>
            </div>
            <h3 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">
              Environnement d'excellence
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Des espaces conçus pour favoriser l'apprentissage et la collaboration entre étudiants.
            </p>
          </div>

          <div className="text-center p-8 bg-white/70 dark:bg-neutral-800/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-neutral-700/50 shadow-lg">
            <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-2xl text-white">💻</span>
            </div>
            <h3 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">
              Technologies modernes
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Équipements de pointe et outils numériques pour une expérience d'apprentissage optimale.
            </p>
          </div>

          <div className="text-center p-8 bg-white/70 dark:bg-neutral-800/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-neutral-700/50 shadow-lg">
            <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-2xl text-white">🤝</span>
            </div>
            <h3 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">
              Communauté bienveillante
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Une communauté d'apprenants et d'enseignants passionnés, prêts à vous accompagner.
            </p>
          </div>
        </div>
      </div>

      {/* Modal for image preview */}
      {selectedImage && (
        <div 
          className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={() => setSelectedImage(null)}
        >
          <div className="relative max-w-4xl max-h-[90vh] bg-white dark:bg-neutral-800 rounded-2xl overflow-hidden">
            <button 
              className="absolute top-4 right-4 w-10 h-10 bg-black/20 hover:bg-black/40 rounded-full flex items-center justify-center text-white z-10"
              onClick={() => setSelectedImage(null)}
            >
              ✕
            </button>
            <div className="aspect-[4/3] bg-gradient-to-br from-orange-200 to-orange-300 dark:from-orange-800 dark:to-orange-900 flex items-center justify-center">
              <div className="text-center text-white">
                <div className="text-6xl mb-4">📸</div>
                <div className="text-xl font-medium">Image Preview</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default Gallery;
