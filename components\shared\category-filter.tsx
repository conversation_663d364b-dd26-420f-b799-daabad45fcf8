"use client";

import React, { useState, useEffect } from "react";
import qs from "query-string";
import { useRouter, useSearchParams } from "next/navigation";
import { cn } from "@/lib/utils"; // A utility function to conditionally apply classes
import { Checkbox } from "../ui/checkbox";
import { Badge } from "../ui/badge";

interface Category {
  id: string;
  name: string;
}

export const CategoryFilter = ({ categories }: { categories: Category[] }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedCategories, setSelectedCategories] = useState<string[]>(
    searchParams.getAll("categoryId") || []
  );

  const toggleCategory = (id: string) => {
    setSelectedCategories((prev) =>
      prev.includes(id) ? prev.filter((cat) => cat !== id) : [...prev, id]
    );
  };

  useEffect(() => {
    const url = qs.stringifyUrl(
      {
        url: "/blogs",
        query: {
          categoryId: selectedCategories,
          title: searchParams.get("title"),
        },
      },
      { skipEmptyString: true, skipNull: true }
    );
    router.push(url);
  }, [selectedCategories, searchParams, router]);

  return (
    <div className="flex flex-col space-y-4 mt-4 w-full">
      <p className="filter-title">Catégories</p>
      <div className="grid grid-cols-2 max-lg:grid-cols-3 my-4 flex-col gap-6">
        {categories.map((category) => (
          <div className="flex items-center gap-2">
            <Checkbox
              key={category.id}
              checked={selectedCategories.includes(category.id)}
              onCheckedChange={(checked) => {
                return checked
                  ? toggleCategory(category.id)
                  : toggleCategory(category.id);
              }}
            />
            <div className="text-sm dark:text-white">{category.name}</div>
          </div>
        ))}
      </div>
    </div>
  );
};
