import { Category, Blog, User } from "@prisma/client";
import { db } from "@/lib/db";

type BlogWithCategory = Blog & {
  categories: Category[];
  author: User;
};

type GetBlogs = {
  userId: string;
  title?: string;
  categoryId?: string;
};

export const getBlogs = async ({
  userId,
  title,
  categoryId,
}: GetBlogs): Promise<BlogWithCategory[]> => {
  try {
    const blogs = await db.blog.findMany({
      where: {
        userId,
        isPublished: true,
        ...(title && {
          title: {
            contains: title,
            mode: "insensitive", // Case insensitive search
          },
        }),
        ...(categoryId && {
          categories: {
            some: {
              id: categoryId, // Filter by category ID if provided
            },
          },
        }),
      },
      include: {
        categories: true,
        user: {
          // Include user (author) data
          select: {
            id: true,
            firstName: true,
            lastName: true,
            username: true,
            image: true,
            bio: true,
            phone: true,
            isTwoFactorEnabled: true,
            role: true,
            isOnboarded: true,
            email: true,
            password: true,
            emailVerified: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc", // Order by creation date (latest first)
      },
    });

    // Map the result to rename 'user' to 'author'
    return blogs.map((blog) => ({
      ...blog,
      author: blog.user,
      user: undefined,
    }));
  } catch (error) {
    console.log("[GET_BLOGS]", error);
    return [];
  }
};
