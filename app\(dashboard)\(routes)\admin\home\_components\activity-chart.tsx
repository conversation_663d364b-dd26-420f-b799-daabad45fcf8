"use client";

import { TrendingUp } from "lucide-react";
import { Area, AreaChart, CartesianGrid, XAxis } from "recharts";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";

const chartData = [
  { jour: "Lundi", heures: 5 },
  { jour: "Mardi", heures: 6 },
  { jour: "Mercredi", heures: 7 },
  { jour: "<PERSON><PERSON>", heures: 4 },
  { jour: "Vendredi", heures: 8 },
  { jour: "Samedi", heures: 3 },
  { jour: "Dimanche", heures: 2 },
];

const chartConfig = {
  heures: {
    label: "Heures",
    color: "hsl(var(--chart-1))",
  },
} satisfies ChartConfig;

export function ActivityChart() {
  return (
    <Card className="w-[30%]">
      <CardHeader>
        <CardTitle>Graphique d'Activité de l'Admin</CardTitle>
        <CardDescription>
          Affichage de l'activité de l'admin en heures par jour
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <AreaChart
            accessibilityLayer
            data={chartData}
            margin={{
              left: 12,
              right: 12,
            }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="jour"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => value.slice(0, 3)}
            />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent indicator="line" />}
            />
            <Area
              dataKey="heures"
              type="natural"
              fill="var(--color-heures)"
              fillOpacity={0.4}
              stroke="var(--color-heures)"
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 font-medium leading-none">
              Tendance à la hausse de 5,2% cette semaine{" "}
              <TrendingUp className="h-4 w-4" />
            </div>
            <div className="flex items-center gap-2 leading-none text-muted-foreground">
              Aperçu Hebdomadaire
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
