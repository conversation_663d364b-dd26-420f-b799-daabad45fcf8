// actions/user-role.ts
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { getDashboardRoute } from "@/lib/role-utils";

const DashboardRedirect = async () => {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return "/";
  }

  const userRole = session.user.role;
  return getDashboardRoute(userRole);
};

export default DashboardRedirect;
