"use client";

import { useState } from "react";
import axios from "axios";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import { ImageIcon, Trash2, Edit } from "lucide-react";
import Image from "next/image";
import { Course } from "@prisma/client";
import { FileUpload } from "@/components/shared/file-upload";
import { Button } from "@/components/ui/button";

interface ImageFormProps {
  initialData: Course;
  courseId: string;
}

export const ImageForm = ({ initialData, courseId }: ImageFormProps) => {
  const router = useRouter();
  const { toast } = useToast();
  const [imageUrl, setImageUrl] = useState(initialData.imageUrl || "");
  const [isEditing, setIsEditing] = useState(!imageUrl);

  const handleImageUpload = async (url: string) => {
    try {
      await axios.patch(`/api/courses/${courseId}`, { imageUrl: url });
      toast({
        title: "Image mise à jour",
        description: "L'image du cours a été modifiée avec succès",
      });
      setImageUrl(url);
      setIsEditing(false);
      router.refresh();
    } catch {
      toast({
        title: "Erreur",
        description:
          "Une erreur est survenue lors de la mise à jour de l'image",
        variant: "destructive",
      });
    }
  };

  const handleDeleteImage = async () => {
    try {
      await axios.patch(`/api/courses/${courseId}`, { imageUrl: "" });
      toast({
        title: "Image supprimée",
        description: "L'image du cours a été retirée",
      });
      setImageUrl("");
      setIsEditing(true);
      router.refresh();
    } catch {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la suppression",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="w-full">
      {imageUrl && !isEditing ? (
        <div className="space-y-4">
          <div className="relative aspect-video bg-slate-100 dark:bg-slate-800 rounded-md overflow-hidden">
            <Image
              alt="Image du cours"
              fill
              className="object-cover"
              src={imageUrl}
              priority
            />
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setIsEditing(true)}
              className="flex items-center gap-2"
            >
              <Edit className="h-4 w-4" />
              Modifier l'image
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteImage}
              className="flex items-center gap-2"
            >
              <Trash2 className="h-4 w-4" />
              Supprimer
            </Button>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 dark:border-gray-600 h-60 rounded-md">
          <FileUpload
            endpoint="courseImage"
            onChange={(file) => {
              if (file) {
                handleImageUpload(file.url);
              }
            }}
          />
          <p className="text-sm text-muted-foreground mt-4">
            Téléchargez une image pour votre cours
          </p>
        </div>
      )}
    </div>
  );
};
