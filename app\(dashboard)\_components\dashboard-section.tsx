import { IconBadge } from "@/components/shared/icon-badge";
import { LucideIcon } from "lucide-react";

interface DashboardSectionProps {
  icon: LucideIcon;
  title: string;
  description: string;
}

export const DashboardSection = ({
  icon,
  title,
  description,
}: DashboardSectionProps) => {
  return (
    <div className="flex items-center gap-x-4">
      <IconBadge icon={icon} />
      <div>
        <h2 className="big-text">{title}</h2>
        <p className="desc-text">{description}</p>
      </div>
    </div>
  );
};
