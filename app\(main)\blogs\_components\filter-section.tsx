import { CategoryFilter } from "@/components/shared/category-filter";
import { SearchInput } from "@/components/shared/search-input";
import { db } from "@/lib/db";
import React from "react";

const FilterSection = async () => {
  const categories = await db.category.findMany({
    where: {
      type: "BLOG",
    },
    orderBy: {
      name: "asc",
    },
    include: {
      blogs: true,
    },
  });

  return (
    <div className="w-[280px] max-lg:w-full flex flex-col space-y-4">
      <div className="flex flex-col">
        <p className="filter-title">Filtrer les blogs</p>
        <p className="filter-description">
          Affinez votre sélection et sauvegardez vos filtres
        </p>
      </div>
      <SearchInput />
      <CategoryFilter categories={categories} />
    </div>
  );
};

export default FilterSection;
