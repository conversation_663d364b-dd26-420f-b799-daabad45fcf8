// "use client";

// import { useState, useEffect } from "react";
// import CommentItem from "./comment-item";
// import { useCurrentUser } from "@/hooks/use-current-user";
// import axios from "axios";

// const Comments = ({ blogId }: { blogId: string }) => {
//   const [comments, setComments] = useState<Comment[]>([]);
//   const user = useCurrentUser();

//   const fetchComments = async () => {
//     const response = await axios.get(`/api/blogs/${blogId}/comment`);
//     setComments(response.data);
//   };

//   useEffect(() => {
//     fetchComments();
//   }, [blogId]);

//   const handleDelete = (commentId: string) => {
//     const response = fetch(`/api/blogs/${blogId}/comment/${commentId}`);

//     console.log("Deleting comment:", commentId);
//   };

//   const handleReply = (parentId: string, content: string) => {
//     console.log("Replying to comment:", parentId, content);
//   };

//   return (
//     <div className="w-full space-y-6">
//       <h2 className="text-xl font-bold">Comments</h2>
//       {comments.length === 0 ? (
//         <p>No comments yet. Be the first to comment!</p>
//       ) : (
//         comments.map((comment) => (
//           <CommentItem
//             key={comment.id}
//             comment={comment}
//             blogId={blogId}
//             currentUserId={user?.id}
//             onDelete={handleDelete}
//             onReply={handleReply}
//           />
//         ))
//       )}
//     </div>
//   );
// };

// export default Comments;
