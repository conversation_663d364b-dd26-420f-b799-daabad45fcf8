import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Command } from "cmdk";

interface CommandOption {
  icon: string;
  label: string;
  description: string;
  shortcut?: string;
  action: () => void;
}

interface CommandMenuProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (command: string) => void;
  position: { top: number; left: number };
}

const commandOptions: CommandOption[] = [
  {
    icon: "H1",
    label: "Heading 1",
    description: "Top-level heading",
    shortcut: "# ",
    action: () => {},
  },
  {
    icon: "H2",
    label: "Heading 2",
    description: "Second-level heading",
    shortcut: "## ",
    action: () => {},
  },
  {
    icon: "H3",
    label: "Heading 3",
    description: "Third-level heading",
    shortcut: "### ",
    action: () => {},
  },
  {
    icon: "•",
    label: "Bullet List",
    description: "Create a bullet list",
    shortcut: "- ",
    action: () => {},
  },
  {
    icon: "1.",
    label: "Numbered List",
    description: "Create a numbered list",
    shortcut: "1. ",
    action: () => {},
  },
  {
    icon: "[]",
    label: "Task List",
    description: "Create a task list",
    shortcut: "- [ ] ",
    action: () => {},
  },
  {
    icon: "∑",
    label: "LaTeX",
    description: "Insert math equation",
    shortcut: "$$ ",
    action: () => {},
  },
  {
    icon: "</>",
    label: "Code Block",
    description: "Insert code block",
    shortcut: "```",
    action: () => {},
  },
];

const CommandMenu: React.FC<CommandMenuProps> = ({
  isOpen,
  onClose,
  onSelect,
  position,
}) => {
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.15 }}
          className="fixed z-50 w-[300px] rounded-lg border border-gray-200 bg-white shadow-lg"
          style={{
            top: `${position.top}px`,
            left: `${position.left}px`,
          }}
        >
          <Command className="rounded-lg border shadow-md">
            <Command.Input placeholder="Type a command..." className="h-9" />
            <Command.List>
              <Command.Empty>No results found.</Command.Empty>
              {commandOptions.map((option) => (
                <Command.Item
                  key={option.label}
                  onSelect={() => {
                    onSelect(option.shortcut || "");
                    onClose();
                  }}
                  className="flex items-center px-2 py-1.5 hover:bg-gray-100 cursor-pointer"
                >
                  <span className="mr-2 text-sm font-mono">{option.icon}</span>
                  <div className="flex-1">
                    <p className="text-sm font-medium">{option.label}</p>
                    <p className="text-xs text-gray-500">
                      {option.description}
                    </p>
                  </div>
                  {option.shortcut && (
                    <kbd className="ml-auto text-xs text-gray-400">
                      {option.shortcut}
                    </kbd>
                  )}
                </Command.Item>
              ))}
            </Command.List>
          </Command>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default CommandMenu;
