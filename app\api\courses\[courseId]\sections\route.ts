import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/lib/db";

// Create a Section
export async function POST(
  req: Request,
  { params }: { params: { courseId: string } }
) {
  try {
    const session = await auth();
    const userId = session?.user.id;

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { title, description, position } = await req.json();

    const section = await db.section.create({
      data: {
        title,
        description,
        position,
        courseId: params.courseId,
      },
    });

    return NextResponse.json(section);
  } catch (error) {
    console.log("[SECTION_CREATE_ERROR]: ", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}
