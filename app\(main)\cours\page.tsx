import { getCourses } from "@/actions/get-courses";

import { auth } from "@/auth";
import { CoursesList } from "@/components/shared/courses-list";
import Title from "@/components/shared/Title";
import { db } from "@/lib/db";
import { redirect } from "next/navigation";
import React from "react";

interface SearchPageProps {
  searchParams: {
    title: string;
    categoryId: string;
  };
}

const page = async ({ searchParams }: SearchPageProps) => {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return redirect("/");
  }

  const categories = await db.category.findMany({
    orderBy: {
      name: "asc",
    },
  });

  const courses = await getCourses();

  return (
    <div className="h-full w-full z-20 flex flex-col items-center">
      <Title
        title="Cours d'ALEPHNULL"
        label="Ressources"
        description="This is the courses page where ill be sharing my courses in from of cards for students by teachers of alephnull"
      />
      <div className="flex my-12">
        <CoursesList items={courses} />
      </div>
    </div>
  );
};

export default page;
