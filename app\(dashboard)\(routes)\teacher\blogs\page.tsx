import React from "react";

import { auth } from "@/auth";
import { db } from "@/lib/db";
import { redirect } from "next/navigation";

import { columns } from "./_components/columns";
import { DataTable } from "./_components/data-table";
import { Spotlight } from "@/components/ui/spotlight";
import BreadCrumb from "@/components/shared/breadcrumb";
import DashboardPageTitle from "@/app/(dashboard)/_components/dashboard-page-title";

const BlogsPage = async () => {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return redirect("/");
  }

  const blogs = await db.blog.findMany({
    where: {
      userId,
    },
    orderBy: {
      createdAt: "desc",
    },
    include: {
      categories: true,
    },
  });

  return (
    <div className="w-full z-20 flex flex-col">
      <BreadCrumb />
      <DashboardPageTitle
        title="Mes Blogs"
        description="Voici la liste de tes blogs"
      />
      <DataTable columns={columns} data={blogs} />
    </div>
  );
};

export default BlogsPage;
