import { db } from "@/lib/db";
import React from "react";
import { Categories } from "./_components/categories";
import { SearchInput } from "@/components/shared/search-input";
import { getCourses } from "@/actions/get-courses";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { CoursesList } from "@/components/shared/courses-list";
import DashboardTitle from "@/app/(dashboard)/_components/dashboard-page-title";
import BreadCrumb from "@/components/shared/breadcrumb";
import { Spotlight } from "@/components/ui/spotlight";
import DashboardPageTitle from "@/app/(dashboard)/_components/dashboard-page-title";
import { Separator } from "@/components/ui/separator";

interface SearchPageProps {
  searchParams: {
    title: string;
    categoryId: string;
  };
}

const SearchPage = async ({ searchParams }: SearchPageProps) => {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return redirect("/");
  }

  const categories = await db.category.findMany({
    orderBy: {
      name: "asc",
    },
  });

  const courses = await getCourses();

  return (
    <div className="w-full z-20 flex flex-col">
      <BreadCrumb />
      <DashboardPageTitle
        title="Explore Courses"
        description="Discover and enroll in courses tailored to your interests and career goals."
      />
      <Separator />
      <Categories items={categories} />
      <CoursesList items={courses} />
    </div>
  );
};

export default SearchPage;
