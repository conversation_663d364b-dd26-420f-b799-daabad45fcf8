import React from "react";
import {
  Video,
  FileText,
  Download,
  Award,
  Clock,
  Infinity,
} from "lucide-react";

interface CourseIncludesProps {
  items: string[];
}

const CourseIncludes = ({ items }: CourseIncludesProps) => {
  // Map of possible icons to use based on content
  const getIconForItem = (item: string) => {
    if (item.toLowerCase().includes("video")) return Video;
    if (
      item.toLowerCase().includes("article") ||
      item.toLowerCase().includes("reading")
    )
      return FileText;
    if (item.toLowerCase().includes("download")) return Download;
    if (item.toLowerCase().includes("certificate")) return Award;
    if (
      item.toLowerCase().includes("hour") ||
      item.toLowerCase().includes("time")
    )
      return Clock;
    if (
      item.toLowerCase().includes("lifetime") ||
      item.toLowerCase().includes("access")
    )
      return Infinity;
    return FileText; // Default
  };

  return (
    <section className="py-14">
      <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-300 mb-6">
        This course includes
      </h1>
      <div className="course-container">
        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {items.map((item, index) => {
            const IconComponent = getIconForItem(item);

            return (
              <div
                key={index}
                className="glass-card p-6 flex items-center space-x-4 hover:shadow-md transition-all duration-300 group border border-color rounded-xl"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="flex-shrink-0 w-12 h-12 bg-course-light-blue rounded-lg flex items-center justify-center group-hover:bg-course-blue transition-colors duration-300">
                  <IconComponent className="h-6 w-6 text-course-blue group-hover:text-white transition-colors duration-300" />
                </div>
                <p className="text-course-dark font-medium">{item}</p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default CourseIncludes;
