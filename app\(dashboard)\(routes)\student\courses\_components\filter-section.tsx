"use client";

import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Search, Filter, X, SortAsc } from "lucide-react";

interface FilterSectionProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
  selectedStatus: string;
  onStatusChange: (status: string) => void;
  selectedInstructor: string;
  onInstructorChange: (instructor: string) => void;
  sortBy: string;
  onSortChange: (sort: string) => void;
  categories: string[];
  instructors: string[];
  onClearFilters: () => void;
}

export default function FilterSection({
  searchQuery,
  onSearch<PERSON>hange,
  selected<PERSON>ategory,
  onCategoryChange,
  selectedStatus,
  onStatus<PERSON>hange,
  selectedInstructor,
  onInstructorChange,
  sortBy,
  onSortChange,
  categories,
  instructors,
  onClearFilters,
}: FilterSectionProps) {
  const hasActiveFilters = selectedCategory || selectedStatus || selectedInstructor || searchQuery;

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <Filter className="w-5 h-5 text-orange-500" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Filtres & Recherche
          </h3>
          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearFilters}
              className="ml-auto text-gray-500 hover:text-orange-500"
            >
              <X className="w-4 h-4 mr-1" />
              Effacer tout
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Rechercher un cours..."
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Category Filter */}
          <Select value={selectedCategory || "all"} onValueChange={(value) => onCategoryChange(value === "all" ? "" : value)}>
            <SelectTrigger>
              <SelectValue placeholder="Toutes catégories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Toutes catégories</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Status Filter */}
          <Select value={selectedStatus || "all"} onValueChange={(value) => onStatusChange(value === "all" ? "" : value)}>
            <SelectTrigger>
              <SelectValue placeholder="Tous statuts" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tous statuts</SelectItem>
              <SelectItem value="in-progress">En cours</SelectItem>
              <SelectItem value="completed">Terminés</SelectItem>
              <SelectItem value="not-started">Non commencés</SelectItem>
            </SelectContent>
          </Select>

          {/* Instructor Filter */}
          <Select value={selectedInstructor || "all"} onValueChange={(value) => onInstructorChange(value === "all" ? "" : value)}>
            <SelectTrigger>
              <SelectValue placeholder="Tous instructeurs" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tous instructeurs</SelectItem>
              {instructors.map((instructor) => (
                <SelectItem key={instructor} value={instructor}>
                  {instructor}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Sort */}
          <Select value={sortBy} onValueChange={onSortChange}>
            <SelectTrigger>
              <SelectValue placeholder="Trier par" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="recent">Plus récents</SelectItem>
              <SelectItem value="progress">Progression</SelectItem>
              <SelectItem value="alphabetical">Alphabétique</SelectItem>
              <SelectItem value="completion">Taux de completion</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Active Filters Display */}
        {hasActiveFilters && (
          <div className="flex flex-wrap gap-2 mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <span className="text-sm text-gray-600 dark:text-gray-400 self-center">
              Filtres actifs:
            </span>
            {searchQuery && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Recherche: "{searchQuery}"
                <X
                  className="w-3 h-3 cursor-pointer hover:text-red-500"
                  onClick={() => onSearchChange("")}
                />
              </Badge>
            )}
            {selectedCategory && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Catégorie: {selectedCategory}
                <X
                  className="w-3 h-3 cursor-pointer hover:text-red-500"
                  onClick={() => onCategoryChange("")}
                />
              </Badge>
            )}
            {selectedStatus && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Statut: {selectedStatus === "in-progress" ? "En cours" : selectedStatus === "completed" ? "Terminés" : "Non commencés"}
                <X
                  className="w-3 h-3 cursor-pointer hover:text-red-500"
                  onClick={() => onStatusChange("")}
                />
              </Badge>
            )}
            {selectedInstructor && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Instructeur: {selectedInstructor}
                <X
                  className="w-3 h-3 cursor-pointer hover:text-red-500"
                  onClick={() => onInstructorChange("")}
                />
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
