import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/lib/db";

export async function PATCH(
  req: Request,
  { params }: { params: { courseId: string; sectionId: string } }
) {
  try {
    const session = await auth();
    const userId = session?.user.id;

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const ownCourse = await db.course.findUnique({
      where: {
        id: params.courseId,
        userId,
      },
    });

    if (!ownCourse) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Check if at least one chapter is published
    const section = await db.section.findUnique({
      where: {
        id: params.sectionId,
        courseId: params.courseId,
      },
      include: {
        chapters: {
          where: {
            isPublished: true,
          },
        },
      },
    });

    if (!section) {
      return new NextResponse("Section not found", { status: 404 });
    }

    if (section.chapters.length === 0) {
      return new NextResponse("Publish at least one chapter first", {
        status: 400,
      });
    }

    // If at least one chapter is published, publish the section
    const publishedSection = await db.section.update({
      where: {
        id: params.sectionId,
      },
      data: {
        isPublished: true,
      },
    });

    return NextResponse.json(publishedSection);
  } catch (error) {
    console.log("[SECTION_PUBLISH]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}
