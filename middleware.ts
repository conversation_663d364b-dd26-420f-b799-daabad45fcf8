import NextAuth from "next-auth";
import authConfig from "@/auth.config";
import {
  DEFAULT_LOGIN_REDIRECT,
  apiAuthPrefix,
  authRoutes,
  publicRoutes,
  roleBasedRoutes,
} from "@/routes";
import { NextResponse } from "next/server";
import { canAccessRoute, getUnauthorizedRedirect } from "@/lib/role-utils";

const { auth } = NextAuth(authConfig);

export default auth((req: any): void | Response | Promise<void | Response> => {
  const { nextUrl } = req;
  const isLoggedIn = !!req.auth;
  const userRole = req.auth?.user?.role;

  const isApiAuthRoute = nextUrl.pathname.startsWith(apiAuthPrefix);
  const isPublicRoute = publicRoutes.includes(nextUrl.pathname);
  const isAuthRoute = authRoutes.includes(nextUrl.pathname);

  if (isApiAuthRoute) {
    return;
  }

  if (isAuthRoute) {
    if (isLoggedIn) {
      return NextResponse.redirect(new URL(DEFAULT_LOGIN_REDIRECT, nextUrl));
    }
    return;
  }

  if (!isLoggedIn && !isPublicRoute) {
    return NextResponse.redirect(new URL("/sign-in", nextUrl));
  }

  // Role-based route protection
  if (isLoggedIn && userRole) {
    const pathname = nextUrl.pathname;

    // Check if user can access the current route
    if (!canAccessRoute(userRole, pathname)) {
      const redirectUrl = getUnauthorizedRedirect(userRole);
      return NextResponse.redirect(new URL(redirectUrl, nextUrl));
    }
  }

  return;
});

export const config = {
  matcher: ["/((?!.+\\.[\\w]+$|_next).*)", "/", "/(api|trpc)(.*)"],
};
