// "use client";

// import { useState } from "react";
// import axios from "axios";
// import { useCurrentUser } from "@/hooks/use-current-user";
// import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
// import { Button } from "@/components/ui/button";
// import { Textarea } from "@/components/ui/textarea";

// const CommentForm = ({ blogId }: { blogId: string }) => {
//   const [content, setContent] = useState("");
//   const user = useCurrentUser();

//   const handleSubmit = asynAc (e: React.FormEvent) => {
//     e.preventDefault();
//     try {
//       await axios.post(`/api/blogs/${blogId}/comment`, {
//         content,
//       });
//       setContent("");
//     } catch (error) {
//       console.error("Error posting comment:", error);
//     }
//   };

//   return (
//     <div className="w-full flex flex-col mt-4 items-start">
//       <div className="mb-4 text-xl font-semibold">Commentaires</div>
//       <form onSubmit={handleSubmit} className="w-full flex gap-4">
//         <Avatar className="w-12 h-12">
//           <AvatarImage
//             src={user?.image || ""}
//             alt={user?.firstName || ""}
//             className="w-full rounded-full object-cover"
//           />
//           <AvatarFallback>{user?.firstName?.charAt(0) || ""}</AvatarFallback>
//         </Avatar>
//         <div className="w-full flex flex-col items-end space-y-4">
//           <Textarea
//             value={content}
//             onChange={(e) => setContent(e.target.value)}
//             placeholder="Write a comment..."
//             required
//           />
//           <Button type="submit" variant={"default"}>
//             Commenter
//           </Button>
//         </div>
//       </form>
//     </div>
//   );
// };

// export default CommentForm;
