"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { 
  Brain, 
  CheckCircle, 
  XCircle, 
  Lightbulb, 
  Clock, 
  Target,
  Zap,
  HelpCircle,
  ArrowRight
} from "lucide-react";

interface Exercise {
  id: string;
  title: string;
  difficulty: "facile" | "moyen" | "difficile";
  topic: string;
  estimatedTime: string;
  completed: boolean;
  score?: number;
  attempts: number;
  maxAttempts: number;
  hasAiHint: boolean;
  description: string;
}

interface InteractiveExercisesProps {
  exercises: Exercise[];
}

const InteractiveExercises = ({ exercises }: InteractiveExercisesProps) => {
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>("all");
  const [selectedTopic, setSelectedTopic] = useState<string>("all");

  // Dummy data
  const dummyExercises: Exercise[] = [
    {
      id: "ex-1",
      title: "Résolution d'équations du premier degré",
      difficulty: "facile",
      topic: "Algèbre",
      estimatedTime: "10 min",
      completed: true,
      score: 95,
      attempts: 1,
      maxAttempts: 3,
      hasAiHint: true,
      description: "Résolvez des équations linéaires avec l'aide de l'IA"
    },
    {
      id: "ex-2",
      title: "Systèmes d'équations à deux inconnues",
      difficulty: "moyen",
      topic: "Algèbre",
      estimatedTime: "15 min",
      completed: true,
      score: 87,
      attempts: 2,
      maxAttempts: 3,
      hasAiHint: true,
      description: "Maîtrisez les méthodes de substitution et d'élimination"
    },
    {
      id: "ex-3",
      title: "Fonctions quadratiques et paraboles",
      difficulty: "difficile",
      topic: "Fonctions",
      estimatedTime: "20 min",
      completed: false,
      attempts: 1,
      maxAttempts: 3,
      hasAiHint: true,
      description: "Analysez les propriétés des fonctions du second degré"
    },
    {
      id: "ex-4",
      title: "Calcul d'aires et périmètres",
      difficulty: "facile",
      topic: "Géométrie",
      estimatedTime: "12 min",
      completed: false,
      attempts: 0,
      maxAttempts: 3,
      hasAiHint: true,
      description: "Calculez les aires et périmètres de figures géométriques"
    },
    {
      id: "ex-5",
      title: "Théorème de Pythagore - Applications",
      difficulty: "moyen",
      topic: "Géométrie",
      estimatedTime: "18 min",
      completed: false,
      attempts: 0,
      maxAttempts: 3,
      hasAiHint: true,
      description: "Appliquez le théorème de Pythagore dans des situations concrètes"
    },
    {
      id: "ex-6",
      title: "Dérivées et tangentes",
      difficulty: "difficile",
      topic: "Calcul",
      estimatedTime: "25 min",
      completed: false,
      attempts: 0,
      maxAttempts: 3,
      hasAiHint: true,
      description: "Calculez des dérivées et trouvez les équations de tangentes"
    }
  ];

  const displayExercises = exercises.length > 0 ? exercises : dummyExercises;

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "facile": return "bg-green-100 text-green-800 dark:bg-green-950/20 dark:text-green-400";
      case "moyen": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-950/20 dark:text-yellow-400";
      case "difficile": return "bg-red-100 text-red-800 dark:bg-red-950/20 dark:text-red-400";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-950/20 dark:text-gray-400";
    }
  };

  const getTopicColor = (topic: string) => {
    switch (topic) {
      case "Algèbre": return "bg-blue-100 text-blue-800 dark:bg-blue-950/20 dark:text-blue-400";
      case "Géométrie": return "bg-purple-100 text-purple-800 dark:bg-purple-950/20 dark:text-purple-400";
      case "Fonctions": return "bg-orange-100 text-orange-800 dark:bg-orange-950/20 dark:text-orange-400";
      case "Calcul": return "bg-pink-100 text-pink-800 dark:bg-pink-950/20 dark:text-pink-400";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-950/20 dark:text-gray-400";
    }
  };

  const filteredExercises = displayExercises.filter(exercise => {
    const matchesDifficulty = selectedDifficulty === "all" || exercise.difficulty === selectedDifficulty;
    const matchesTopic = selectedTopic === "all" || exercise.topic === selectedTopic;
    return matchesDifficulty && matchesTopic;
  });

  const completedCount = displayExercises.filter(ex => ex.completed).length;
  const totalCount = displayExercises.length;
  const averageScore = displayExercises
    .filter(ex => ex.score)
    .reduce((acc, ex) => acc + (ex.score || 0), 0) / displayExercises.filter(ex => ex.score).length || 0;

  const topics = [...new Set(displayExercises.map(ex => ex.topic))];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="w-5 h-5 text-orange-500" />
          Exercices Interactifs avec IA
        </CardTitle>
        
        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
          <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Target className="w-5 h-5 text-blue-600" />
              <span className="font-medium text-blue-900 dark:text-blue-100">Progression</span>
            </div>
            <div className="text-2xl font-bold text-blue-600 mb-1">
              {completedCount}/{totalCount}
            </div>
            <Progress value={(completedCount / totalCount) * 100} className="h-2" />
          </div>
          
          <div className="bg-green-50 dark:bg-green-950/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <span className="font-medium text-green-900 dark:text-green-100">Score Moyen</span>
            </div>
            <div className="text-2xl font-bold text-green-600">
              {averageScore > 0 ? `${Math.round(averageScore)}%` : "N/A"}
            </div>
          </div>
          
          <div className="bg-orange-50 dark:bg-orange-950/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Zap className="w-5 h-5 text-orange-600" />
              <span className="font-medium text-orange-900 dark:text-orange-100">IA Disponible</span>
            </div>
            <div className="text-2xl font-bold text-orange-600">
              {displayExercises.filter(ex => ex.hasAiHint).length}
            </div>
          </div>
        </div>
        
        {/* Filters */}
        <div className="flex flex-wrap gap-2 mt-4">
          <div className="flex gap-2">
            <Button
              variant={selectedDifficulty === "all" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedDifficulty("all")}
            >
              Toutes difficultés
            </Button>
            <Button
              variant={selectedDifficulty === "facile" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedDifficulty("facile")}
            >
              Facile
            </Button>
            <Button
              variant={selectedDifficulty === "moyen" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedDifficulty("moyen")}
            >
              Moyen
            </Button>
            <Button
              variant={selectedDifficulty === "difficile" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedDifficulty("difficile")}
            >
              Difficile
            </Button>
          </div>
          
          <div className="flex gap-2">
            <Button
              variant={selectedTopic === "all" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedTopic("all")}
            >
              Tous sujets
            </Button>
            {topics.map(topic => (
              <Button
                key={topic}
                variant={selectedTopic === topic ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedTopic(topic)}
              >
                {topic}
              </Button>
            ))}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {filteredExercises.map((exercise) => (
            <Card key={exercise.id} className="hover:shadow-lg transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg text-gray-900 dark:text-white mb-2">
                      {exercise.title}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                      {exercise.description}
                    </p>
                  </div>
                  
                  {exercise.completed && (
                    <div className="flex-shrink-0 ml-2">
                      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                        <CheckCircle className="w-5 h-5 text-white" />
                      </div>
                    </div>
                  )}
                </div>
                
                <div className="flex flex-wrap gap-2 mb-4">
                  <Badge variant="outline" className={getDifficultyColor(exercise.difficulty)}>
                    {exercise.difficulty}
                  </Badge>
                  <Badge variant="outline" className={getTopicColor(exercise.topic)}>
                    {exercise.topic}
                  </Badge>
                  {exercise.hasAiHint && (
                    <Badge variant="outline" className="bg-purple-100 text-purple-800 dark:bg-purple-950/20 dark:text-purple-400">
                      <Lightbulb className="w-3 h-3 mr-1" />
                      IA
                    </Badge>
                  )}
                </div>
                
                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      <span>{exercise.estimatedTime}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <HelpCircle className="w-4 h-4" />
                      <span>{exercise.attempts}/{exercise.maxAttempts} tentatives</span>
                    </div>
                  </div>
                  
                  {exercise.completed && exercise.score && (
                    <div className="font-medium text-green-600">
                      Score: {exercise.score}%
                    </div>
                  )}
                </div>
                
                <div className="flex gap-2">
                  <Button 
                    className="flex-1" 
                    variant={exercise.completed ? "outline" : "default"}
                    disabled={exercise.attempts >= exercise.maxAttempts && !exercise.completed}
                  >
                    {exercise.completed ? "Refaire" : exercise.attempts > 0 ? "Continuer" : "Commencer"}
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                  
                  {exercise.hasAiHint && (
                    <Button variant="outline" size="sm">
                      <Lightbulb className="w-4 h-4" />
                    </Button>
                  )}
                </div>
                
                {exercise.attempts >= exercise.maxAttempts && !exercise.completed && (
                  <div className="mt-3 p-3 bg-red-50 dark:bg-red-950/20 rounded-lg">
                    <div className="flex items-center gap-2 text-red-600 text-sm">
                      <XCircle className="w-4 h-4" />
                      <span>Nombre maximum de tentatives atteint</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
        
        {filteredExercises.length === 0 && (
          <div className="text-center py-12">
            <Brain className="w-16 h-16 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Aucun exercice trouvé
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Essayez de modifier vos filtres pour voir plus d'exercices.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default InteractiveExercises;
