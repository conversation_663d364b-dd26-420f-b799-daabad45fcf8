import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/lib/db";

export async function PATCH(
  req: Request,
  { params }: { params: { courseId: string; chapterId: string } }
) {
  try {
    const session = await auth();
    const userId = session?.user.id;

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Verify that the course belongs to the user
    const ownCourse = await db.course.findUnique({
      where: {
        id: params.courseId,
        userId,
      },
    });

    if (!ownCourse) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Unpublish the chapter
    const unpublishedChapter = await db.chapter.update({
      where: {
        id: params.chapterId,
        section: {
          courseId: params.courseId,
        },
      },
      data: {
        isPublished: false,
      },
    });

    // Check if there are any published chapters left in the course
    const publishedChaptersInCourse = await db.chapter.findMany({
      where: {
        section: {
          courseId: params.courseId,
        },
        isPublished: true,
      },
    });

    // If no published chapters remain, unpublish the course
    if (publishedChaptersInCourse.length === 0) {
      await db.course.update({
        where: {
          id: params.courseId,
        },
        data: {
          isPublished: false,
        },
      });
    }

    return NextResponse.json(unpublishedChapter);
  } catch (error) {
    console.log("[CHAPTER_UNPUBLISH_ERROR]: ", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}
