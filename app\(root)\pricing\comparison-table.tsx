import React from "react";

const plans = [
  {
    name: "Free",
    price: "Free forever",
    seats: "1",
    storage: "15 GB",
    emailSharing: true,
    access: false,
  },
  {
    name: "Startup",
    price: "$39 per month billed annually",
    seats: "Up to 3",
    storage: "1 TB",
    emailSharing: true,
    access: true,
  },
  {
    name: "Team",
    price: "$89 per month billed annually",
    seats: "Up to 10",
    storage: "15 TB",
    emailSharing: true,
    access: true,
  },
  {
    name: "Enterprise",
    price: "$149 per month billed annually",
    seats: "Unlimited",
    storage: "Unlimited",
    emailSharing: true,
    access: true,
  },
];

const features = [
  { name: "Number of seats", key: "seats" },
  { name: "Storage", key: "storage" },
  { name: "Email sharing", key: "emailSharing", icon: true },
  { name: "Any time, anywhere access", key: "access", icon: true },
  // Add more features here if needed
];

const getIcon = (feature: any, plan: any) => {
  if (feature.icon) {
    return plan[feature.key] ? (
      <svg
        className="shrink-0 size-5 text-orange-600 dark:text-orange-500"
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <polyline points="20 6 9 17 4 12" />
      </svg>
    ) : (
      <svg
        className="shrink-0 size-5 text-gray-400 dark:text-neutral-600"
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M5 12h14" />
      </svg>
    );
  }
  return (
    <span className="text-sm text-gray-800 dark:text-neutral-200">
      {plan[feature.key]}
    </span>
  );
};

const ComparisonTable = () => (
  <div className="relative">
    <div className="max-w-[85rem] px-4 py-10 sm:px-6 lg:px-8 md:py-14 lg:py-20 mx-auto">
      <div className="max-w-2xl mx-auto text-center mb-10 lg:mb-14">
        <h2 className="text-2xl font-bold md:text-3xl md:leading-tight dark:text-white">
          Compare plans
        </h2>
      </div>

      <div className="hidden lg:block sticky top-0 start-0 py-2 bg-white/60 backdrop-blur-md dark:bg-neutral-900/60">
        <div className="grid grid-cols-6 gap-6">
          <div className="col-span-2">
            <span className="font-semibold text-lg text-gray-800 dark:text-neutral-200">
              Features
            </span>
          </div>
          {plans.map((plan) => (
            <div key={plan.name} className="col-span-1">
              <span className="font-semibold text-lg text-gray-800 dark:text-neutral-200">
                {plan.name}
              </span>
              <p className="mt-2 text-sm text-gray-500 dark:text-neutral-500">
                {plan.price}
              </p>
            </div>
          ))}
        </div>
      </div>

      <div className="space-y-4 lg:space-y-0">
        {features.map((feature) => (
          <ul key={feature.name} className="grid lg:grid-cols-6 lg:gap-6">
            <li className="lg:col-span-2 pb-1.5 lg:py-3">
              <span className="font-semibold lg:font-normal text-sm text-gray-800 dark:text-neutral-200">
                {feature.name}
              </span>
            </li>
            {plans.map((plan) => (
              <li
                key={plan.name}
                className={`col-span-1 py-1.5 lg:py-3 ${
                  feature.icon
                    ? "border-b border-gray-200 dark:border-neutral-800"
                    : ""
                }`}
              >
                <div className="grid grid-cols-2 md:grid-cols-6 lg:block">
                  <span className="lg:hidden md:col-span-2 text-sm text-gray-800 dark:text-neutral-200">
                    {plan.name}
                  </span>
                  {getIcon(feature, plan)}
                </div>
              </li>
            ))}
          </ul>
        ))}
      </div>
    </div>
  </div>
);

export default ComparisonTable;
