"use client";

import { useForm } from "react-hook-form";
import * as z from "zod";
import axios from "axios";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import Editor from "../../../../../../../lib/editor";

interface ContentFormProps {
  initialData: {
    content: string | null;
  };
  announcementId: string;
}

const formSchema = z.object({
  content: z.string().optional(),
});

export const ContentForm = ({ initialData, announcementId }: ContentFormProps) => {
  const router = useRouter();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      content: initialData.content || "",
    },
  });

  const { isSubmitting, isValid } = form.formState;

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      await axios.patch(`/api/announcements/${announcementId}`, values);
      toast.success("Contenu mis à jour! 📄", {
        description: "Le contenu détaillé de votre annonce a été modifié",
        duration: 3000,
      });
      router.refresh();
    } catch (error) {
      toast.error("Erreur lors de la mise à jour", {
        description: "Impossible de modifier le contenu. Veuillez réessayer.",
        duration: 4000,
      });
    }
  };

  return (
    <div className="w-full">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full flex flex-col gap-4"
        >
          <FormField
            control={form.control}
            name="content"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Editor
                    value={field.value || ""}
                    onChange={field.onChange}
                    placeholder="Rédigez le contenu détaillé de votre annonce..."
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="flex justify-end">
            <Button type="submit" disabled={!isValid || isSubmitting}>
              Enregistrer
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};
