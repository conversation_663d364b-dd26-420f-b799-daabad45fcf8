import React from "react";
import { Award, BookOpen, Users } from "lucide-react";

interface AboutInstructorProps {
  name: string;
  bio: string;
  imageUrl: string;
  coursesCount?: number;
  studentsCount?: number;
  rating?: number;
}

const AboutInstructor = ({
  name,
  bio,
  imageUrl,
  coursesCount = 12,
  studentsCount = 45000,
  rating = 4.9,
}: AboutInstructorProps) => {
  return (
    <section className="py-14">
      <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-300 mb-6">
        About the instructor
      </h1>
      <div className="course-container">
        <div className="glass-card p-6 md:p-8">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="md:w-1/4 flex flex-col items-center">
              <div className="relative w-40 h-40 rounded-full overflow-hidden mb-4 border-4 border-course-light-blue">
                <img
                  src={imageUrl}
                  alt={name}
                  className="w-full h-full object-cover"
                />
              </div>

              <h3 className="text-xl font-semibold text-course-dark mb-1">
                {name}
              </h3>
              <p className="text-course-gray text-sm mb-4">Lead Instructor</p>

              <div className="flex items-center space-x-1 text-yellow-400">
                {[...Array(5)].map((_, i) => (
                  <svg
                    key={i}
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 fill-current"
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
                <span className="text-course-dark font-medium ml-1">
                  {rating}
                </span>
              </div>
            </div>

            <div className="md:w-3/4">
              <p className="text-course-gray mb-6 text-pretty">{bio}</p>

              <div className="grid grid-cols-3 gap-4">
                <div className="flex flex-col items-center p-4 rounded-lg bg-course-light-blue/30">
                  <BookOpen className="h-6 w-6 text-course-blue mb-2" />
                  <span className="text-xl font-bold text-course-dark">
                    {coursesCount}
                  </span>
                  <span className="text-course-gray text-sm">Courses</span>
                </div>

                <div className="flex flex-col items-center p-4 rounded-lg bg-course-light-blue/30">
                  <Users className="h-6 w-6 text-course-blue mb-2" />
                  <span className="text-xl font-bold text-course-dark">
                    {studentsCount.toLocaleString()}
                  </span>
                  <span className="text-course-gray text-sm">Students</span>
                </div>

                <div className="flex flex-col items-center p-4 rounded-lg bg-course-light-blue/30">
                  <Award className="h-6 w-6 text-course-blue mb-2" />
                  <span className="text-xl font-bold text-course-dark">
                    {rating}
                  </span>
                  <span className="text-course-gray text-sm">Rating</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutInstructor;
