"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { toast } from "sonner";
import axios from "axios";

export default function CreateAnnouncementPage() {
  const router = useRouter();

  useEffect(() => {
    const createAnnouncement = async () => {
      try {
        const response = await axios.post("/api/announcements", {
          title: "Nouvelle annonce",
          description: "Description de l'annonce",
          type: "NEWS",
          priority: "MEDIUM",
          targetAudience: "ALL",
          isPublished: false, // Brouillon par défaut
          isPinned: false,
        });

        toast.success("Nouvelle annonce créée! 🎉", {
          description: "Vous pouvez maintenant personnaliser votre annonce",
          duration: 4000,
        });
        router.push(`/admin/announcements/${response.data.id}`);
      } catch {
        toast.error("Erreur lors de la création", {
          description: "Impossible de créer l'annonce. Veuillez réessayer.",
          duration: 4000,
        });
        router.push("/admin/announcements");
      }
    };

    createAnnouncement();
  }, [router]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500 mx-auto"></div>
        <p className="mt-4 text-lg">Création de l'annonce...</p>
      </div>
    </div>
  );
}
