"use client";

import Image from "next/image";

interface UserProps {
  name: string;
  level: string;
  image: string;
  // online: boolean;
}

const User: React.FC<UserProps> = ({ name, level, image }) => {
  return (
    <div className="flex gap-4 items-center">
      <div className="relative">
        <Image
          src={image}
          alt={image}
          width={48}
          height={48}
          className="rounded-3xl border dark:border-orange-400 border-orange-600"
        />
      </div>
      <div className="flex flex-col gap-0.25 items-start">
        <p className="text-lg text-gray-300 font-medium">{name}</p>
        <p className="p-text dark:text-gray-500 text-sm">{level}</p>
      </div>
    </div>
  );
};

export default User;
