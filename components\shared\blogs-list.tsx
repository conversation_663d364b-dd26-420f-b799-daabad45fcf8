import { Category, Blog, User } from "@prisma/client";
import { BlogCard } from "./blog-card";

type BlogWithCategory = Blog & {
  categories: Category[];
  author: User;
};

interface BlogsListProps {
  items: BlogWithCategory[];
}

export const BlogsList = ({ items }: BlogsListProps) => {
  return (
    <>
      <div className="w-full grid sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 2xl:grid-cols-3 gap-12">
        {items.map((item) => (
          <BlogCard
            key={item.id}
            id={item.id}
            title={item.title}
            createdAt={item.createdAt}
            imageUrl={item.image || ""}
            author={item.author}
            description={item.description || "No description available"}
            categories={
              item.categories.length > 0
                ? item.categories.map((category) => category.name)
                : ["Uncategorized"]
            }
          />
        ))}
      </div>
      {items.length === 0 && (
        <div className="w-full text-center text-lg text-muted-foreground mt-10">
          No blogs found
        </div>
      )}
    </>
  );
};
