"use client";

import { useForm } from "react-hook-form";
import * as z from "zod";
import axios from "axios";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";

interface TargetAudienceFormProps {
  initialData: {
    targetAudience: string | null;
  };
  announcementId: string;
}

const formSchema = z.object({
  targetAudience: z.string().min(1, {
    message: "Le public cible est requis",
  }),
});

export const TargetAudienceForm = ({ initialData, announcementId }: TargetAudienceFormProps) => {
  const router = useRouter();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      targetAudience: initialData.targetAudience || "ALL",
    },
  });

  const { isSubmitting, isValid } = form.formState;

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      await axios.patch(`/api/announcements/${announcementId}`, values);
      toast.success("Public cible mis à jour! 👥", {
        description: "Le public cible de votre annonce a été modifié",
        duration: 3000,
      });
      router.refresh();
    } catch (error) {
      toast.error("Erreur lors de la mise à jour", {
        description: "Impossible de modifier le public cible. Veuillez réessayer.",
        duration: 4000,
      });
    }
  };

  return (
    <div className="w-full">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full flex gap-4 items-center max-sm:flex-col max-sm:items-end"
        >
          <FormField
            control={form.control}
            name="targetAudience"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormControl>
                  <Select
                    disabled={isSubmitting}
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Sélectionner le public" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ALL">Tous</SelectItem>
                      <SelectItem value="STUDENTS">Étudiants</SelectItem>
                      <SelectItem value="TEACHERS">Professeurs</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button
            disabled={!isValid || isSubmitting}
            type="submit"
            className="max-sm:w-full"
          >
            Enregistrer
          </Button>
        </form>
      </Form>
    </div>
  );
};
