import { db } from "@/lib/db";

export const getProgress = async (
  userId: string,
  courseId: string
): Promise<number> => {
  try {
    // First get all published chapters in the course
    const publishedChapters = await db.chapter.findMany({
      where: {
        section: {
          courseId: courseId,
        },
        isPublished: true,
      },
      select: {
        id: true,
      },
    });

    if (publishedChapters.length === 0) {
      return 0; // No published chapters means 0% progress
    }

    const publishedChapterIds = publishedChapters.map((chapter) => chapter.id);

    // Count how many chapters the user has completed
    const validCompletedChapters = await db.userProgress.count({
      where: {
        userId: userId,
        chapterId: {
          in: publishedChapterIds,
        },
        isCompleted: true,
      },
    });

    // Calculate progress percentage
    const progressPercentage = Math.round(
      (validCompletedChapters / publishedChapterIds.length) * 100
    );

    return progressPercentage;
  } catch (error) {
    console.error("[GET_PROGRESS_ERROR]", error);
    return 0;
  }
};
