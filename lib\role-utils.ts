import { UserRole } from "@prisma/client";

/**
 * Get the appropriate dashboard route based on user role
 * @param role - The user's role
 * @returns The dashboard route for the user's role
 */
export function getDashboardRoute(role: UserRole | undefined): string {
  switch (role) {
    case "ADMIN":
      return "/admin/home";
    case "TEACHER":
      return "/teacher/home";
    case "USER":
    default:
      return "/student/home";
  }
}

/**
 * Check if a user can access a specific route based on their role
 * @param role - The user's role
 * @param pathname - The route pathname to check
 * @returns Whether the user can access the route
 */
export function canAccessRoute(role: UserRole | undefined, pathname: string): boolean {
  if (!role) return false;

  // Admin can access all routes
  if (role === "ADMIN") return true;

  // Teacher can access teacher and student routes
  if (role === "TEACHER") {
    return pathname.startsWith("/teacher") || pathname.startsWith("/student");
  }

  // Students can only access student routes
  if (role === "USER") {
    return pathname.startsWith("/student");
  }

  return false;
}

/**
 * Get the redirect route for unauthorized access
 * @param role - The user's role
 * @returns The appropriate redirect route
 */
export function getUnauthorizedRedirect(role: UserRole | undefined): string {
  return getDashboardRoute(role);
}
