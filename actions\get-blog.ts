import { Category, Blog, User, Like } from "@prisma/client";
import { db } from "@/lib/db";

type BlogWithCategory = Blog & {
  categories: Category[];
  author: User;
  likes: Like[];
};

export const getBlog = async (
  blogId: string,
  userId?: string
): Promise<BlogWithCategory | null> => {
  try {
    const blog = await db.blog.findUnique({
      where: {
        id: blogId,
      },
      include: {
        categories: true,
        likes: true,
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            username: true,
            image: true,
            bio: true,
            phone: true,
            isTwoFactorEnabled: true,
            role: true,
            isOnboarded: true,
            email: true,
            password: true,
            emailVerified: true,
          },
        },
      },
    });

    if (!blog) {
      return null;
    }

    return {
      ...blog,
      author: blog.user,
    };
  } catch (error) {
    console.log("[GET_BLOG]", error);
    return null;
  }
};
