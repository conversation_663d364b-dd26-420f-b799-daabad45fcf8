import { User } from "@prisma/client";
import { db } from "@/lib/db";

interface UserProps {
  id: string;
}

export const getUser = async ({ id }: UserProps) => {
  try {
    const user = await db.user.findUnique({
      where: {
        id,
      },
    });

    if (!user) {
      console.error("[GET_USER] User not found"); 
      return null;
    }

    return user;
  } catch (error) {
    console.error("[GET_USER]", error);
    return null;
  }
};
