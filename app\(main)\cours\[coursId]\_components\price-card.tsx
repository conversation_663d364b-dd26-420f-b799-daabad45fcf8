import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Check, Clock, Calendar, Shield } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

interface PriceCardProps {
  price: number;
  items: string[];
}

const PriceCard = ({ price, items }: PriceCardProps) => {
  return (
    <div className="glass-card border border-color rounded-xl overflow-hidden shadow-md transition-all duration-300 hover:shadow-xl animate-scale-in">
      <div className="p-6 md:p-8">
        <div className="space-y-6">
          <div>
            <div className="flex items-center mb-1">
              <h2 className="text-3xl font-bold text-course-dark">
                {price} MAD
              </h2>
              <span className="ml-3 text-course-gray line-through text-lg">
                {Math.round(price * 1.33)} MAD
              </span>
            </div>
            <Badge className="bg-green-100 text-green-800">25% off</Badge>
          </div>

          <div className="space-y-3">
            <div className="flex items-center">
              <Clock className="h-5 w-5 text-course-gray mr-3" />
              <span className="text-course-dark">
                5 days left at this price!
              </span>
            </div>
            <div className="flex items-center">
              <Calendar className="h-5 w-5 text-course-gray mr-3" />
              <span className="text-course-dark">Full lifetime access</span>
            </div>
            <div className="flex items-center">
              <Shield className="h-5 w-5 text-course-gray mr-3" />
              <span className="text-course-dark">
                30-day money-back guarantee
              </span>
            </div>
          </div>

          <div className="space-y-3">
            <Button className="w-full" variant={"default"}>
              Enroll Now
            </Button>
            <Button className="w-full" variant={"outline"}>
              Try Free Preview
            </Button>
          </div>

          <Separator />

          <div>
            <h3 className="font-medium text-lg mb-3 text-course-dark">
              This course includes:
            </h3>
            <ul className="space-y-2">
              {items.map((item, index) => {
                return (
                  <li key={index} className="flex items-start">
                    <Check className="h-5 w-5 text-course-green mr-2 mt-0.5 flex-shrink-0" />
                    <p className="text-course-dark font-medium">{item}</p>
                  </li>
                );
              })}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PriceCard;
