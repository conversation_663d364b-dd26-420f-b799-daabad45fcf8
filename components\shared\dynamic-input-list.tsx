import React, { useState } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Pencil, Trash } from "lucide-react";

interface DynamicInputListProps {
  items: string[];
  onAdd: (item: string) => void;
  onEdit: (index: number, newItem: string) => void;
  onDelete: (index: number) => void;
  placeholder?: string;
}

const DynamicInputList: React.FC<DynamicInputListProps> = ({
  items,
  onAdd,
  onEdit,
  onDelete,
  placeholder = "Ajoutez un élément",
}) => {
  const [inputValue, setInputValue] = useState("");
  const [editingIndex, setEditingIndex] = useState<number | null>(null); // Track which item is being edited
  const [editValue, setEditValue] = useState(""); // Store the value being edited

  const handleAdd = () => {
    if (inputValue.trim()) {
      onAdd(inputValue.trim());
      setInputValue("");
    }
  };

  const handleEditClick = (index: number, value: string) => {
    setEditingIndex(index); // Enable edit mode for this item
    setEditValue(value); // Set the current value for editing
  };

  const handleSaveClick = (index: number) => {
    onEdit(index, editValue); // Save the edited value
    setEditingIndex(null); // Exit edit mode
  };

  return (
    <div>
      {/* Input field for adding new items */}
      <div className="flex gap-2 mb-4">
        <Input
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          placeholder={placeholder}
          className="text-sm"
        />
        <Button onClick={handleAdd} variant={"outline"}>
          Ajouter
        </Button>
      </div>

      {/* Display the list of items with edit and delete options */}
      <ul className="space-y-2">
        {items.map((item, index) => (
          <li key={index} className="flex items-center gap-2">
            <Input
              value={editingIndex === index ? editValue : item} // Show edit value if in edit mode
              onChange={(e) => setEditValue(e.target.value)} // Update edit value
              disabled={editingIndex !== index} // Disable input if not in edit mode
              className="text-sm"
            />
            {editingIndex === index ? (
              // Save button when in edit mode
              <Button
                onClick={() => handleSaveClick(index)}
                variant={"default"}
              >
                Enregistrer
              </Button>
            ) : (
              // Edit button when not in edit mode
              <Button
                onClick={() => handleEditClick(index, item)}
                variant={"secondary"}
                size={"icon"}
                className="px-3"
              >
                <Pencil className="w-8 h-8" />
              </Button>
            )}
            <Button
              onClick={() => onDelete(index)}
              variant={"destructive"}
              size={"icon"}
              className="px-3"
            >
              <Trash className="w-8 h-8 " />
            </Button>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default DynamicInputList;
