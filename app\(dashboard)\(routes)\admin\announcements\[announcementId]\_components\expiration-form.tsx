"use client";

import { useForm } from "react-hook-form";
import * as z from "zod";
import axios from "axios";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { Calendar } from "lucide-react";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface ExpirationFormProps {
  initialData: {
    expiresAt: Date | null;
  };
  announcementId: string;
}

const formSchema = z.object({
  expiresAt: z.string().optional(),
});

export const ExpirationForm = ({ initialData, announcementId }: ExpirationFormProps) => {
  const router = useRouter();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      expiresAt: initialData.expiresAt
        ? new Date(initialData.expiresAt).toISOString().split('T')[0]
        : "",
    },
  });

  const { isSubmitting, isValid } = form.formState;

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      const payload = {
        expiresAt: values.expiresAt ? new Date(values.expiresAt).toISOString() : null,
      };
      await axios.patch(`/api/announcements/${announcementId}`, payload);
      toast.success("Date d'expiration mise à jour! 📅", {
        description: values.expiresAt
          ? "Votre annonce expirera à la date définie"
          : "Votre annonce n'a plus de date d'expiration",
        duration: 3000,
      });
      router.refresh();
    } catch (error) {
      toast.error("Erreur lors de la mise à jour", {
        description: "Impossible de modifier la date d'expiration. Veuillez réessayer.",
        duration: 4000,
      });
    }
  };

  return (
    <div className="w-full">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full flex gap-4 items-center max-sm:flex-col max-sm:items-end"
        >
          <FormField
            control={form.control}
            name="expiresAt"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormControl>
                  <div className="relative">
                    <Calendar className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="date"
                      disabled={isSubmitting}
                      className="pl-10 w-full"
                      placeholder="Date d'expiration (optionnel)"
                      {...field}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button
            disabled={!isValid || isSubmitting}
            type="submit"
            className="max-sm:w-full"
          >
            Enregistrer
          </Button>
        </form>
      </Form>
    </div>
  );
};
