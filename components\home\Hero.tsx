"use client";

import React from "react";
import { BackgroundBeams } from "../ui/background-beams";
import { Button } from "../ui/button";
import { Badge } from "../ui/badge";
import { FlipWords } from "../ui/flip-words";
import { useToast } from "@/hooks/use-toast";
import Link from "next/link";

export default function Hero() {
  const words = ["connaissances", "réussite", "inspiration", "excellence"];
  const { toast } = useToast();

  return (
    <main className="h-[100vh] max-md:h-[100%] w-full relative flex flex-col items-start justify-center antialiased p-32 max-lg:py-18 max-lg:px-16 max-md:px-8 max-sm:px-4">

      <div className="flex flex-col items-start justify-around max-md:items-center relative z-10">
        <Badge className="text-sm mb-8 animate-fade-in-up border-orange-500 text-orange-600 dark:text-orange-400 dark:border-orange-400 shadow-lg hover:shadow-xl transition-all duration-300" variant={"outline"}>
          Plateforme d'apprentissage nouvelle génération
        </Badge>

        <div className="text-6xl font-bold max-lg:text-5xl max-md:text-center animate-fade-in-up delay-200">
          Transformez votre parcours <br />
          d'apprentissage avec
          <FlipWords words={words} className="text-orange-500 dark:text-orange-400" />
        </div>

        <p className="p-text w-[60%] mt-12 text-lg text-left max-md:w-full max-md:text-center animate-fade-in-up delay-400 leading-relaxed">
          <span className="font-semibold text-black dark:text-white bg-gradient-to-r from-orange-500 to-orange-600 bg-clip-text text-transparent">
            ALEPHNULL
          </span>{" "}
          révolutionne l'éducation en ligne avec une approche personnalisée et interactive.
          Découvrez des cours de qualité supérieure, dispensés par des experts passionnés,
          et rejoignez une communauté d'apprenants motivés qui transforment leurs ambitions en réalité.
        </p>

        <div className="flex gap-6 mt-12 animate-fade-in-up delay-600 max-md:flex-col max-md:w-full">
          <Link href="/sign-up">
            <Button
              variant="default"
              size="lg"
              className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 px-8 py-6 text-base font-semibold"
            >
              Commencer gratuitement
            </Button>
          </Link>
          <Link href="/cours">
            <Button
              variant="outline"
              size="lg"
              className="border-2 border-orange-500 text-orange-600 hover:bg-orange-50 dark:hover:bg-orange-950 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 px-8 py-6 text-base font-semibold"
            >
              Explorer les cours
            </Button>
          </Link>
        </div>

        {/* Stats section */}
        <div className="flex gap-12 mt-16 animate-fade-in-up delay-800 max-md:grid max-md:grid-cols-2 max-md:gap-8 max-md:w-full">
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-500">1000+</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Étudiants actifs</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-500">50+</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Cours disponibles</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-500">95%</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Taux de satisfaction</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-500">24/7</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Support disponible</div>
          </div>
        </div>
      </div>

      <BackgroundBeams className="opacity-30" />
    </main>
  );
}
