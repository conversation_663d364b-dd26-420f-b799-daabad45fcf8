"use server";

import { auth } from "@/auth";
import { db } from "@/lib/db";
import {
  AnnouncementSchema
} from "@/schemas";
import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import * as z from "zod";

export const createAnnouncement = async (values: z.infer<typeof AnnouncementSchema>) => {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return { error: "Non autorisé" };
    }

    if (session.user.role !== "ADMIN" && session.user.role !== "TEACHER") {
      return { error: "Permissions insuffisantes" };
    }

    const validatedFields = AnnouncementSchema.safeParse(values);

    if (!validatedFields.success) {
      return { error: "Champs invalides!" };
    }

    const {
      title,
      description,
      content,
      type,
      priority,
      isPublished,
      isPinned,
      imageUrl,
      targetAudience,
      expiresAt,
    } = validatedFields.data;

    const announcement = await db.announcement.create({
      data: {
        title,
        description,
        content,
        type,
        priority,
        isPublished,
        isPinned,
        imageUrl,
        targetAudience,
        expiresAt,
        authorId: session.user.id,
        publishedAt: isPublished ? new Date() : null,
      },
    });

    revalidatePath("/admin/announcements");
    revalidatePath("/student/home");

    return { success: "Annonce créée avec succès! 🎉", data: announcement };
  } catch (error) {
    console.error("[CREATE_ANNOUNCEMENT]", error);
    return { error: "Erreur interne du serveur" };
  }
};

export const updateAnnouncement = async (values: z.infer<typeof AnnouncementSchema> & { id: string }) => {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return { error: "Non autorisé" };
    }

    if (session.user.role !== "ADMIN" && session.user.role !== "TEACHER") {
      return { error: "Permissions insuffisantes" };
    }

    const UpdateAnnouncementSchema = AnnouncementSchema.partial().extend({
      id: z.string(),
    });

    const validatedFields = UpdateAnnouncementSchema.safeParse(values);

    if (!validatedFields.success) {
      return { error: "Champs invalides!" };
    }

    const { id, ...updateData } = validatedFields.data;

    // Check if announcement exists and user has permission
    const existingAnnouncement = await db.announcement.findUnique({
      where: { id },
    });

    if (!existingAnnouncement) {
      return { error: "Annonce non trouvée" };
    }

    if (
      existingAnnouncement.authorId !== session.user.id &&
      session.user.role !== "ADMIN"
    ) {
      return { error: "Permissions insuffisantes" };
    }

    const announcement = await db.announcement.update({
      where: { id },
      data: {
        ...updateData,
        publishedAt:
          updateData.isPublished && !existingAnnouncement.publishedAt
            ? new Date()
            : existingAnnouncement.publishedAt,
      },
    });

    revalidatePath("/admin/announcements");
    revalidatePath("/student/home");

    return { success: "Annonce mise à jour avec succès! ✅", data: announcement };
  } catch (error) {
    console.error("[UPDATE_ANNOUNCEMENT]", error);
    return { error: "Erreur interne du serveur" };
  }
};

export const publishAnnouncement = async (id: string, isPublished: boolean) => {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return { error: "Non autorisé" };
    }

    if (session.user.role !== "ADMIN" && session.user.role !== "TEACHER") {
      return { error: "Permissions insuffisantes" };
    }

    const existingAnnouncement = await db.announcement.findUnique({
      where: { id },
    });

    if (!existingAnnouncement) {
      return { error: "Annonce non trouvée" };
    }

    if (
      existingAnnouncement.authorId !== session.user.id &&
      session.user.role !== "ADMIN"
    ) {
      return { error: "Permissions insuffisantes" };
    }

    const announcement = await db.announcement.update({
      where: { id },
      data: {
        isPublished,
        publishedAt: isPublished ? new Date() : null,
      },
    });

    revalidatePath("/admin/announcements");
    revalidatePath("/student/home");

    return {
      success: `Annonce ${isPublished ? "publiée 🚀" : "dépubliée 📝"} avec succès!`,
      data: announcement,
    };
  } catch (error) {
    console.error("[PUBLISH_ANNOUNCEMENT]", error);
    return { error: "Erreur interne du serveur" };
  }
};

export const deleteAnnouncement = async (id: string) => {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return { error: "Non autorisé" };
    }

    if (session.user.role !== "ADMIN" && session.user.role !== "TEACHER") {
      return { error: "Permissions insuffisantes" };
    }

    const existingAnnouncement = await db.announcement.findUnique({
      where: { id },
    });

    if (!existingAnnouncement) {
      return { error: "Annonce non trouvée" };
    }

    if (
      existingAnnouncement.authorId !== session.user.id &&
      session.user.role !== "ADMIN"
    ) {
      return { error: "Permissions insuffisantes" };
    }

    await db.announcement.delete({
      where: { id },
    });

    revalidatePath("/admin/announcements");
    revalidatePath("/student/home");

    return { success: "Annonce supprimée avec succès! 🗑️" };
  } catch (error) {
    console.error("[DELETE_ANNOUNCEMENT]", error);
    return { error: "Erreur interne du serveur" };
  }
};
