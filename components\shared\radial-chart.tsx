"use client";

import { TrendingUp } from "lucide-react";
import {
  PolarGrid,
  PolarRadiusAxis,
  RadialBar,
  RadialBarChart,
} from "recharts";
import { Badge } from "@/components/ui/badge";

import { Card, CardContent } from "@/components/ui/card";
import { type ChartConfig, ChartContainer } from "@/components/ui/chart";

interface RadialChartProps {
  title: string;
  footerText: string;
  tags: string[];
  progress: number;
}

export function RadialChart({
  title,
  footerText,
  tags,
  progress,
  children,
}: RadialChartProps & { children?: React.ReactNode }) {
  const chartData = [{ value: progress, fill: "#ffffff" }];

  return (
    <Card className="flex w-[420px] overflow-hidden">
      <CardContent className="w-full flex p-4 items-center">
        <ChartContainer
          config={{ value: { label: "Value", color: "#ffffff" } }}
          className="w-32 h-32 flex-shrink-0 relative"
        >
          <div>
            <Radial<PERSON>ar<PERSON>hart
              data={chartData}
              startAngle={0}
              endAngle={250}
              innerRadius={80}
              outerRadius={110}
            >
              <PolarGrid
                gridType="circle"
                radialLines={false}
                stroke="none"
                className="first:fill-muted last:fill-background"
                polarRadius={[86, 74]}
              />
              <RadialBar dataKey="value" cornerRadius={10} background />
              <PolarRadiusAxis tick={false} tickLine={false} axisLine={false} />
            </RadialBarChart>
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-2xl font-bold">
                {chartData[0].value.toLocaleString()}%
              </span>
            </div>
          </div>
        </ChartContainer>
        <div className="w-full flex flex-col justify-between ml-4 space-y-2">
          <div className="w-full flex flex-col gap-1">
            <div className="w-full flex flex-wrap gap-1">
              {tags.map((tag) => (
                <Badge key={tag} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
            <h3 className="font-semibold text-xl mb-2">{title}</h3>
          </div>
          <div className="w-full flex items-center justify-between gap-2">
            <div className="text-xs text-muted-foreground">{footerText}</div>
            {children}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
