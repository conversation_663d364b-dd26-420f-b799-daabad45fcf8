import type { <PERSON><PERSON><PERSON> } from "next";
import { cn } from "@/lib/utils";
import { ThemeProvider } from "@/utils/theme-provider";
import "../globals.css";
import Sidebar from "./_components/sidebar";
import Navbar from "./_components/navbar";
import { Spotlight } from "@/components/ui/spotlight-new";
import { Toaster } from "sonner";
import Footer from "@/components/shared/Footer";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div>
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
        <Toaster
          position="top-right"
          richColors
          closeButton
          theme="system"
        />
        <div className="max-md:hidden md:flex h-full w-72 flex-col fixed inset-y-0 z-50">
          <Sidebar />
        </div>
        <div className="md:hidden h-[70px] md:pl-72 fixed inset-y-0 w-full z-50">
          <Navbar />
        </div>
        <main className="md:pl-72">
          <div className="overflow-hidden dark:bg-grid-white/[0.04] bg-grid-black/[0.04] relative w-full flex flex-col items-center gap-3 max-md:py-24 p-16 max-lg:px-12 max-md:px-8 max-sm:px-4 z-10">
            <div className="absolute pointer-events-none inset-0 flex items-center justify-center dark:bg-black bg-white [mask-image:radial-gradient(ellipse_70%_7%_at_top,transparent_30%,black)]"></div>
            <Spotlight />
            <div className="mx-auto relative z-10 w-full">{children}</div>
          </div>
          <Footer />
        </main>
      </ThemeProvider>
    </div>
  );
}
