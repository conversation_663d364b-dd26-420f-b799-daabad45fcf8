"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { 
  MessageSquare, 
  Search, 
  Plus, 
  ThumbsUp, 
  MessageCircle, 
  Pin,
  Clock,
  User,
  Users,
  CheckCircle
} from "lucide-react";

interface ForumPost {
  id: string;
  title: string;
  content: string;
  author: string;
  authorRole: "student" | "teacher" | "admin";
  createdAt: Date;
  replies: number;
  likes: number;
  isPinned: boolean;
  isResolved: boolean;
  category: string;
  tags: string[];
}

interface DiscussionForumProps {
  posts: ForumPost[];
}

const DiscussionForum = ({ posts }: DiscussionForumProps) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [showNewPostForm, setShowNewPostForm] = useState(false);
  const [newPost, setNewPost] = useState({
    title: "",
    content: "",
    category: "general"
  });

  // Dummy data
  const dummyPosts: ForumPost[] = [
    {
      id: "post-1",
      title: "Comment résoudre les équations du second degré ?",
      content: "J'ai des difficultés avec la méthode du discriminant. Quelqu'un peut-il m'expliquer étape par étape ?",
      author: "Marie Dubois",
      authorRole: "student",
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
      replies: 5,
      likes: 12,
      isPinned: false,
      isResolved: true,
      category: "Questions",
      tags: ["algèbre", "équations", "aide"]
    },
    {
      id: "post-2",
      title: "Ressources supplémentaires pour la géométrie",
      content: "Voici quelques liens utiles pour approfondir vos connaissances en géométrie euclidienne...",
      author: "Prof. Hamza Ihind",
      authorRole: "teacher",
      createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      replies: 8,
      likes: 25,
      isPinned: true,
      isResolved: false,
      category: "Ressources",
      tags: ["géométrie", "ressources", "liens"]
    },
    {
      id: "post-3",
      title: "Groupe d'étude pour l'examen final",
      content: "Qui serait intéressé pour former un groupe d'étude en ligne ? On pourrait se retrouver sur Discord.",
      author: "Pierre Martin",
      authorRole: "student",
      createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      replies: 15,
      likes: 8,
      isPinned: false,
      isResolved: false,
      category: "Étude",
      tags: ["groupe", "examen", "collaboration"]
    },
    {
      id: "post-4",
      title: "Erreur dans l'exercice 3.2 ?",
      content: "Je pense qu'il y a une erreur dans l'énoncé de l'exercice 3.2. La réponse ne correspond pas...",
      author: "Sophie Laurent",
      authorRole: "student",
      createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      replies: 3,
      likes: 4,
      isPinned: false,
      isResolved: true,
      category: "Problèmes",
      tags: ["exercice", "erreur", "correction"]
    }
  ];

  const displayPosts = posts.length > 0 ? posts : dummyPosts;

  const getRoleIcon = (role: string) => {
    switch (role) {
      case "teacher": return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "admin": return <CheckCircle className="w-4 h-4 text-blue-500" />;
      default: return <User className="w-4 h-4 text-gray-500" />;
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case "teacher": return "Professeur";
      case "admin": return "Admin";
      default: return "Étudiant";
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "teacher": return "bg-green-100 text-green-800 dark:bg-green-950/20 dark:text-green-400";
      case "admin": return "bg-blue-100 text-blue-800 dark:bg-blue-950/20 dark:text-blue-400";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-950/20 dark:text-gray-400";
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "Questions": return "bg-orange-100 text-orange-800 dark:bg-orange-950/20 dark:text-orange-400";
      case "Ressources": return "bg-purple-100 text-purple-800 dark:bg-purple-950/20 dark:text-purple-400";
      case "Étude": return "bg-blue-100 text-blue-800 dark:bg-blue-950/20 dark:text-blue-400";
      case "Problèmes": return "bg-red-100 text-red-800 dark:bg-red-950/20 dark:text-red-400";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-950/20 dark:text-gray-400";
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return "Il y a quelques minutes";
    if (diffInHours < 24) return `Il y a ${diffInHours}h`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `Il y a ${diffInDays}j`;
    
    return date.toLocaleDateString('fr-FR');
  };

  const filteredPosts = displayPosts.filter(post => {
    const matchesSearch = !searchQuery || 
      post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === "all" || post.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const categories = [...new Set(displayPosts.map(post => post.category))];
  const pinnedPosts = filteredPosts.filter(post => post.isPinned);
  const regularPosts = filteredPosts.filter(post => !post.isPinned);

  const handleNewPost = () => {
    // Handle new post creation
    console.log("New post:", newPost);
    setShowNewPostForm(false);
    setNewPost({ title: "", content: "", category: "general" });
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="w-5 h-5 text-orange-500" />
            Forum de Discussion
          </CardTitle>
          <Button onClick={() => setShowNewPostForm(!showNewPostForm)}>
            <Plus className="w-4 h-4 mr-2" />
            Nouveau Post
          </Button>
        </div>
        
        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4 mt-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Rechercher dans le forum..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <Button
              variant={selectedCategory === "all" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory("all")}
            >
              Tout
            </Button>
            {categories.map(category => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category)}
              >
                {category}
              </Button>
            ))}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {/* New Post Form */}
        {showNewPostForm && (
          <Card className="mb-6 border-orange-200 dark:border-orange-800">
            <CardContent className="p-4">
              <h3 className="font-semibold text-lg mb-4">Créer un nouveau post</h3>
              <div className="space-y-4">
                <Input
                  placeholder="Titre de votre post..."
                  value={newPost.title}
                  onChange={(e) => setNewPost({...newPost, title: e.target.value})}
                />
                <Textarea
                  placeholder="Contenu de votre post..."
                  value={newPost.content}
                  onChange={(e) => setNewPost({...newPost, content: e.target.value})}
                  rows={4}
                />
                <div className="flex gap-2">
                  <Button onClick={handleNewPost}>Publier</Button>
                  <Button variant="outline" onClick={() => setShowNewPostForm(false)}>
                    Annuler
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Pinned Posts */}
        {pinnedPosts.length > 0 && (
          <div className="mb-6">
            <h3 className="font-semibold text-lg mb-4 flex items-center gap-2">
              <Pin className="w-5 h-5 text-orange-500" />
              Posts Épinglés
            </h3>
            <div className="space-y-4">
              {pinnedPosts.map((post) => (
                <Card key={post.id} className="border-orange-200 dark:border-orange-800 bg-orange-50 dark:bg-orange-950/10">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <Pin className="w-4 h-4 text-orange-500" />
                          <h3 className="font-semibold text-gray-900 dark:text-white">
                            {post.title}
                          </h3>
                          {post.isResolved && (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          )}
                        </div>
                        <p className="text-gray-600 dark:text-gray-300 line-clamp-2 mb-3">
                          {post.content}
                        </p>
                        <div className="flex flex-wrap gap-2 mb-3">
                          <Badge variant="outline" className={getCategoryColor(post.category)}>
                            {post.category}
                          </Badge>
                          <Badge variant="outline" className={getRoleBadgeColor(post.authorRole)}>
                            {getRoleIcon(post.authorRole)}
                            <span className="ml-1">{getRoleLabel(post.authorRole)}</span>
                          </Badge>
                          {post.tags.map(tag => (
                            <Badge key={tag} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center gap-4">
                        <span>Par {post.author}</span>
                        <div className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          <span>{formatTimeAgo(post.createdAt)}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <ThumbsUp className="w-4 h-4" />
                          <span>{post.likes}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <MessageCircle className="w-4 h-4" />
                          <span>{post.replies}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Regular Posts */}
        <div className="space-y-4">
          {regularPosts.map((post) => (
            <Card key={post.id} className="hover:shadow-md transition-shadow duration-200">
              <CardContent className="p-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-semibold text-gray-900 dark:text-white">
                        {post.title}
                      </h3>
                      {post.isResolved && (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      )}
                    </div>
                    <p className="text-gray-600 dark:text-gray-300 line-clamp-2 mb-3">
                      {post.content}
                    </p>
                    <div className="flex flex-wrap gap-2 mb-3">
                      <Badge variant="outline" className={getCategoryColor(post.category)}>
                        {post.category}
                      </Badge>
                      <Badge variant="outline" className={getRoleBadgeColor(post.authorRole)}>
                        {getRoleIcon(post.authorRole)}
                        <span className="ml-1">{getRoleLabel(post.authorRole)}</span>
                      </Badge>
                      {post.tags.map(tag => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <div className="flex items-center gap-4">
                    <span>Par {post.author}</span>
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      <span>{formatTimeAgo(post.createdAt)}</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    <Button variant="ghost" size="sm">
                      <ThumbsUp className="w-4 h-4 mr-1" />
                      {post.likes}
                    </Button>
                    <Button variant="ghost" size="sm">
                      <MessageCircle className="w-4 h-4 mr-1" />
                      {post.replies}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredPosts.length === 0 && (
          <div className="text-center py-12">
            <MessageSquare className="w-16 h-16 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Aucun post trouvé
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Essayez de modifier vos critères de recherche ou créez le premier post !
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DiscussionForum;
