import * as z from "zod";

const phoneRegex = new RegExp(/^(?:(?:\+|00)212|0)([5-7])([0-9]{8})$/);

export const NewPasswordSchema = z.object({
  password: z
    .string()
    .min(8, { message: "Le mot de passe est trop court!" })
    .max(20, { message: "Le mot de passe est trop long!" }),
});

export const ResetSchema = z.object({
  email: z.string().email(),
});

export const LoginSchema = z.object({
  email: z.string().email(),
  password: z.string(),
  code: z.optional(z.string()),
});

export const RegisterSchema = z.object({
  firstName: z.string().min(3),
  lastName: z.string().min(3),
  email: z.string().email(),
  password: z
    .string()
    .min(8, { message: "Le mot de passe est trop court!" })
    .max(20, { message: "Le mot de passe est trop long!" }),
});

export const OnboardingSchema = z.object({
  email: z.string().email(),
  image: z.string().url().nonempty(),
  firstName: z.string().min(3),
  lastName: z.string().min(3),
  username: z.string().min(3).max(30),
  phone: z.string().regex(phoneRegex, "Invalid Number!"),
  bio: z.string().min(3).max(500),
});

export const PasswordSchema = z
  .object({
    currentPassword: z
      .string()
      .min(6, { message: "Current password is required" }),
    newPassword: z
      .string()
      .min(6, { message: "New password must be at least 6 characters long" }),
    confirmPassword: z.string().min(6, {
      message: "Confirm password must be at least 6 characters long",
    }),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

export const AnnouncementSchema = z.object({
  title: z.string().min(1, {
    message: "Le titre est requis",
  }).max(100, {
    message: "Le titre ne peut pas dépasser 100 caractères",
  }),
  description: z.string().min(1, {
    message: "La description est requise",
  }).max(500, {
    message: "La description ne peut pas dépasser 500 caractères",
  }),
  content: z.string().optional(),
  type: z.enum(["EVENT", "NOTICE", "NEWS", "MAINTENANCE", "UPDATE"], {
    required_error: "Le type d'annonce est requis",
  }),
  priority: z.enum(["LOW", "MEDIUM", "HIGH", "URGENT"]).default("MEDIUM"),
  isPublished: z.boolean().default(false),
  isPinned: z.boolean().default(false),
  imageUrl: z.string().optional(),
  targetAudience: z.string().default("ALL"),
  expiresAt: z.date().optional(),
});