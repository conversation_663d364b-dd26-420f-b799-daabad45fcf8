"use client";

import { useForm } from "react-hook-form";
import * as z from "zod";
import axios from "axios";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON>, <PERSON>nOff } from "lucide-react";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormDescription,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";

interface PublishFormProps {
  initialData: {
    isPublished: boolean;
    isPinned: boolean;
  };
  announcementId: string;
}

const formSchema = z.object({
  isPublished: z.boolean(),
  isPinned: z.boolean(),
});

export const PublishForm = ({ initialData, announcementId }: PublishFormProps) => {
  const router = useRouter();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zod<PERSON><PERSON><PERSON>ver(formSchema),
    defaultValues: initialData,
  });

  const { isSubmitting, isValid } = form.formState;

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      await axios.patch(`/api/announcements/${announcementId}`, values);
      const publishStatus = values.isPublished ? "publiée 🚀" : "en brouillon 📝";
      const pinStatus = values.isPinned ? "épinglée 📌" : "non épinglée";

      toast.success("Options mises à jour!", {
        description: `Votre annonce est maintenant ${publishStatus} et ${pinStatus}`,
        duration: 4000,
      });
      router.refresh();
    } catch (error) {
      toast.error("Erreur lors de la mise à jour", {
        description: "Impossible de modifier les options. Veuillez réessayer.",
        duration: 4000,
      });
    }
  };

  return (
    <div className="w-full">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full flex flex-col gap-4"
        >
          <FormField
            control={form.control}
            name="isPublished"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Publier</FormLabel>
                  <FormDescription>
                    Rendre l'annonce visible aux utilisateurs
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled={isSubmitting}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="isPinned"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Épingler</FormLabel>
                  <FormDescription>
                    Afficher en haut de la liste des annonces
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled={isSubmitting}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <div className="flex justify-end">
            <Button
              disabled={!isValid || isSubmitting}
              type="submit"
            >
              Enregistrer
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};
