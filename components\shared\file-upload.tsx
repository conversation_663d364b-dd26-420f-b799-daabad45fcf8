import { UploadDropzone } from "@/lib/uploadthing";
import { ourFileRouter } from "@/app/api/uploadthing/core";
import { toast } from "sonner";

interface FileUploadProps {
  onChange: (fileData?: { url: string; name: string; size: number }) => void;
  endpoint: keyof typeof ourFileRouter;
}

export const FileUpload = ({ onChange, endpoint }: FileUploadProps) => {
  return (
    <UploadDropzone
      endpoint={endpoint}
      onClientUploadComplete={(res) => {
        if (res && res[0]) {
          toast.success("Fichier téléchargé! 📁", {
            description: `${res[0].name} a été téléchargé avec succès`,
            duration: 3000,
          });
          onChange({ url: res[0].url, name: res[0].name, size: res[0].size });
        }
      }}
      onUploadError={(error: Error) => {
        toast.error("Erreur de téléchargement", {
          description: error?.message || "Impossible de télécharger le fichier",
          duration: 4000,
        });
      }}
    />
  );
};
