"use client";

import { useState } from "react";
import axios from "axios";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { FileUpload } from "@/components/shared/file-upload";

interface ChapterVideoFormProps {
  initialData: {
    videoUrl: string | null;
  };
  courseId: string;
  sectionId: string;
  chapterId: string;
}

export const ChapterVideoForm = ({
  initialData,
  courseId,
  sectionId,
  chapterId,
}: ChapterVideoFormProps) => {
  const router = useRouter();
  const { toast } = useToast();
  const [isEditing, setIsEditing] = useState(false);

  const toggleEdit = () => setIsEditing((current) => !current);

  const onSubmit = async (values: { videoUrl: string }) => {
    try {
      await axios.patch(
        `/api/courses/${courseId}/sections/${sectionId}/chapters/${chapterId}`,
        values
      );
      toast({
        title: "Vidéo mise à jour",
        description: "La vidéo du chapitre a été modifiée avec succès",
      });
      toggleEdit();
      router.refresh();
    } catch {
      toast({
        title: "Erreur",
        description:
          "Une erreur est survenue lors de la mise à jour de la vidéo",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="w-full space-y-4">
      <div className="flex items-center justify-between">
        <Button onClick={toggleEdit} variant="outline">
          {isEditing ? "Annuler" : "Modifier la vidéo"}
        </Button>
      </div>

      {!isEditing ? (
        !initialData.videoUrl ? (
          <div className="flex flex-col items-center justify-center h-60 bg-slate-200 dark:bg-slate-800 rounded-md gap-2">
            <p className="text-slate-500 italic">Aucune vidéo téléchargée</p>
            <Button variant="ghost" size="sm" onClick={toggleEdit}>
              Ajouter une vidéo
            </Button>
          </div>
        ) : (
          <div className="relative aspect-video mt-2 bg-black rounded-md overflow-hidden">
            <video
              src={initialData.videoUrl}
              controls
              className="w-full h-full object-contain"
            />
          </div>
        )
      ) : (
        <div className="mt-4 space-y-2">
          <FileUpload
            endpoint="chapterVideo"
            onChange={(file) => {
              if (file) {
                onSubmit({ videoUrl: file.url });
              }
            }}
          />
          <div className="text-xs text-muted-foreground">
            Téléchargez la vidéo pour ce chapitre (MP4, WebM)
          </div>
        </div>
      )}
    </div>
  );
};
