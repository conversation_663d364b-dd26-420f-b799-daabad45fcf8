"use client";

import { useForm } from "react-hook-form";
import * as z from "zod";
import axios from "axios";
import { zodResolver } from "@hookform/resolvers/zod";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import { User } from "@prisma/client";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { useEffect, useState } from "react";
import { User as UserIcon } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

const formSchema = z.object({
  instructorId: z.string().nullable(),
});

interface InstructorFormProps {
  initialData: {
    instructorId?: string | null;
  };
  courseId: string;
}

export const InstructorForm = ({
  initialData,
  courseId,
}: InstructorFormProps) => {
  const [users, setUsers] = useState<User[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(true);
  const router = useRouter();
  const { toast } = useToast();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      instructorId: initialData.instructorId || null,
    },
  });

  const { isSubmitting, isValid } = form.formState;

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await axios.get("/api/users");
        setUsers(response.data);
      } catch (error) {
        toast({
          title: "Erreur",
          description: "Échec du chargement des utilisateurs",
          variant: "destructive",
        });
      } finally {
        setIsLoadingUsers(false);
      }
    };

    fetchUsers();
  }, []);

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      await axios.patch(`/api/courses/${courseId}`, {
        instructorId: values.instructorId || null,
      });
      toast({
        title: "Instructeur mis à jour",
        description: "L'instructeur du cours a été modifié avec succès",
      });
      router.refresh();
    } catch {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la mise à jour",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="w-full">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full flex gap-4 items-center max-sm:flex-col max-sm:items-end"
        >
          <FormField
            control={form.control}
            name="instructorId"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value || undefined}
                    disabled={isSubmitting || isLoadingUsers}
                  >
                    <SelectTrigger className="py-8">
                      <SelectValue placeholder="Sélectionner un instructeur" />
                    </SelectTrigger>
                    <SelectContent className="p-2">
                      <SelectItem value="null" className="pl-6">
                        <div className="flex items-center gap-4 p-2">
                          <UserIcon className="h-10 w-10" />
                          <div className="flex flex-col items-start">
                            <p className="text-sm dark:text-gray-400 text-gray-600 font-medium">
                              Aucun instructeur
                            </p>
                          </div>
                        </div>
                      </SelectItem>

                      {users.map((user) => (
                        <SelectItem
                          key={user.id}
                          value={user.id}
                          className="pl-6"
                        >
                          <div className="flex items-center gap-3 p-2">
                            <div className="relative">
                              <Avatar className="w-10 h-10">
                                <AvatarImage
                                  src={user.image || ""}
                                  alt={`${user.firstName} ${user.lastName}`}
                                  className="w-10 h-10 object-cover rounded-3xl border dark:border-orange-400 border-orange-600"
                                />
                                <AvatarFallback className="rounded-3xl border dark:border-orange-400 border-orange-600">
                                  {user.firstName?.charAt(0)}
                                  {user.lastName?.charAt(0)}
                                </AvatarFallback>
                              </Avatar>
                            </div>
                            <div className="flex flex-col items-start">
                              <p className="text-sm dark:text-gray-200 text-gray-500 font-medium">
                                {user.firstName} {user.lastName}
                              </p>
                              <p className="text-xs dark:text-gray-400 text-gray-600">
                                {user.email}
                              </p>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="flex items-center">
            <Button
              type="submit"
              disabled={!isValid || isSubmitting || isLoadingUsers}
              className="whitespace-nowrap"
            >
              {isSubmitting ? "Enregistrement..." : "Enregistrer"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};
