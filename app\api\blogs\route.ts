import { auth } from "@/auth";
import { NextResponse } from "next/server";
import { db } from "@/lib/db";

export async function POST(req: Request) {
  const session = await auth();
  try {
    const userId = session?.user.id;
    const { title } = await req.json();

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const blog = await db.blog.create({
      data: {
        userId,
        title,
        content: "",
        linkShare: `blog-${Date.now()}`,
      },
    });

    return NextResponse.json(blog);
  } catch (error) {
    console.log("[BLOGS]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}
