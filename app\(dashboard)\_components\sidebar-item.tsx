"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { LucideIcon } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";

interface SidebarItemProps {
  icon: LucideIcon;
  label: string;
  href: string;
}

export const SidebarItem = ({ icon: Icon, label, href }: SidebarItemProps) => {
  const pathname = usePathname();
  const router = useRouter();

  const isActive =
    (pathname === "/dashboard" && href === "/dashboard") ||
    pathname === href ||
    pathname?.startsWith(`${href}/`);

  const onClick = () => {
    router.push(href);
  };

  return (
    <Button
      onClick={onClick}
      type="button"
      className={cn(
        "mx-4 flex items-center gap-x-2 text-sm font-[500] pl-3 rounded-lg transition-all",
        "bg-transparent dark:bg-transparent",
        "text-gray-800 dark:text-slate-300",
        "hover:text-gray-600 dark:hover:text-gray-400",
        "hover:bg-gray-100 dark:hover:bg-gray-700",
        isActive &&
          "text-orange-500 dark:text-orange-500 bg-orange-100/20 dark:bg-orange-100/20"
      )}
    >
      <div className="flex items-center gap-x-3 py-3 -px-8 w-full">
        <Icon
          size={18}
          className={cn(
            "text-gray-600 dark:text-gray-400",
            "hover:text-gray-700 dark:hover:text-gray-500",
            isActive && "text-orange-500 dark:text-orange-500"
          )}
        />
        {label}
      </div>
    </Button>
  );
};
