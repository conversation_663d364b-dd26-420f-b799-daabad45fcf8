import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { db } from "@/lib/db";
import Link from "next/link";
import {
  ArrowLeft,
  BookOpen, // Pour le titre
  FileText, // Pour la description
  Film, // Pour la vidéo
  LockKeyhole, // Pour l'accès
  Paperclip, // Pour les pièces jointes
} from "lucide-react";
import DashboardPageTitle from "@/app/(dashboard)/_components/dashboard-page-title";
import { ChapterTitleForm } from "./_components/chapter-title-form";
import { ChapterDescriptionForm } from "./_components/chapter-description-form";
import { ChapterVideoForm } from "./_components/chapter-video-form";
import { ChapterAccessForm } from "./_components/chapter-access-form";
import { ChapterActions } from "./_components/chapter-actions";
import { Separator } from "@/components/ui/separator";
import { ChapterAttachmentForm } from "./_components/chapter-attachment-form";
import { DashboardSection } from "@/app/(dashboard)/_components/dashboard-section";

const ChapterIdPage = async ({
  params,
}: {
  params: { courseId: string; sectionId: string; chapterId: string };
}) => {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return redirect("/");
  }

  const chapter = await db.chapter.findUnique({
    where: {
      id: params.chapterId,
      sectionId: params.sectionId,
    },
    include: {
      attachments: true,
    },
  });

  if (!chapter) {
    return redirect("/");
  }

  const requiredFields = [chapter.title, chapter.description, chapter.videoUrl];

  const totalFields = requiredFields.length;
  const completedFields = requiredFields.filter(Boolean).length;
  const completionText = `(${completedFields}/${totalFields})`;
  const isComplete = requiredFields.every(Boolean);

  return (
    <div className="w-full z-20 flex flex-col">
      <Link
        href={`/teacher/courses/${params.courseId}/sections/${params.sectionId}/chapters`}
        className="flex items-center text-sm hover:opacity-75 transition mb-6"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Retour à la liste des chapitres
      </Link>

      <div className="flex items-start justify-between gap-y-4 max-2xl:flex-col">
        <DashboardPageTitle
          title="Configuration du Chapitre"
          description={`Champs complétés ${completionText}`}
        />
        <ChapterActions
          disabled={!isComplete}
          courseId={params.courseId}
          sectionId={params.sectionId}
          chapterId={params.chapterId}
          isPublished={chapter.isPublished}
        />
      </div>

      <Separator decorative className="mt-6 mb-8" />

      <div className="w-full h-full flex items-start justify-start gap-12 max-xl:flex-col max-sm:gap-6">
        {/* Colonne de gauche - Contenu textuel */}
        <div className="w-full flex flex-col grow gap-2 justify-between">
          <div className="flex flex-col gap-12">
            {/* Titre du chapitre */}
            <div className="flex flex-col gap-4">
              <DashboardSection
                icon={BookOpen}
                title="Titre du Chapitre"
                description="Définissez un titre clair et descriptif"
              />
              <ChapterTitleForm
                initialData={chapter}
                courseId={params.courseId}
                sectionId={params.sectionId}
                chapterId={params.chapterId}
              />
            </div>

            {/* Description détaillée */}
            <div className="flex flex-col gap-4">
              <DashboardSection
                icon={FileText}
                title="Description Détaillée"
                description="Rédigez le contenu pédagogique principal"
              />
              <ChapterDescriptionForm
                initialData={chapter}
                courseId={params.courseId}
                sectionId={params.sectionId}
                chapterId={params.chapterId}
              />
            </div>

            {/* Paramètres d'accès */}
            <div className="flex flex-col gap-4">
              <DashboardSection
                icon={LockKeyhole}
                title="Visibilité et Accès"
                description="Contrôlez qui peut accéder à ce chapitre"
              />
              <ChapterAccessForm
                initialData={chapter}
                courseId={params.courseId}
                sectionId={params.sectionId}
                chapterId={params.chapterId}
              />
            </div>
          </div>
        </div>

        {/* Séparateur vertical */}
        <Separator
          orientation="vertical"
          decorative
          className="h-full bg-muted"
        />

        {/* Colonne de droite - Multimédia et paramètres */}
        <div className="w-full flex flex-col grow gap-2 justify-between">
          {/* Contenu vidéo */}
          <div className="flex flex-col gap-12">
            <div className="flex flex-col gap-4">
              <DashboardSection
                icon={Film}
                title="Contenu Vidéo"
                description="Téléversez le média principal du chapitre"
              />
              <ChapterVideoForm
                initialData={chapter}
                courseId={params.courseId}
                sectionId={params.sectionId}
                chapterId={params.chapterId}
              />
            </div>

            {/* Ressources annexes */}
            <div className="flex flex-col gap-4">
              <DashboardSection
                icon={Paperclip}
                title="Ressources Annexes"
                description="Ajoutez des supports complémentaires"
              />
              <ChapterAttachmentForm
                initialData={{
                  ...chapter,
                  attachments: chapter.attachments || [],
                }}
                courseId={params.courseId}
                sectionId={params.sectionId}
                chapterId={params.chapterId}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChapterIdPage;
