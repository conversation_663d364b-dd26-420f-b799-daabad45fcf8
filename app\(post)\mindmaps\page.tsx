"use client";

import { useState } from "react";
import { posts } from "#site/content";
import Title from "@/components/shared/Title";
import { Spotlight } from "@/components/ui/spotlight";
import { SearchInput } from "./SearchInput";
import { TagSelector } from "./TagSelector";
import { SortSelector } from "./SortSelector";
import { PostList } from "./PostList";
import { getAllTags } from "@/lib/utils";

export default function Page() {
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [sortOption, setSortOption] = useState("Most Recent");
  const allTags = getAllTags(posts);

  const displayPosts = posts
    .filter(
      (post) =>
        (selectedTags.length === 0 ||
          post.tags?.some((tag) => selectedTags.includes(tag))) &&
        (post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          post.tags?.some((tag) =>
            tag.toLowerCase().includes(searchQuery.toLowerCase())
          ))
    )
    .sort((a: any, b: any) => {
      if (sortOption === "Most Recent") {
        return new Date(b.date).getTime() - new Date(a.date).getTime();
      } else if (sortOption === "Most Popular") {
        return b.date - a.date; // Assuming posts have a 'popularity' field
      }
      return 0;
    });

  const handleTagClick = (tag: string) => {
    setSelectedTags((prevSelectedTags) => {
      if (prevSelectedTags.includes(tag)) {
        return prevSelectedTags.filter((t) => t !== tag);
      } else {
        return [...prevSelectedTags, tag];
      }
    });
  };

  return (
    <div className="overflow-hidden dark:bg-grid-white/[0.04] bg-grid-black/[0.04] relative w-full flex flex-col items-center justify-center gap-3 p-32 max-lg:p-8">
      <div className="absolute pointer-events-none inset-0 flex items-center justify-center dark:bg-black bg-white [mask-image:radial-gradient(ellipse_50%_30%_at_top,transparent_40%,black)]"></div>
      <Spotlight
        className="-top-100 left-0 md:left-60 md:-top-20 opacity-10"
        fill="orange"
      />
      <div className="z-20 max-w-[100rem] flex flex-col items-center">
        <Title label="Microlearning" title="Mind Maps" description="" />
        <p className="p-text max-w-[1200px] max-md:-mt-4">
          Découvrez des cartes mentales détaillées pour une visualisation
          efficace des concepts mathématiques et scientifiques. Ces schémas vous
          aideront à organiser vos connaissances et à mieux saisir les notions
          complexes grâce à des représentations claires et structurées.
        </p>

        <SearchInput
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
        />

        <div className="mt-12 w-full flex justify-between max-md:flex-col gap-8">
          <TagSelector
            allTags={allTags}
            selectedTags={selectedTags}
            onTagClick={handleTagClick}
          />
          <SortSelector sortOption={sortOption} onSortChange={setSortOption} />
        </div>

        <div className="mt-12 w-full flex items-center justify-center">
          {displayPosts.length > 0 ? (
            <PostList posts={displayPosts} />
          ) : (
            <p>Nothing to see here yet</p>
          )}
        </div>
      </div>
    </div>
  );
}
