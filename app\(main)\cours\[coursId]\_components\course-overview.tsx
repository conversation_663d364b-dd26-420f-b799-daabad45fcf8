import BlogDisplay from "@/app/(main)/blogs/[blogId]/_components/blog-display";
import React from "react";

interface CourseOverviewProps {
  overview: string | null | undefined;
}

const CourseOverview = ({ overview }: CourseOverviewProps) => {
  return (
    <section className="py-14">
      <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-300 mb-6">
        Course Content
      </h1>
      <div className="course-container">
        <div className="grid md:grid-cols-5 gap-8">
          <div className="md:col-span-3">
            <div className="glass-card p-6 md:p-8">
              <div className="prose prose-lg max-w-none">
                <BlogDisplay content={overview || ""} />
              </div>
            </div>
          </div>

          <div className="md:col-span-2">
            <div className="glass-card p-6 md:p-8 h-full">
              <h3 className="text-xl font-semibold mb-4">
                Why Take This Course?
              </h3>

              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3 flex-shrink-0">
                    <span className="text-course-green font-bold">1</span>
                  </div>
                  <p className="text-course-dark">
                    Comprehensive curriculum designed by industry experts
                  </p>
                </div>

                <div className="flex items-start">
                  <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3 flex-shrink-0">
                    <span className="text-course-blue font-bold">2</span>
                  </div>
                  <p className="text-course-dark">
                    Hands-on projects to build your portfolio
                  </p>
                </div>

                <div className="flex items-start">
                  <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3 flex-shrink-0">
                    <span className="text-purple-600 font-bold">3</span>
                  </div>
                  <p className="text-course-dark">
                    Community support and networking opportunities
                  </p>
                </div>

                <div className="flex items-start">
                  <div className="w-8 h-8 rounded-full bg-orange-100 flex items-center justify-center mr-3 flex-shrink-0">
                    <span className="text-orange-500 font-bold">4</span>
                  </div>
                  <p className="text-course-dark">
                    Lifetime access to course updates and resources
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CourseOverview;
