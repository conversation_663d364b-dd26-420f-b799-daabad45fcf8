import React, { useState, useRef, useCallback } from "react";
import MDEditor from "@uiw/react-md-editor";
import rehypeKatex from "rehype-katex";
import remarkMath from "remark-math";
import CommandMenu from "../app/(dashboard)/(routes)/teacher/blogs/[blogId]/_components/command-menu";
import "katex/dist/katex.css";

interface EditorProps {
  value: string;
  onChange: (value: string) => void;
  height?: number;
}

const Editor: React.FC<EditorProps> = ({ value, onChange, height = 500 }) => {
  const [showCommandMenu, setShowCommandMenu] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 });
  const editorRef = useRef<HTMLDivElement>(null);

  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (e.key === "/" && !showCommandMenu) {
        e.preventDefault();
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
          const range = selection.getRangeAt(0);
          const rect = range.getBoundingClientRect();
          setMenuPosition({
            top: rect.bottom + window.scrollY + 10,
            left: rect.left + window.scrollX,
          });
        }
        setShowCommandMenu(true);
      } else if (e.key === "Escape" && showCommandMenu) {
        setShowCommandMenu(false);
      }
    },
    [showCommandMenu]
  );

  const handleCommandSelect = (command: string) => {
    const position =
      editorRef.current?.getElementsByTagName("textarea")[0]?.selectionStart ||
      0;
    const newValue = value.slice(0, position) + command + value.slice(position);
    onChange(newValue);
  };

  return (
    <div ref={editorRef} className="w-full" onKeyDown={handleKeyDown as any}>
      <div className="w-full">
        <MDEditor
          value={value}
          onChange={(val) => onChange(val || "")}
          preview="edit"
          previewOptions={{
            rehypePlugins: [[rehypeKatex]],
            remarkPlugins: [[remarkMath]],
          }}
          className="border-none"
          height={height}
        />
      </div>
      <CommandMenu
        isOpen={showCommandMenu}
        onClose={() => setShowCommandMenu(false)}
        onSelect={handleCommandSelect}
        position={menuPosition}
      />
    </div>
  );
};

export default Editor;
