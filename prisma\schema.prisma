// prisma/schema.prisma
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
  seed     = "../scripts/seed.ts"
}

// USER

enum CategoryType {
  COURSE
  BLOG
}

enum AnnouncementType {
  EVENT
  NOTICE
  NEWS
  MAINTENANCE
  UPDATE
}

enum AnnouncementPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum UserRole {
  ADMIN
  TEACHER
  USER
}

model Account {
  id                String  @id @default(cuid())
  userId            String  @map("user_id")
  type              String
  provider          String
  providerAccountId String  @map("provider_account_id")
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model User {
  id                    String                 @id @default(cuid())
  username              String?
  bio                   String?
  phone                 String?
  firstName             String?
  lastName              String?
  email                 String?                @unique
  emailVerified         DateTime?
  image                 String?
  password              String?
  role                  UserRole               @default(USER)
  isOnboarded           Boolean                @default(false)
  accounts              Account[]
  enrollments           Enrollment[]
  isTwoFactorEnabled    Boolean                @default(false)
  twoFactorConfirmation TwoFactorConfirmation?

  blogs        Blog[]         @relation("UserBlogs")
  decks        Deck[]         @relation("UserDecks")
  flashcards   Flashcard[]    @relation("UserFlashcards")
  likes        Like[]         @relation("UserLikes")
  comments     Comment[]      @relation("UserComments")
  Announcement Announcement[]
}

model VerificationToken {
  id      String   @id @default(cuid())
  email   String
  token   String   @unique
  expires DateTime

  @@unique([email, token])
}

model PasswordResetToken {
  id      String   @id @default(cuid())
  email   String
  token   String   @unique
  expires DateTime

  @@unique([email, token])
}

model TwoFactorToken {
  id      String   @id @default(cuid())
  email   String
  token   String   @unique
  expires DateTime

  @@unique([email, token])
}

model TwoFactorConfirmation {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId])
}

// COURSE

model Course {
  id             String   @id @default(uuid())
  userId         String?
  title          String   @db.Text
  description    String?  @db.Text
  imageUrl       String?  @db.Text
  price          Float?
  isPublished    Boolean  @default(false)
  introVideoUrl  String?  @db.Text
  courseIncludes String[]
  prerequisites  String[]
  whatYoullLearn String[]
  overview       String?  @db.Text
  instructorId   String?

  // relationships
  categories  Category[]   @relation("CourseCategories")
  sections    Section[]
  attachments Attachment[]
  purchases   Purchase[]
  enrollments Enrollment[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Section {
  id          String  @id @default(uuid())
  title       String  @db.Text
  description String? @db.Text
  position    Int
  isPublished Boolean @default(false)

  courseId String
  course   Course @relation(fields: [courseId], references: [id], onDelete: Cascade)

  chapters Chapter[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Attachment {
  id   String @id @default(uuid())
  name String
  url  String @db.Text

  courseId String?
  course   Course? @relation(fields: [courseId], references: [id], onDelete: Cascade)

  chapterId String?
  chapter   Chapter? @relation(fields: [chapterId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([courseId])
  @@index([chapterId])
}

model Chapter {
  id          String  @id @default(uuid())
  title       String
  description String? @db.Text
  videoUrl    String? @db.Text
  transcript  String? @db.Text
  notes       String? @db.Text
  position    Int
  isPublished Boolean @default(false)
  isFree      Boolean @default(false)

  // relationships
  muxData      MuxData?
  attachments  Attachment[]
  userProgress UserProgress[]

  sectionId String
  section   Section @relation(fields: [sectionId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([sectionId])
}

model MuxData {
  id         String  @id @default(uuid())
  assetId    String
  playbackId String?

  chapterId String  @unique
  chapter   Chapter @relation(fields: [chapterId], references: [id], onDelete: Cascade)
}

model UserProgress {
  id     String @id @default(uuid())
  userId String

  chapterId String
  chapter   Chapter @relation(fields: [chapterId], references: [id], onDelete: Cascade)

  isCompleted Boolean @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, chapterId])
  @@index([chapterId])
}

model Purchase {
  id     String @id @default(uuid())
  userId String

  courseId String
  course   Course @relation(fields: [courseId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, courseId])
  @@index([courseId])
}

model StripeCustomer {
  id             String @id @default(uuid())
  userId         String @unique
  stripeCustomer String @unique

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Enrollment {
  id         String   @id @default(cuid())
  userId     String
  courseId   String
  enrolledAt DateTime @default(now())

  user   User   @relation(fields: [userId], references: [id])
  course Course @relation(fields: [courseId], references: [id])

  @@unique([userId, courseId])
}

// BLOG

model Blog {
  id          String     @id @default(uuid())
  userId      String
  title       String     @db.Text
  description String?    @db.Text
  image       String?    @db.Text
  content     String?    @db.Text
  isPublished Boolean    @default(false)
  isSaved     Boolean    @default(false)
  isLiked     Boolean    @default(false)
  linkShare   String?    @unique @default("")
  likes       Like[]     @relation("BlogLikes")
  comments    Comment[]  @relation("BlogComments")
  categories  Category[] @relation("BlogCategories")
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  user User @relation("UserBlogs", fields: [userId], references: [id])
}

// SOCIAL INTERACTIONS WITH BLOG
model Like {
  id        String   @id @default(uuid())
  userId    String
  blogId    String
  createdAt DateTime @default(now())

  user User @relation("UserLikes", fields: [userId], references: [id])
  blog Blog @relation("BlogLikes", fields: [blogId], references: [id])
}

model Comment {
  id        String   @id @default(uuid())
  userId    String
  blogId    String
  content   String   @db.Text
  parentId  String?
  createdAt DateTime @default(now())

  user    User      @relation("UserComments", fields: [userId], references: [id])
  blog    Blog      @relation("BlogComments", fields: [blogId], references: [id])
  parent  Comment?  @relation("CommentReplies", fields: [parentId], references: [id])
  replies Comment[] @relation("CommentReplies")
}

model Category {
  id      String       @id @default(uuid())
  name    String       @unique
  type    CategoryType
  courses Course[]     @relation("CourseCategories")
  blogs   Blog[]       @relation("BlogCategories")
}

// FLASHCARD SYSTEM

model Deck {
  id          String      @id @default(cuid())
  name        String
  tag         Tag
  icon        Icon
  description String?
  color       Color
  flashcards  Flashcard[]
  userId      String
  user        User        @relation("UserDecks", fields: [userId], references: [id])
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
}

model Flashcard {
  id           String     @id @default(cuid())
  question     String
  answer       String
  order        Int        @default(0)
  difficulty   Difficulty @default(MEDIUM)
  lastReviewed DateTime?
  deckId       String
  deck         Deck       @relation(fields: [deckId], references: [id])
  userId       String
  user         User       @relation("UserFlashcards", fields: [userId], references: [id])

  // Anki algorithm fields
  // interval     Int        @default(1)       // Days until next review
  // repetitions  Int        @default(0)       // Number of times reviewed
  // easeFactor   Float      @default(2.5)     // Ease factor (default 250%)
  // nextReview   DateTime   @default(now())  // When card is due
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// ENUMS
enum Tag {
  LANGUAGE
  SCIENCE
  MATH
  HISTORY
  ART
  TECHNOLOGY
  MEDICINE
  BUSINESS
}

enum Icon {
  BOOK
  BRAIN
  CALCULATOR
  GLOBE
  MUSIC
  CODE
  FLASK
  CHART
}

enum Color {
  RED
  ORANGE
  YELLOW
  GREEN
  TEAL
  BLUE
  INDIGO
  PURPLE
}

enum Difficulty {
  VERY_EASY // 1 (Green)
  EASY // 2 (Blue)
  MEDIUM // 3 (Yellow)
  HARD // 4 (Orange)
  VERY_HARD // 5 (Red)
}

// ANNOUNCEMENTS

model Announcement {
  id             String               @id @default(cuid())
  title          String
  description    String               @db.Text
  content        String?              @db.Text
  type           AnnouncementType
  priority       AnnouncementPriority @default(MEDIUM)
  isPublished    Boolean              @default(false)
  isPinned       Boolean              @default(false)
  imageUrl       String?
  targetAudience String? // "ALL", "STUDENTS", "TEACHERS", specific course IDs

  authorId String
  author   User   @relation(fields: [authorId], references: [id], onDelete: Cascade)

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  publishedAt DateTime?
  expiresAt   DateTime?

  @@map("announcements")
}

// SESSION

model Session {
  id        String    @id @default(cuid())
  userId    String
  startTime DateTime
  endTime   DateTime?
}
