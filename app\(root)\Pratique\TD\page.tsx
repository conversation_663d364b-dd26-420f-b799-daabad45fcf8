import Title from "@/components/shared/Title";
import Image from "next/image";
import { Spotlight } from "@/components/ui/spotlight";
import { Badge } from "@/components/ui/badge";

const page = () => {
  return (
    <div className=" overflow-hidden dark:bg-grid-white/[0.04] bg-grid-black/[0.04] relative w-full flex flex-col items-center justify-center gap-3 p-32 max-lg:p-8">
      <div className="absolute pointer-events-none inset-0 flex items-center justify-center dark:bg-black bg-white [mask-image:radial-gradient(ellipse_50%_30%_at_top,transparent_40%,black)]"></div>
      <Spotlight
        className="-top-100 left-0 md:left-60 md:-top-20 opacity-10"
        fill="orange"
      />
      <div className="max-w-[100rem]">
        <Title
          label="Espace Pratique"
          title="Travaux Dirigés: Renforcez Vos Compétences"
          description=""
          additionalStyles="mb-[-48px]"
        />
        <p className="p-text max-w-[1200px] max-md:-mt-4">
          Découvrez nos astuces pratiques pour maîtriser rapidement les concepts
          mathématiques et physiques les plus complexes. Recherchez des astuces
          par sujet ou difficulté et commencez à optimiser votre apprentissage
          dès maintenant.
        </p>
      </div>
    </div>
  );
};

export default page;
