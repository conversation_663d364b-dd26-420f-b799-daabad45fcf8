"use client";

import { But<PERSON> } from "@/components/ui/button";
import { getCourses } from "@/actions/get-courses";
import {
  <PERSON>er,
  DrawerTrigger,
  Drawer<PERSON>ontent,
  DrawerHeader,
  DrawerTitle,
  DrawerDescription,
  DrawerFooter,
  DrawerClose,
} from "@/components/ui/drawer";
import {
  Sheet,
  SheetTrigger,
  Sheet<PERSON>ontent,
  SheetHeader,
  SheetTitle,
  SheetDescription,
} from "@/components/ui/sheet";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  Phone,
  Mail,
  Tag,
  Calendar,
  UserIcon,
  Calendar1,
  User,
  MailIcon,
  PhoneIcon,
} from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Course } from "@prisma/client";
import { useEffect, useState } from "react";

export default function UserInfo({ user }: any) {
  const [courses, setCourses] = useState<Course[]>([]);

  useEffect(() => {
    const fetchCourses = async () => {
      const fetchedCourses = await getCourses();
      setCourses(fetchedCourses);
    };

    fetchCourses();
  }, [user.id]);

  return (
    <>
      {/* Desktop Version */}
      <div className="hidden md:block">
        <Sheet>
          <SheetTrigger>
            <Button variant="secondary" size="icon">
              <UserIcon />
            </Button>
          </SheetTrigger>
          <SheetContent className="w-[900px]">
            <UserDetails user={user} courses={courses} />
          </SheetContent>
        </Sheet>
      </div>

      {/* Mobile Version */}
      <div className="md:hidden">
        <Drawer>
          <DrawerTrigger>
            <Button variant="secondary" size="icon">
              <UserIcon />
            </Button>
          </DrawerTrigger>
          <DrawerContent>
            <UserDetails user={user} courses={courses} />
            <DrawerFooter>
              <DrawerClose>
                <Button variant="outline">Fermer</Button>
              </DrawerClose>
            </DrawerFooter>
          </DrawerContent>
        </Drawer>
      </div>
    </>
  );
}

function UserDetails({ user, courses }: any) {
  console.log(courses);
  return (
    <div className="w-full flex flex-col items-start gap-4 p-4">
      <p className="text-2xl font-semibold">Informations personnelles</p>
      <div className="w-full flex flex-col gap-4">
        <div className="flex items-center gap-4">
          <Avatar className="w-16 h-16">
            <AvatarImage src={user.image || ""} alt={user?.firstName || ""} />
            <AvatarFallback>{user.firstName?.[0] || "?"}</AvatarFallback>
          </Avatar>
          <div className="w-full flex flex-col items-start">
            <p className="font-medium text-2xl text-white">
              {user.firstName} {user.lastName}
            </p>
            <div className="w-full flex items-center justify-start gap-2">
              <p className="desc-text text-sm font-semibold">
                Age 21 •{" "}
                {user.role.charAt(0).toUpperCase() +
                  user.role.slice(1).toLowerCase()}{" "}
                • Agadir, Morocco
              </p>
            </div>
          </div>
        </div>
        <div className="w-full flex gap-8">
          <div className="flex gap-2 items-center desc-text text-sm">
            <Calendar1 />
            {new Date(user.emailVerified).toLocaleDateString("fr-FR", {
              day: "2-digit",
              month: "short",
              year: "numeric",
            })}
          </div>
          <div className="flex gap-2 items-center desc-text text-sm">
            <User />
            Last Attended 3 days ago
          </div>
        </div>
        <div className="w-full flex gap-8">
          <div className="flex gap-2 items-center desc-text text-sm">
            <MailIcon />
            {user.email}
          </div>
          <div className="flex gap-2 items-center desc-text text-sm">
            <PhoneIcon />
            {user.phone}
          </div>
        </div>
        <div className="mt-2 w-full flex flex-col gap-2 bg-neutral-900 rounded-md p-4">
          <span className="desc-text">Biographie</span>
          {user.bio}
        </div>
      </div>
      <Separator />
      <p className="text-2xl font-semibold">Enrolled Courses</p>
      <div className="w-full flex flex-col gap-4">
        {courses.map((course: Course) => (
          <div className="w-full flex items-center gap-4" key={course.id}>
            {course.title}
          </div>
        ))}
      </div>
    </div>
  );
}
