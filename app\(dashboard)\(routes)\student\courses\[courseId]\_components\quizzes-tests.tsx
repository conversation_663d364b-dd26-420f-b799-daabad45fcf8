"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { 
  Trophy, 
  Clock, 
  Target, 
  Star, 
  Medal,
  CheckCircle,
  AlertCircle,
  Users,
  Calendar,
  Play,
  RotateCcw
} from "lucide-react";

interface Quiz {
  id: string;
  title: string;
  type: "quiz" | "test" | "exam";
  questions: number;
  timeLimit: string;
  attempts: number;
  maxAttempts: number;
  bestScore?: number;
  lastAttempt?: Date;
  deadline?: Date;
  difficulty: "facile" | "moyen" | "difficile";
  topic: string;
  isCompleted: boolean;
  isLocked: boolean;
}

interface LeaderboardEntry {
  rank: number;
  name: string;
  score: number;
  completionTime: string;
  avatar?: string;
}

interface QuizzesTestsProps {
  quizzes: Quiz[];
  leaderboard: LeaderboardEntry[];
}

const QuizzesTests = ({ quizzes, leaderboard }: QuizzesTestsProps) => {
  const [selectedType, setSelectedType] = useState<string>("all");

  // Dummy data
  const dummyQuizzes: Quiz[] = [
    {
      id: "quiz-1",
      title: "Quiz: Équations du premier degré",
      type: "quiz",
      questions: 10,
      timeLimit: "15 min",
      attempts: 2,
      maxAttempts: 3,
      bestScore: 95,
      lastAttempt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      difficulty: "facile",
      topic: "Algèbre",
      isCompleted: true,
      isLocked: false
    },
    {
      id: "quiz-2",
      title: "Test: Systèmes d'équations",
      type: "test",
      questions: 15,
      timeLimit: "30 min",
      attempts: 1,
      maxAttempts: 2,
      bestScore: 87,
      lastAttempt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      difficulty: "moyen",
      topic: "Algèbre",
      isCompleted: true,
      isLocked: false
    },
    {
      id: "quiz-3",
      title: "Quiz: Géométrie euclidienne",
      type: "quiz",
      questions: 12,
      timeLimit: "20 min",
      attempts: 0,
      maxAttempts: 3,
      difficulty: "moyen",
      topic: "Géométrie",
      isCompleted: false,
      isLocked: false
    },
    {
      id: "exam-1",
      title: "Examen Final: Mathématiques Générales",
      type: "exam",
      questions: 50,
      timeLimit: "2h 00min",
      attempts: 0,
      maxAttempts: 1,
      deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      difficulty: "difficile",
      topic: "Général",
      isCompleted: false,
      isLocked: true
    }
  ];

  const dummyLeaderboard: LeaderboardEntry[] = [
    { rank: 1, name: "Marie Dubois", score: 98, completionTime: "12:30", avatar: "/avatars/marie.jpg" },
    { rank: 2, name: "Pierre Martin", score: 95, completionTime: "14:15", avatar: "/avatars/pierre.jpg" },
    { rank: 3, name: "Sophie Laurent", score: 92, completionTime: "13:45", avatar: "/avatars/sophie.jpg" },
    { rank: 4, name: "Vous", score: 87, completionTime: "16:20", avatar: "/avatars/you.jpg" },
    { rank: 5, name: "Jean Moreau", score: 85, completionTime: "15:30", avatar: "/avatars/jean.jpg" }
  ];

  const displayQuizzes = quizzes.length > 0 ? quizzes : dummyQuizzes;
  const displayLeaderboard = leaderboard.length > 0 ? leaderboard : dummyLeaderboard;

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "quiz": return <Target className="w-4 h-4" />;
      case "test": return <CheckCircle className="w-4 h-4" />;
      case "exam": return <Medal className="w-4 h-4" />;
      default: return <Trophy className="w-4 h-4" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "quiz": return "Quiz";
      case "test": return "Test";
      case "exam": return "Examen";
      default: return "Évaluation";
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "quiz": return "bg-blue-100 text-blue-800 dark:bg-blue-950/20 dark:text-blue-400";
      case "test": return "bg-orange-100 text-orange-800 dark:bg-orange-950/20 dark:text-orange-400";
      case "exam": return "bg-red-100 text-red-800 dark:bg-red-950/20 dark:text-red-400";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-950/20 dark:text-gray-400";
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "facile": return "bg-green-100 text-green-800 dark:bg-green-950/20 dark:text-green-400";
      case "moyen": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-950/20 dark:text-yellow-400";
      case "difficile": return "bg-red-100 text-red-800 dark:bg-red-950/20 dark:text-red-400";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-950/20 dark:text-gray-400";
    }
  };

  const filteredQuizzes = displayQuizzes.filter(quiz => {
    return selectedType === "all" || quiz.type === selectedType;
  });

  const completedQuizzes = displayQuizzes.filter(q => q.isCompleted).length;
  const averageScore = displayQuizzes
    .filter(q => q.bestScore)
    .reduce((acc, q) => acc + (q.bestScore || 0), 0) / displayQuizzes.filter(q => q.bestScore).length || 0;

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('fr-FR', { 
      day: 'numeric', 
      month: 'short',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return <Trophy className="w-5 h-5 text-yellow-500" />;
      case 2: return <Medal className="w-5 h-5 text-gray-400" />;
      case 3: return <Medal className="w-5 h-5 text-orange-600" />;
      default: return <div className="w-5 h-5 flex items-center justify-center text-sm font-bold text-gray-500">#{rank}</div>;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Trophy className="w-5 h-5 text-orange-500" />
          Quiz & Tests Gamifiés
        </CardTitle>
        
        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
          <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle className="w-5 h-5 text-blue-600" />
              <span className="font-medium text-blue-900 dark:text-blue-100">Complétés</span>
            </div>
            <div className="text-2xl font-bold text-blue-600">
              {completedQuizzes}/{displayQuizzes.length}
            </div>
          </div>
          
          <div className="bg-green-50 dark:bg-green-950/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Star className="w-5 h-5 text-green-600" />
              <span className="font-medium text-green-900 dark:text-green-100">Score Moyen</span>
            </div>
            <div className="text-2xl font-bold text-green-600">
              {averageScore > 0 ? `${Math.round(averageScore)}%` : "N/A"}
            </div>
          </div>
          
          <div className="bg-orange-50 dark:bg-orange-950/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Trophy className="w-5 h-5 text-orange-600" />
              <span className="font-medium text-orange-900 dark:text-orange-100">Classement</span>
            </div>
            <div className="text-2xl font-bold text-orange-600">
              #{displayLeaderboard.find(entry => entry.name === "Vous")?.rank || "N/A"}
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <Tabs defaultValue="quizzes" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="quizzes">
              Quiz & Tests ({filteredQuizzes.length})
            </TabsTrigger>
            <TabsTrigger value="leaderboard">
              Classement
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="quizzes" className="mt-6">
            {/* Filter Buttons */}
            <div className="flex gap-2 mb-6">
              <Button
                variant={selectedType === "all" ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedType("all")}
              >
                Tout
              </Button>
              <Button
                variant={selectedType === "quiz" ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedType("quiz")}
              >
                Quiz
              </Button>
              <Button
                variant={selectedType === "test" ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedType("test")}
              >
                Tests
              </Button>
              <Button
                variant={selectedType === "exam" ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedType("exam")}
              >
                Examens
              </Button>
            </div>
            
            <div className="space-y-4">
              {filteredQuizzes.map((quiz) => (
                <Card key={quiz.id} className={`hover:shadow-md transition-shadow duration-200 ${
                  quiz.isLocked ? "opacity-60" : ""
                }`}>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold text-lg text-gray-900 dark:text-white">
                            {quiz.title}
                          </h3>
                          {quiz.isLocked && (
                            <AlertCircle className="w-5 h-5 text-orange-500" />
                          )}
                        </div>
                        
                        <div className="flex flex-wrap gap-2 mb-3">
                          <Badge variant="outline" className={getTypeColor(quiz.type)}>
                            {getTypeIcon(quiz.type)}
                            <span className="ml-1">{getTypeLabel(quiz.type)}</span>
                          </Badge>
                          <Badge variant="outline" className={getDifficultyColor(quiz.difficulty)}>
                            {quiz.difficulty}
                          </Badge>
                          <Badge variant="outline">
                            {quiz.topic}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center gap-6 text-sm text-gray-600 dark:text-gray-300">
                          <div className="flex items-center gap-1">
                            <Target className="w-4 h-4" />
                            <span>{quiz.questions} questions</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            <span>{quiz.timeLimit}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <RotateCcw className="w-4 h-4" />
                            <span>{quiz.attempts}/{quiz.maxAttempts} tentatives</span>
                          </div>
                        </div>
                      </div>
                      
                      {quiz.bestScore && (
                        <div className="text-right">
                          <div className="text-2xl font-bold text-green-600 mb-1">
                            {quiz.bestScore}%
                          </div>
                          <div className="text-sm text-gray-500">
                            Meilleur score
                          </div>
                        </div>
                      )}
                    </div>
                    
                    {quiz.deadline && (
                      <div className="mb-4 p-3 bg-orange-50 dark:bg-orange-950/20 rounded-lg">
                        <div className="flex items-center gap-2 text-orange-700 dark:text-orange-300">
                          <Calendar className="w-4 h-4" />
                          <span className="text-sm">
                            Échéance: {formatDate(quiz.deadline)}
                          </span>
                        </div>
                      </div>
                    )}
                    
                    {quiz.lastAttempt && (
                      <div className="mb-4 text-sm text-gray-500">
                        Dernière tentative: {formatDate(quiz.lastAttempt)}
                      </div>
                    )}
                    
                    <div className="flex gap-2">
                      <Button 
                        className="flex-1"
                        disabled={quiz.isLocked || (quiz.attempts >= quiz.maxAttempts && !quiz.isCompleted)}
                        variant={quiz.isCompleted ? "outline" : "default"}
                      >
                        <Play className="w-4 h-4 mr-2" />
                        {quiz.isLocked 
                          ? "Verrouillé" 
                          : quiz.attempts > 0 
                            ? "Reprendre" 
                            : "Commencer"
                        }
                      </Button>
                      
                      {quiz.isCompleted && (
                        <Button variant="outline">
                          Voir les résultats
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="leaderboard" className="mt-6">
            <div className="space-y-4">
              {displayLeaderboard.map((entry) => (
                <Card key={entry.rank} className={`${
                  entry.name === "Vous" ? "ring-2 ring-orange-500 bg-orange-50 dark:bg-orange-950/20" : ""
                }`}>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-4">
                      <div className="flex-shrink-0">
                        {getRankIcon(entry.rank)}
                      </div>
                      
                      <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                        <Users className="w-5 h-5 text-gray-500" />
                      </div>
                      
                      <div className="flex-1">
                        <h3 className={`font-semibold ${
                          entry.name === "Vous" ? "text-orange-600" : "text-gray-900 dark:text-white"
                        }`}>
                          {entry.name}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-300">
                          Temps: {entry.completionTime}
                        </p>
                      </div>
                      
                      <div className="text-right">
                        <div className="text-xl font-bold text-green-600">
                          {entry.score}%
                        </div>
                        <div className="text-sm text-gray-500">
                          Score
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default QuizzesTests;
