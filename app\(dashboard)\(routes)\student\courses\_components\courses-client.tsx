"use client";

import { useState, useMemo } from "react";
import FilterSection from "./filter-section";
import CourseList from "./course-list";
import ResumeCourse from "./resume-course";
import UpcomingDeadlines from "./upcoming-deadlines";

interface Course {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  price: number;
  progress: number;
  lastAccessed: Date;
  instructor: string;
  category: string;
  totalChapters: number;
  completedChapters: number;
  isCompleted: boolean;
}

interface CoursesClientProps {
  initialCourses: any[];
}

const CoursesClient = ({ initialCourses }: CoursesClientProps) => {
  // Filter states
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");
  const [selectedInstructor, setSelectedInstructor] = useState("");
  const [sortBy, setSortBy] = useState("recent");

  // Transform courses data with dummy progress and metadata
  const courses: Course[] = useMemo(() => {
    return initialCourses.map((course, index) => ({
      id: course.id,
      title: course.title,
      description: course.description || "Description du cours",
      imageUrl: course.imageUrl || "/images/placeholder-course.jpg",
      price: course.price || 0,
      progress: Math.floor(Math.random() * 100), // Dummy progress
      lastAccessed: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000), // Random last access within 7 days
      instructor: "Dr. Hamza Ihind", // Dummy instructor
      category: course.categories?.[0]?.name || "Mathématiques",
      totalChapters: Math.floor(Math.random() * 20) + 5, // 5-25 chapters
      completedChapters: Math.floor(Math.random() * 15) + 1, // 1-16 chapters
      isCompleted: Math.random() > 0.7, // 30% chance of being completed
    }));
  }, [initialCourses]);

  // Get unique categories and instructors
  const categories = useMemo(() => {
    const cats = courses.map(course => course.category);
    return [...new Set(cats)];
  }, [courses]);

  const instructors = useMemo(() => {
    const insts = courses.map(course => course.instructor);
    return [...new Set(insts)];
  }, [courses]);

  // Filter and sort courses
  const filteredAndSortedCourses = useMemo(() => {
    let filtered = courses.filter(course => {
      const matchesSearch = !searchQuery || 
        course.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        course.description.toLowerCase().includes(searchQuery.toLowerCase());
      
      const matchesCategory = !selectedCategory || course.category === selectedCategory;
      
      const matchesStatus = !selectedStatus || 
        (selectedStatus === "completed" && course.isCompleted) ||
        (selectedStatus === "in-progress" && !course.isCompleted && course.progress > 0) ||
        (selectedStatus === "not-started" && course.progress === 0);
      
      const matchesInstructor = !selectedInstructor || course.instructor === selectedInstructor;

      return matchesSearch && matchesCategory && matchesStatus && matchesInstructor;
    });

    // Sort courses
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "recent":
          return b.lastAccessed.getTime() - a.lastAccessed.getTime();
        case "progress":
          return b.progress - a.progress;
        case "alphabetical":
          return a.title.localeCompare(b.title);
        case "completion":
          return b.completedChapters - a.completedChapters;
        default:
          return 0;
      }
    });

    return filtered;
  }, [courses, searchQuery, selectedCategory, selectedStatus, selectedInstructor, sortBy]);

  // Get last accessed course for resume section
  const lastCourse = useMemo(() => {
    const inProgressCourses = courses.filter(course => !course.isCompleted && course.progress > 0);
    if (inProgressCourses.length === 0) return null;
    
    const mostRecent = inProgressCourses.reduce((latest, current) => 
      current.lastAccessed > latest.lastAccessed ? current : latest
    );

    return {
      id: mostRecent.id,
      title: mostRecent.title,
      imageUrl: mostRecent.imageUrl,
      progress: mostRecent.progress,
      lastChapter: `Chapitre ${mostRecent.completedChapters + 1}`,
      timeSpent: Math.floor(Math.random() * 300) + 60, // 60-360 minutes
      category: mostRecent.category,
    };
  }, [courses]);

  const clearFilters = () => {
    setSearchQuery("");
    setSelectedCategory("");
    setSelectedStatus("");
    setSelectedInstructor("");
  };

  return (
    <div className="space-y-6">
      {/* Resume Last Course */}
      <ResumeCourse course={lastCourse} />

      {/* Filters */}
      <FilterSection
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        selectedCategory={selectedCategory}
        onCategoryChange={setSelectedCategory}
        selectedStatus={selectedStatus}
        onStatusChange={setSelectedStatus}
        selectedInstructor={selectedInstructor}
        onInstructorChange={setSelectedInstructor}
        sortBy={sortBy}
        onSortChange={setSortBy}
        categories={categories}
        instructors={instructors}
        onClearFilters={clearFilters}
      />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Course List */}
        <div className="lg:col-span-2">
          <div className="mb-4">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Mes Cours ({filteredAndSortedCourses.length})
            </h2>
            <p className="text-gray-600 dark:text-gray-300">
              Gérez et suivez vos cours inscrits
            </p>
          </div>
          <CourseList courses={filteredAndSortedCourses} />
        </div>

        {/* Sidebar - Bigger and more responsive */}
        <div className="lg:col-span-1">
          <UpcomingDeadlines deadlines={[]} />
        </div>
      </div>
    </div>
  );
};

export default CoursesClient;
