import Link from "next/link";
import Image from "next/image";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { 
  Calendar, 
  AlertTriangle, 
  Newspaper, 
  Settings, 
  Star,
  Pin,
  Clock
} from "lucide-react";
import { AnnouncementWithAuthor } from "@/actions/get-announcements";

interface AnnouncementCardProps {
  announcement: AnnouncementWithAuthor;
}

export const AnnouncementCard = ({ announcement }: AnnouncementCardProps) => {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case "EVENT": return Calendar;
      case "NOTICE": return AlertTriangle;
      case "NEWS": return Newspaper;
      case "MAINTENANCE": return Settings;
      case "UPDATE": return Star;
      default: return Newspaper;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "EVENT": return "Événement";
      case "NOTICE": return "Avis";
      case "NEWS": return "Actualité";
      case "MAINTENANCE": return "Maintenance";
      case "UPDATE": return "Mise à jour";
      default: return "Annonce";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "HIGH": 
      case "URGENT": return "bg-red-100 text-red-800 dark:bg-red-950/20 dark:text-red-400";
      case "MEDIUM": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-950/20 dark:text-yellow-400";
      case "LOW": return "bg-blue-100 text-blue-800 dark:bg-blue-950/20 dark:text-blue-400";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-950/20 dark:text-gray-400";
    }
  };

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case "HIGH": return "Élevée";
      case "URGENT": return "Urgente";
      case "MEDIUM": return "Moyenne";
      case "LOW": return "Faible";
      default: return priority;
    }
  };

  const TypeIcon = getTypeIcon(announcement.type);
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  return (
    <Link href={`/student/announcements/${announcement.id}`}>
      <Card className="h-full hover:shadow-lg transition-all duration-200 cursor-pointer group">
        {announcement.imageUrl && (
          <div className="relative h-48 w-full overflow-hidden rounded-t-lg">
            <Image
              src={announcement.imageUrl}
              alt={announcement.title}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-200"
            />
            {announcement.isPinned && (
              <div className="absolute top-3 right-3">
                <Badge className="bg-orange-500 text-white">
                  <Pin className="w-3 h-3 mr-1" />
                  Épinglé
                </Badge>
              </div>
            )}
          </div>
        )}
        
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between gap-2">
            <div className="flex items-center gap-2 flex-1">
              <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-orange-600 rounded-full flex items-center justify-center flex-shrink-0">
                <TypeIcon className="w-4 h-4 text-white" />
              </div>
              <div className="flex flex-col gap-1 min-w-0 flex-1">
                <Badge variant="outline" className="w-fit text-xs">
                  {getTypeLabel(announcement.type)}
                </Badge>
                {!announcement.imageUrl && announcement.isPinned && (
                  <Badge className="bg-orange-500 text-white w-fit text-xs">
                    <Pin className="w-3 h-3 mr-1" />
                    Épinglé
                  </Badge>
                )}
              </div>
            </div>
            <Badge variant="outline" className={`text-xs ${getPriorityColor(announcement.priority)}`}>
              {getPriorityLabel(announcement.priority)}
            </Badge>
          </div>
          
          <h3 className="font-semibold text-lg line-clamp-2 group-hover:text-orange-600 transition-colors">
            {announcement.title}
          </h3>
        </CardHeader>
        
        <CardContent className="pt-0">
          <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-3 mb-4">
            {announcement.description}
          </p>
          
          <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
            <div className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              <span>{formatDate(announcement.createdAt)}</span>
            </div>
            <div className="flex items-center gap-1">
              <span>Par {announcement.author.firstName} {announcement.author.lastName}</span>
            </div>
          </div>
          
          {announcement.expiresAt && (
            <div className="mt-2 text-xs text-amber-600 dark:text-amber-400">
              Expire le {formatDate(announcement.expiresAt)}
            </div>
          )}
        </CardContent>
      </Card>
    </Link>
  );
};
