import { auth } from "@/auth";
import { db } from "@/lib/db";
import { AnnouncementSchema } from "@/schemas";
import { NextRequest, NextResponse } from "next/server";

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const published = searchParams.get("published");
    const type = searchParams.get("type");
    const priority = searchParams.get("priority");
    const targetAudience = searchParams.get("targetAudience");
    const limit = searchParams.get("limit");

    const whereClause: any = {};

    if (published !== null) {
      whereClause.isPublished = published === "true";
    }

    if (type) {
      whereClause.type = type;
    }

    if (priority) {
      whereClause.priority = priority;
    }

    if (targetAudience) {
      whereClause.OR = [
        { targetAudience: "ALL" },
        { targetAudience: targetAudience },
        { targetAudience: { contains: targetAudience } },
      ];
    }

    // Don't include expired announcements for published requests
    if (published === "true") {
      whereClause.OR = [
        { expiresAt: null },
        { expiresAt: { gt: new Date() } },
      ];
    }

    const announcements = await db.announcement.findMany({
      where: whereClause,
      include: {
        author: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            image: true,
            role: true,
          },
        },
      },
      orderBy: [
        { isPinned: "desc" },
        { priority: "desc" },
        { createdAt: "desc" },
      ],
      take: limit ? parseInt(limit) : undefined,
    });

    return NextResponse.json(announcements);
  } catch (error) {
    console.error("[ANNOUNCEMENTS_GET]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    if (session.user.role !== "ADMIN" && session.user.role !== "TEACHER") {
      return new NextResponse("Forbidden", { status: 403 });
    }

    const body = await req.json();
    const validatedFields = AnnouncementSchema.safeParse(body);

    if (!validatedFields.success) {
      return new NextResponse("Invalid fields", { status: 400 });
    }

    const {
      title,
      description,
      content,
      type,
      priority,
      isPublished,
      isPinned,
      imageUrl,
      targetAudience,
      expiresAt,
    } = validatedFields.data;

    const announcement = await db.announcement.create({
      data: {
        title,
        description,
        content,
        type,
        priority,
        isPublished,
        isPinned,
        imageUrl,
        targetAudience,
        expiresAt,
        authorId: session.user.id,
        publishedAt: isPublished ? new Date() : null,
      },
      include: {
        author: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            image: true,
            role: true,
          },
        },
      },
    });

    return NextResponse.json(announcement);
  } catch (error) {
    console.error("[ANNOUNCEMENTS_POST]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}
