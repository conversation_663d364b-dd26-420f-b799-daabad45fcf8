import { auth } from "@/auth";
import { db } from "@/lib/db";
import { AnnouncementSchema } from "@/schemas";
import { NextRequest, NextResponse } from "next/server";
import * as z from "zod";

export async function GET(
  req: NextRequest,
  { params }: { params: { announcementId: string } }
) {
  try {
    const announcement = await db.announcement.findUnique({
      where: {
        id: params.announcementId,
      },
      include: {
        author: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            image: true,
            role: true,
          },
        },
      },
    });

    if (!announcement) {
      return new NextResponse("Not Found", { status: 404 });
    }

    return NextResponse.json(announcement);
  } catch (error) {
    console.error("[ANNOUNCEMENT_GET]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}

export async function PATCH(
  req: NextRequest,
  { params }: { params: { announcementId: string } }
) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    if (session.user.role !== "ADMIN" && session.user.role !== "TEACHER") {
      return new NextResponse("Forbidden", { status: 403 });
    }

    const body = await req.json();
    const UpdateAnnouncementSchema = AnnouncementSchema.partial().extend({
      id: z.string(),
    });
    const validatedFields = UpdateAnnouncementSchema.safeParse({
      ...body,
      id: params.announcementId,
    });

    if (!validatedFields.success) {
      return new NextResponse("Invalid fields", { status: 400 });
    }

    const { id, ...updateData } = validatedFields.data;

    // Check if announcement exists and user has permission
    const existingAnnouncement = await db.announcement.findUnique({
      where: { id: params.announcementId },
    });

    if (!existingAnnouncement) {
      return new NextResponse("Not Found", { status: 404 });
    }

    if (
      existingAnnouncement.authorId !== session.user.id &&
      session.user.role !== "ADMIN"
    ) {
      return new NextResponse("Forbidden", { status: 403 });
    }

    const announcement = await db.announcement.update({
      where: {
        id: params.announcementId,
      },
      data: {
        ...updateData,
        publishedAt:
          updateData.isPublished && !existingAnnouncement.publishedAt
            ? new Date()
            : existingAnnouncement.publishedAt,
      },
      include: {
        author: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            image: true,
            role: true,
          },
        },
      },
    });

    return NextResponse.json(announcement);
  } catch (error) {
    console.error("[ANNOUNCEMENT_PATCH]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: { announcementId: string } }
) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    if (session.user.role !== "ADMIN" && session.user.role !== "TEACHER") {
      return new NextResponse("Forbidden", { status: 403 });
    }

    const existingAnnouncement = await db.announcement.findUnique({
      where: { id: params.announcementId },
    });

    if (!existingAnnouncement) {
      return new NextResponse("Not Found", { status: 404 });
    }

    if (
      existingAnnouncement.authorId !== session.user.id &&
      session.user.role !== "ADMIN"
    ) {
      return new NextResponse("Forbidden", { status: 403 });
    }

    await db.announcement.delete({
      where: {
        id: params.announcementId,
      },
    });

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error("[ANNOUNCEMENT_DELETE]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}
