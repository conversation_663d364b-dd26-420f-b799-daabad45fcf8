import Link from "next/link";
import Image from "next/image";
import { ArrowUpRight } from "lucide-react";
import { CategoryItem } from "@/app/(dashboard)/(routes)/student/search/_components/category-item";
import { colorMap } from "@/styles/color-map";
import { formatDate } from "@/lib/utils";
import { User } from "@prisma/client";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";

interface BlogCardProps {
  id: string;
  title: string;
  author: User;
  imageUrl: string;
  description: string;
  createdAt: Date;
  categories: string[];
}

export const BlogCard = ({
  id,
  title,
  author,
  createdAt,
  imageUrl,
  description,
  categories,
}: BlogCardProps) => {
  return (
    <Link href={`/blogs/${id}`}>
      <div className="flex flex-col gap-2 group hover:shadow-lg transition rounded-xl h-[360px] border border-neutral-800 bg-gray-700/10">
        {/* Image Section */}
        <div className="relative w-full aspect-video rounded-t-xl overflow-hidden">
          <Image
            fill
            className="object-cover"
            alt={title}
            src={imageUrl}
            placeholder="blur"
            blurDataURL="/images/placeholder.png"
          />
        </div>

        {/* Content Section */}
        <div className="px-4 pb-4 pt-2 flex flex-col w-full h-full">
          {/* Categories + Date */}
          <div className="w-full flex justify-between mb-3 items-start gap-0">
            <div className="flex flex-wrap gap-1 justify-start">
              {categories.map((cat) => (
                <CategoryItem key={cat} label={cat} color={colorMap[cat]} />
              ))}
            </div>
            <div className="text-sm dark:text-gray-400 text-gray-600 justify-end text-right">
              {formatDate(String(createdAt))}
            </div>
          </div>

          {/* Title */}
          <div className="mb-2">
            <div className="w-full flex justify-between items-center">
              <h3 className="text-lg font-semibold dark:text-white group-hover:text-orange-500 transition">
                {title}
              </h3>
              <ArrowUpRight className="h-5 w-5 dark:text-gray-400 text-gray-600 group-hover:text-orange-500 transition" />
            </div>
          </div>

          {/* Description */}
          <p className="text-sm dark:text-gray-400 text-gray-600 line-clamp-3 h-14">
            {description}
          </p>

          {/* Spacer to align content */}
          <div className="flex-grow mt-4"></div>

          {/* Author Section */}
          <div className="flex items-center gap-3">
            {author.image ? (
              <Avatar>
                <AvatarImage
                  src={author.image}
                  alt={author.firstName || ""}
                  className="object-cover rounded-3xl border dark:border-orange-400 border-orange-600"
                />
                <AvatarFallback>{author.firstName}</AvatarFallback>
              </Avatar>
            ) : (
              <div className="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center dark:text-gray-400 text-gray-600 first-letter:text-sm">
                {author.firstName?.[0] ?? ""}
                {author.lastName?.[0] ?? ""}
              </div>
            )}
            <div>
              <div className="text-sm dark:text-gray-300 text-gray-700  flex flex-col gap-1">
                {author.firstName} {author.lastName}
              </div>
              <div className="text-xs text-gray-400">@{author.username}</div>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default BlogCard;
