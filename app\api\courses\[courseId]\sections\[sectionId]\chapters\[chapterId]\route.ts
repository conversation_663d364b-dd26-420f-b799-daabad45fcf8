import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/lib/db";
import Mux from "@mux/mux-node";

const mux = new Mux({
  tokenId: process.env.MUX_TOKEN_ID,
  tokenSecret: process.env.MUX_TOKEN_SECRET,
});

export async function DELETE(
  req: Request,
  {
    params,
  }: { params: { courseId: string; sectionId: string; chapterId: string } }
) {
  try {
    const session = await auth();
    const userId = session?.user.id;

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Verify if the user owns the course
    const ownCourse = await db.course.findUnique({
      where: {
        id: params.courseId,
        userId,
      },
    });

    if (!ownCourse) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Fetch the chapter and ensure it belongs to the correct section and course
    const chapter = await db.chapter.findUnique({
      where: {
        id: params.chapterId,
        section: {
          courseId: params.courseId,
          id: params.sectionId,
        },
      },
      include: {
        attachments: true,
        muxData: true,
      },
    });

    if (!chapter) {
      return new NextResponse("Not Found", { status: 404 });
    }

    // Clean up Mux data if the chapter has a video
    if (chapter.videoUrl && chapter.muxData) {
      await mux.video.assets.delete(chapter.muxData.assetId);
      await db.muxData.delete({
        where: {
          id: chapter.muxData.id,
        },
      });
    }

    // Delete all attachments associated with the chapter
    if (chapter.attachments.length > 0) {
      await db.attachment.deleteMany({
        where: {
          chapterId: params.chapterId,
        },
      });
    }

    // Delete the chapter
    const deletedChapter = await db.chapter.delete({
      where: {
        id: params.chapterId,
      },
    });

    // Check if there are any published chapters left in the course
    const publishedChaptersInCourse = await db.chapter.findMany({
      where: {
        section: {
          courseId: params.courseId,
        },
        isPublished: true,
      },
    });

    // If no published chapters remain, unpublish the course
    if (!publishedChaptersInCourse.length) {
      await db.course.update({
        where: {
          id: params.courseId,
        },
        data: {
          isPublished: false,
        },
      });
    }

    return NextResponse.json(deletedChapter);
  } catch (error) {
    console.log("[CHAPTER_ID_DELETE]: ", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}

export async function PATCH(
  req: Request,
  {
    params,
  }: { params: { courseId: string; sectionId: string; chapterId: string } }
) {
  try {
    const session = await auth();
    const userId = session?.user.id;

    // Parse request data
    const { isPublished, attachments, ...values } = await req.json();

    // Check if user is authenticated
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Verify if the user owns the course
    const ownCourse = await db.course.findUnique({
      where: {
        id: params.courseId,
        userId,
      },
    });

    if (!ownCourse) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Prepare update data
    const updateData: any = {
      ...values,
    };

    // If videoUrl is an object, extract the url property
    if (values.videoUrl && typeof values.videoUrl === "object") {
      updateData.videoUrl = values.videoUrl.url;
    }

    // Update chapter information in the database
    const chapter = await db.chapter.update({
      where: {
        id: params.chapterId,
        section: {
          courseId: params.courseId,
          id: params.sectionId,
        },
      },
      data: updateData,
    });

    // Handle video asset creation/update
    if (values.videoUrl) {
      // Check if there's already Mux data for this chapter
      const existingMuxData = await db.muxData.findFirst({
        where: {
          chapterId: params.chapterId,
        },
      });

      // If there is existing Mux data, delete the asset and the Mux data from DB
      if (existingMuxData) {
        await mux.video.assets.delete(existingMuxData.assetId);
        await db.muxData.delete({
          where: {
            id: existingMuxData.id,
          },
        });
      }

      // Create a new asset in Mux
      const asset = await mux.video.assets.create({
        input: values.videoUrl,
        playback_policy: ["public"],
        max_resolution_tier: "1080p",
      });

      // Save the Mux asset data in the database
      await db.muxData.create({
        data: {
          chapterId: params.chapterId,
          assetId: asset.id,
          playbackId: asset.playback_ids?.[0]?.id,
        },
      });
    }

    // Handle attachments if provided
    if (attachments) {
      // First delete all existing attachments
      await db.attachment.deleteMany({
        where: {
          chapterId: params.chapterId,
        },
      });

      // Then create new attachments
      if (attachments.length > 0) {
        await db.attachment.createMany({
          data: attachments.map((attachment: any) => ({
            ...attachment,
            chapterId: params.chapterId,
          })),
        });
      }
    }

    return NextResponse.json(chapter);
  } catch (error) {
    console.log("[COURSES_CHAPTER_ID]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}
