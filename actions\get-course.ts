import { Category, Course, User, Like } from "@prisma/client";
import { db } from "@/lib/db";

type CourseWithCategory = Course & {
  categories: Category[];
};

export const getCourse = async (
  courseId: string
): Promise<CourseWithCategory | null> => {
  try {
    const course = await db.course.findUnique({
      where: {
        id: courseId,
      },
      include: {
        categories: true,
      },
    });

    if (!course) {
      return null;
    }

    return course;
  } catch (error) {
    console.log("[GET_COURSE]", error);
    return null;
  }
};
