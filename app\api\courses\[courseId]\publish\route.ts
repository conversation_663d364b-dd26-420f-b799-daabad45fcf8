import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/lib/db";

export async function PATCH(
  req: Request,
  { params }: { params: { courseId: string } }
) {
  try {
    const session = await auth();
    const userId = session?.user.id;

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Find course with sections and their chapters
    const course = await db.course.findUnique({
      where: {
        id: params.courseId,
        userId,
      },
      include: {
        sections: {
          include: {
            chapters: {
              where: {
                isPublished: true,
              },
              include: {
                muxData: true,
              },
            },
          },
        },
        categories: true,
      },
    });

    if (!course) {
      return new NextResponse("Course not found", { status: 404 });
    }

    // Check for required fields
    const missingFields = [];
    if (!course.title) missingFields.push("title");
    if (!course.description) missingFields.push("description");
    if (!course.imageUrl) missingFields.push("imageUrl");
    if (course.categories.length === 0)
      missingFields.push("at least one category");

    // Check for published content
    const hasPublishedContent = course.sections.some((section) =>
      section.chapters.some((chapter) => chapter.isPublished)
    );

    if (!hasPublishedContent) {
      missingFields.push("at least one published chapter");
    }

    if (missingFields.length > 0) {
      return new NextResponse(
        `Missing required fields: ${missingFields.join(", ")}`,
        { status: 400 }
      );
    }

    // Publish the course
    const publishedCourse = await db.course.update({
      where: {
        id: params.courseId,
        userId,
      },
      data: {
        isPublished: true,
      },
    });

    return NextResponse.json(publishedCourse);
  } catch (error) {
    console.error("[COURSE_PUBLISH_ERROR]", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
