import type { NextAuthConfig } from "next-auth";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";
import { LoginSchema } from "@/schemas";
import { getUserByEmail } from "@/data/user";
import Google from "next-auth/providers/google";
import bcrypt from "bcryptjs";

export default {
  providers: [
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    }),
    CredentialsProvider({
      async authorize(credentials) {
        const validatedFields = LoginSchema.safeParse(credentials);

        if (validatedFields.success) {
          const { email, password } = validatedFields.data;
          const user = await getUserByEmail(email);

          if (!user || !user.password) return null;

          const passwordMatch = await bcrypt.compare(password, user.password);

          if (passwordMatch) {
            return {
              id: user.id,
              role: user.role ?? "USER",
              email: user.email ?? "",
              username: user.username ?? undefined,
              bio: user.bio ?? undefined,
              phone: user.phone ?? undefined,
              firstName: user.firstName ?? undefined,
              lastName: user.lastName ?? undefined,
              emailVerified: user.emailVerified ?? undefined,
              isTwoFactorEnabled: user.isTwoFactorEnabled,
            };
          }
        }

        return null;
      },
    }),
  ],
} satisfies NextAuthConfig;
