import { BlockMath, InlineMath } from "react-katex";

export const Exercices = [
  {
    enonce: "\\text{salam cv ?}",
    hint: "Sessions en Direct Interactives",
    solution:
      "Des sessions en direct où les étudiants peuvent interagir avec les instructeurs en temps réel, poser des questions et participer à des discussions.",
  },
  {
    enonce:
      "Soit <BlockMath>f</BlockMath> une fonction définie par : <BlockMath>f(x) = dfrac{1}{(x² + 3x + 2)³}</BlockMath> <br/> <BlockMath>\\begin{enumerate} item[1.] How do you feel \\end{enumerate}</BlockMath>",
    hint: "Sessions en Direct Interactives",
    solution:
      "Des sessions en direct où les étudiants peuvent interagir avec les instructeurs en temps réel, poser des questions et participer à des discussions.",
  },
  {
    enonce:
      "Soit <BlockMath>f</BlockMath> une fonction définie par : <BlockMath>f(x) = dfrac{1}{(x² + 3x + 2)³}</BlockMath> <br/> <BlockMath>\\begin{enumerate} item[1.] How do you feel \\end{enumerate}</BlockMath>",
    hint: "Sessions en Direct Interactives",
    solution:
      "Des sessions en direct où les étudiants peuvent interagir avec les instructeurs en temps réel, poser des questions et participer à des discussions.",
  },
];
