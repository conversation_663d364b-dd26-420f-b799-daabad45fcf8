"use client";

import { useState } from "react";
import * as z from "zod";
import axios from "axios";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { PlusCircle, X } from "lucide-react";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Chapter, Section } from "@prisma/client";
import { ChaptersList } from "./chapters-list";

interface ChaptersFormProps {
  initialData: Section & { chapters: Chapter[] };
  courseId: string;
  sectionId: string;
}

const formSchema = z.object({
  title: z.string().min(1, { message: "Un titre est requis" }),
});

export const ChaptersForm = ({
  initialData,
  courseId,
  sectionId,
}: ChaptersFormProps) => {
  const router = useRouter();
  const { toast } = useToast();
  const [isCreating, setIsCreating] = useState(false);

  const toggleCreating = () => setIsCreating((current) => !current);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: "",
    },
  });

  const { isSubmitting, isValid } = form.formState;

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      const nextPosition = initialData.chapters.length;

      await axios.post(
        `/api/courses/${courseId}/sections/${sectionId}/chapters`,
        {
          ...values,
          position: nextPosition,
        }
      );

      toast({
        title: "Chapitre créé",
        description: "Le nouveau chapitre a été ajouté avec succès",
      });
      toggleCreating();
      router.refresh();
    } catch {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la création du chapitre",
        variant: "destructive",
      });
    }
  };

  const onReorder = async (updateData: { id: string; position: number }[]) => {
    try {
      await axios.put(
        `/api/courses/${courseId}/sections/${sectionId}/chapters/reorder`,
        {
          list: updateData,
        }
      );
      toast({
        title: "Ordre mis à jour",
        description: "L'ordre des chapitres a été modifié",
      });
      router.refresh();
    } catch {
      toast({
        title: "Erreur",
        description: "Impossible de réorganiser les chapitres",
        variant: "destructive",
      });
    }
  };

  const onEdit = (id: string) => {
    router.push(
      `/teacher/courses/${courseId}/sections/${sectionId}/chapters/${id}`
    );
  };

  return (
    <div className="flex gap-4 w-full max-sm:flex-col max-sm:items-start">
      <Button
        variant="outline"
        onClick={toggleCreating}
        className="flex items-center gap-2"
      >
        {isCreating ? (
          <>
            <X className="h-4 w-4" />
            Annuler
          </>
        ) : (
          <>
            <PlusCircle className="h-4 w-4" />
            Ajouter un chapitre
          </>
        )}
      </Button>

      <div className="w-full space-y-4">
        {isCreating && (
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="w-full flex gap-4 items-center max-sm:flex-col max-sm:items-end"
            >
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormControl>
                      <Input
                        disabled={isSubmitting}
                        placeholder="ex : Introduction au cours"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button type="submit" disabled={!isValid || isSubmitting}>
                Créer
              </Button>
            </form>
          </Form>
        )}

        {!isCreating && (
          <div className="text-sm mb-2">
            {initialData.chapters.length === 0 ? (
              <p className="text-slate-500 italic">Aucun chapitre</p>
            ) : (
              <ChaptersList
                onEdit={onEdit}
                onReorder={onReorder}
                items={initialData.chapters}
              />
            )}
          </div>
        )}

        {!isCreating && initialData.chapters.length > 0 && (
          <p className="text-xs text-muted-foreground mt-4">
            Glissez-déposez pour réorganiser les chapitres
          </p>
        )}
      </div>
    </div>
  );
};
