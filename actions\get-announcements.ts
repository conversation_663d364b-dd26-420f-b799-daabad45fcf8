import { db } from "@/lib/db";
import { Announcement, AnnouncementType, AnnouncementPriority, User } from "@prisma/client";

export type AnnouncementWithAuthor = Announcement & {
  author: User;
};

interface GetAnnouncementsOptions {
  published?: boolean;
  targetAudience?: string;
  type?: AnnouncementType;
  priority?: AnnouncementPriority;
  limit?: number;
  includeExpired?: boolean;
}

export const getAnnouncements = async (
  options: GetAnnouncementsOptions = {}
): Promise<AnnouncementWithAuthor[]> => {
  const {
    published = true,
    targetAudience,
    type,
    priority,
    limit,
    includeExpired = false,
  } = options;

  try {
    const whereClause: any = {};

    if (published !== undefined) {
      whereClause.isPublished = published;
    }

    if (targetAudience) {
      whereClause.OR = [
        { targetAudience: "ALL" },
        { targetAudience: targetAudience },
        { targetAudience: { contains: targetAudience } },
      ];
    }

    if (type) {
      whereClause.type = type;
    }

    if (priority) {
      whereClause.priority = priority;
    }

    if (!includeExpired) {
      whereClause.OR = [
        { expiresAt: null },
        { expiresAt: { gt: new Date() } },
      ];
    }

    const announcements = await db.announcement.findMany({
      where: whereClause,
      include: {
        author: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            image: true,
            role: true,
          },
        },
      },
      orderBy: [
        { isPinned: "desc" },
        { priority: "desc" },
        { createdAt: "desc" },
      ],
      take: limit,
    });

    return announcements;
  } catch (error) {
    console.error("[GET_ANNOUNCEMENTS]", error);
    return [];
  }
};

export const getAnnouncementById = async (
  id: string
): Promise<AnnouncementWithAuthor | null> => {
  try {
    const announcement = await db.announcement.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            image: true,
            role: true,
          },
        },
      },
    });

    return announcement;
  } catch (error) {
    console.error("[GET_ANNOUNCEMENT_BY_ID]", error);
    return null;
  }
};

export const getPublishedAnnouncements = async (
  targetAudience: string = "ALL",
  limit?: number
): Promise<AnnouncementWithAuthor[]> => {
  return getAnnouncements({
    published: true,
    targetAudience,
    limit,
    includeExpired: false,
  });
};
