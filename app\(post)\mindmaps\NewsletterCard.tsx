import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Send } from "lucide-react";

export function NewsletterCard() {
  return (
    <div className="flex flex-col justify-between p-10 max-md:p-6 max-md:gap-6 max border border-zinc-800 rounded-2xl shadow-sm bg-zinc-900">
      <div className="bg-background p-4 border border-zinc-800 rounded-2xl w-fit">
        <Send width={35} height={35} />
      </div>
      <div className="flex flex-col gap-2">
        <h3 className="dash-text">Weekly Tips</h3>
        <p className="text-left p-text">
          Re<PERSON>vez chaque semaine des astuces pratiques et des conseils pour
          améliorer vos compétences en maths et physique. Inscrivez-vous avec
          votre email pour ne manquer aucune mise à jour!
        </p>
      </div>
      <div className="mt-4 flex space-x-2">
        <Input type="email" placeholder="Email" />
        <Button type="submit">Subscribe</Button>
      </div>
    </div>
  );
}
