"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { 
  BookOpen, 
  Play, 
  CheckCircle, 
  Clock, 
  FileText, 
  Video,
  Lock,
  Download
} from "lucide-react";

interface Lesson {
  id: string;
  title: string;
  type: "video" | "reading" | "quiz" | "assignment";
  duration: string;
  isCompleted: boolean;
  isLocked: boolean;
  description?: string;
}

interface Chapter {
  id: string;
  title: string;
  description: string;
  totalLessons: number;
  completedLessons: number;
  duration: string;
  lessons: Lesson[];
}

interface Section {
  id: string;
  title: string;
  description: string | null;
  position: number;
  chapters: any[];
}

interface SyllabusLessonsProps {
  chapters: Chapter[] | Section[];
  courseProgress: number;
}

const SyllabusL<PERSON>ons = ({ chapters, courseProgress }: SyllabusLessonsProps) => {
  const [openChapters, setOpenChapters] = useState<string[]>(["chapter-1"]);

  const getLessonIcon = (type: string, isCompleted: boolean, isLocked: boolean) => {
    if (isLocked) return <Lock className="w-4 h-4 text-gray-400" />;
    if (isCompleted) return <CheckCircle className="w-4 h-4 text-green-500" />;
    
    switch (type) {
      case "video": return <Video className="w-4 h-4 text-blue-500" />;
      case "reading": return <FileText className="w-4 h-4 text-purple-500" />;
      case "quiz": return <BookOpen className="w-4 h-4 text-orange-500" />;
      case "assignment": return <Download className="w-4 h-4 text-red-500" />;
      default: return <Play className="w-4 h-4 text-gray-500" />;
    }
  };

  const getLessonTypeLabel = (type: string) => {
    switch (type) {
      case "video": return "Vidéo";
      case "reading": return "Lecture";
      case "quiz": return "Quiz";
      case "assignment": return "Devoir";
      default: return "Leçon";
    }
  };

  const getLessonTypeBadgeColor = (type: string) => {
    switch (type) {
      case "video": return "bg-blue-100 text-blue-800 dark:bg-blue-950/20 dark:text-blue-400";
      case "reading": return "bg-purple-100 text-purple-800 dark:bg-purple-950/20 dark:text-purple-400";
      case "quiz": return "bg-orange-100 text-orange-800 dark:bg-orange-950/20 dark:text-orange-400";
      case "assignment": return "bg-red-100 text-red-800 dark:bg-red-950/20 dark:text-red-400";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-950/20 dark:text-gray-400";
    }
  };

  // Dummy data
  const dummyChapters: Chapter[] = [
    {
      id: "chapter-1",
      title: "Introduction aux Mathématiques",
      description: "Concepts fondamentaux et bases théoriques",
      totalLessons: 5,
      completedLessons: 5,
      duration: "2h 30min",
      lessons: [
        {
          id: "lesson-1-1",
          title: "Qu'est-ce que les mathématiques ?",
          type: "video",
          duration: "15min",
          isCompleted: true,
          isLocked: false,
          description: "Introduction générale aux mathématiques"
        },
        {
          id: "lesson-1-2",
          title: "Histoire des mathématiques",
          type: "reading",
          duration: "20min",
          isCompleted: true,
          isLocked: false,
          description: "Évolution historique des concepts mathématiques"
        },
        {
          id: "lesson-1-3",
          title: "Systèmes de numération",
          type: "video",
          duration: "25min",
          isCompleted: true,
          isLocked: false
        },
        {
          id: "lesson-1-4",
          title: "Quiz: Concepts de base",
          type: "quiz",
          duration: "10min",
          isCompleted: true,
          isLocked: false
        },
        {
          id: "lesson-1-5",
          title: "Exercices pratiques",
          type: "assignment",
          duration: "30min",
          isCompleted: true,
          isLocked: false
        }
      ]
    },
    {
      id: "chapter-2",
      title: "Algèbre Fondamentale",
      description: "Équations, inéquations et fonctions de base",
      totalLessons: 6,
      completedLessons: 3,
      duration: "3h 15min",
      lessons: [
        {
          id: "lesson-2-1",
          title: "Variables et expressions",
          type: "video",
          duration: "20min",
          isCompleted: true,
          isLocked: false
        },
        {
          id: "lesson-2-2",
          title: "Résolution d'équations linéaires",
          type: "video",
          duration: "30min",
          isCompleted: true,
          isLocked: false
        },
        {
          id: "lesson-2-3",
          title: "Systèmes d'équations",
          type: "reading",
          duration: "25min",
          isCompleted: true,
          isLocked: false
        },
        {
          id: "lesson-2-4",
          title: "Fonctions linéaires",
          type: "video",
          duration: "35min",
          isCompleted: false,
          isLocked: false
        },
        {
          id: "lesson-2-5",
          title: "Quiz: Algèbre de base",
          type: "quiz",
          duration: "15min",
          isCompleted: false,
          isLocked: false
        },
        {
          id: "lesson-2-6",
          title: "Projet: Modélisation algébrique",
          type: "assignment",
          duration: "45min",
          isCompleted: false,
          isLocked: false
        }
      ]
    },
    {
      id: "chapter-3",
      title: "Géométrie Euclidienne",
      description: "Formes, angles et théorèmes géométriques",
      totalLessons: 7,
      completedLessons: 0,
      duration: "4h 00min",
      lessons: [
        {
          id: "lesson-3-1",
          title: "Points, lignes et plans",
          type: "video",
          duration: "25min",
          isCompleted: false,
          isLocked: true
        },
        {
          id: "lesson-3-2",
          title: "Angles et leurs propriétés",
          type: "video",
          duration: "30min",
          isCompleted: false,
          isLocked: true
        },
        {
          id: "lesson-3-3",
          title: "Triangles et quadrilatères",
          type: "reading",
          duration: "35min",
          isCompleted: false,
          isLocked: true
        },
        {
          id: "lesson-3-4",
          title: "Théorème de Pythagore",
          type: "video",
          duration: "40min",
          isCompleted: false,
          isLocked: true
        },
        {
          id: "lesson-3-5",
          title: "Cercles et leurs propriétés",
          type: "video",
          duration: "30min",
          isCompleted: false,
          isLocked: true
        },
        {
          id: "lesson-3-6",
          title: "Quiz: Géométrie de base",
          type: "quiz",
          duration: "20min",
          isCompleted: false,
          isLocked: true
        },
        {
          id: "lesson-3-7",
          title: "Construction géométrique",
          type: "assignment",
          duration: "60min",
          isCompleted: false,
          isLocked: true
        }
      ]
    }
  ];

  // Transform sections to chapters format if needed
  const transformedChapters = chapters.length > 0 && 'chapters' in chapters[0]
    ? (chapters as Section[]).map((section, index) => ({
        id: section.id,
        title: section.title,
        description: section.description || "",
        totalLessons: section.chapters.length,
        completedLessons: 0, // Will be calculated from user progress
        duration: "2h 30min", // Default duration
        lessons: section.chapters.map((chapter: any, chapterIndex: number) => ({
          id: chapter.id,
          title: chapter.title,
          type: "video" as const,
          duration: "15min",
          isCompleted: false,
          isLocked: false,
          description: chapter.description || ""
        }))
      }))
    : chapters as Chapter[];

  const displayChapters = transformedChapters.length > 0 ? transformedChapters : dummyChapters;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BookOpen className="w-5 h-5 text-orange-500" />
          Programme & Leçons
        </CardTitle>
        <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-300">
          <span>{displayChapters.length} chapitres</span>
          <span>•</span>
          <span>{displayChapters.reduce((acc, ch) => acc + ch.totalLessons, 0)} leçons</span>
          <span>•</span>
          <span>{displayChapters.reduce((acc, ch) => acc + ch.completedLessons, 0)} terminées</span>
        </div>
      </CardHeader>
      <CardContent>
        <Accordion type="multiple" value={openChapters} onValueChange={setOpenChapters}>
          {displayChapters.map((chapter, index) => {
            const chapterProgress = (chapter.completedLessons / chapter.totalLessons) * 100;
            
            return (
              <AccordionItem key={chapter.id} value={chapter.id}>
                <AccordionTrigger className="hover:no-underline">
                  <div className="flex items-center justify-between w-full mr-4">
                    <div className="flex items-center gap-4">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                        chapterProgress === 100 
                          ? "bg-green-500 text-white" 
                          : chapterProgress > 0 
                            ? "bg-orange-500 text-white"
                            : "bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-300"
                      }`}>
                        {chapterProgress === 100 ? (
                          <CheckCircle className="w-4 h-4" />
                        ) : (
                          index + 1
                        )}
                      </div>
                      <div className="text-left">
                        <h3 className="font-semibold text-gray-900 dark:text-white">
                          {chapter.title}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-300">
                          {chapter.description}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <span>{chapter.duration}</span>
                      <div className="w-20">
                        <Progress value={chapterProgress} className="h-2" />
                      </div>
                      <span>{chapter.completedLessons}/{chapter.totalLessons}</span>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-2 ml-12 mt-4">
                    {chapter.lessons.map((lesson) => (
                      <div
                        key={lesson.id}
                        className={`flex items-center justify-between p-3 rounded-lg border transition-colors duration-200 ${
                          lesson.isLocked
                            ? "bg-gray-50 border-gray-200 dark:bg-gray-800/50 dark:border-gray-700"
                            : lesson.isCompleted
                              ? "bg-green-50 border-green-200 dark:bg-green-950/10 dark:border-green-800"
                              : "bg-white border-gray-200 dark:bg-neutral-800 dark:border-neutral-700 hover:bg-gray-50 dark:hover:bg-neutral-700/50 cursor-pointer"
                        }`}
                      >
                        <div className="flex items-center gap-3">
                          {getLessonIcon(lesson.type, lesson.isCompleted, lesson.isLocked)}
                          <div>
                            <h4 className={`font-medium ${
                              lesson.isLocked 
                                ? "text-gray-400" 
                                : "text-gray-900 dark:text-white"
                            }`}>
                              {lesson.title}
                            </h4>
                            {lesson.description && (
                              <p className="text-sm text-gray-600 dark:text-gray-300">
                                {lesson.description}
                              </p>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-3">
                          <Badge variant="outline" className={getLessonTypeBadgeColor(lesson.type)}>
                            {getLessonTypeLabel(lesson.type)}
                          </Badge>
                          <div className="flex items-center gap-1 text-sm text-gray-500">
                            <Clock className="w-3 h-3" />
                            <span>{lesson.duration}</span>
                          </div>
                          {!lesson.isLocked && (
                            <Button size="sm" variant={lesson.isCompleted ? "outline" : "default"}>
                              {lesson.isCompleted ? "Revoir" : "Commencer"}
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            );
          })}
        </Accordion>
      </CardContent>
    </Card>
  );
};

export default SyllabusLessons;
