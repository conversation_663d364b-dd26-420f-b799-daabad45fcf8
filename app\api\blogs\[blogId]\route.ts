import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/lib/db";

export async function DELETE(
  req: Request,
  { params }: { params: { blogId: string } }
) {
  try {
    const session = await auth();
    const userId = session?.user.id;

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const blog = await db.blog.findUnique({
      where: {
        id: params.blogId,
        userId: userId,
      },
    });

    if (!blog) {
      return new NextResponse("Not Found", { status: 404 });
    }

    const deletedBlog = await db.blog.delete({
      where: {
        id: params.blogId,
      },
    });

    return NextResponse.json(deletedBlog);
  } catch (error) {
    console.log("[BLOG_ID_DELETE]: ", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}

export async function PATCH(
  req: Request,
  { params }: { params: { blogId: string } }
) {
  const session = await auth();

  try {
    const userId = session?.user.id;
    const { blogId } = params;
    const values = await req.json();

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { categoryIds, ...rest } = values;

    const updateData: any = { ...rest };

    if (categoryIds && Array.isArray(categoryIds)) {
      updateData.categories = {
        set: categoryIds.map((id: string) => ({ id })),
      };
    }

    const blog = await db.blog.update({
      where: {
        id: blogId,
        userId,
      },
      data: updateData,
    });

    return NextResponse.json(blog);
  } catch (error) {
    console.log("[BLOG_ID]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}
