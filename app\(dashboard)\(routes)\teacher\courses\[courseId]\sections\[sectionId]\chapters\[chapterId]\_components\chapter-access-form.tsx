"use client";

import { useState } from "react";
import axios from "axios";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";

interface ChapterAccessFormProps {
  initialData: {
    isFree: boolean;
  };
  courseId: string;
  sectionId: string;
  chapterId: string;
}

export const ChapterAccessForm = ({
  initialData,
  courseId,
  sectionId,
  chapterId,
}: ChapterAccessFormProps) => {
  const router = useRouter();
  const { toast } = useToast();
  const [isFree, setIsFree] = useState(initialData.isFree);

  const onSubmit = async (value: boolean) => {
    try {
      await axios.patch(
        `/api/courses/${courseId}/sections/${sectionId}/chapters/${chapterId}`,
        { isFree: value }
      );
      toast({
        title: "Accès mis à jour",
        description: value
          ? "Ce chapitre est maintenant accessible gratuitement"
          : "Ce chapitre n'est plus accessible gratuitement",
      });
      router.refresh();
    } catch {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la mise à jour",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="w-full p-4 border border-color rounded-lg">
      <div className="flex items-center gap-x-4">
        <Switch
          checked={isFree}
          onCheckedChange={(value: boolean) => {
            setIsFree(value);
            onSubmit(value);
          }}
        />
        <p className="text-base">
          {isFree
            ? "Ce chapitre est gratuit en prévisualisation."
            : "Ce chapitre n'est pas gratuit en prévisualisation."}
        </p>
      </div>
    </div>
  );
};
