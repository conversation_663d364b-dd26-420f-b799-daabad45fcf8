import Image from "next/image";

interface CourseProps {
  image: string;
  tags: string[][];
  title: string;
  desc: string;
  name: string;
  date: string;
}

const Blog: React.FC<CourseProps> = ({
  image,
  tags,
  title,
  desc,
  name,
  date,
}) => {
  return (
    <div className="flex flex-col items-start max-h-[580px] min-w-[380px] gap-0 flex-1 ">
      <Image src={image} alt={image} width={1000} height={100} />

      <p className="text-orange-600 dark:text-orange-400 mt-5 text-md font-semibold leading-tight">
        {name} • {date}
      </p>

      <div className="flex mt-2 w-full items-center justify-between">
        <h1 className="text-gray-900 dark:text-white text-2xl font-semibold">
          {title}
        </h1>
        <Image
          src="/assets/icons/arrow-up-right.svg"
          alt="arrow"
          height={12}
          width={12}
        />
      </div>

      <p className="desc-text mt-2 dark:text-gray-300 text-left max-w-[400px]">
        {desc}
      </p>

      <div className="flex gap-3 mt-6">
        {tags.map((element, index) => {
          const tagClasses = element.slice(1).join(" ");
          console.log(tagClasses);

          return (
            <div
              key={index}
              className={`flex gap-2 px-2 py-0.5 rounded-md shadow border border-gray-300 justify-start items-center`}
            >
              <div className={`w-2 h-2 rounded-full ${tagClasses}`}></div>
              {element[0]}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default Blog;
