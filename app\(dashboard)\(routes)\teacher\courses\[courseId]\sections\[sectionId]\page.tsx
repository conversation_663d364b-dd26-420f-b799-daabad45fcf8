import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { db } from "@/lib/db";
import Link from "next/link";
import { ArrowLeft, BookOpen, FileText, ListTodo } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import DashboardPageTitle from "@/app/(dashboard)/_components/dashboard-page-title";
import { DashboardSection } from "@/app/(dashboard)/_components/dashboard-section";
import { SectionTitleForm } from "./_components/section-title-form";
import { SectionDescriptionForm } from "./_components/section-description-form";
import { SectionActions } from "./_components/section-actions";
import { ChaptersForm } from "./_components/chapters-form";

const SectionIdPage = async ({
  params,
}: {
  params: { courseId: string; sectionId: string };
}) => {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return redirect("/");
  }

  const section = await db.section.findUnique({
    where: {
      id: params.sectionId,
      courseId: params.courseId,
    },
    include: {
      chapters: {
        orderBy: {
          position: "asc",
        },
      },
    },
  });

  if (!section) {
    return redirect("/");
  }

  const requiredFields = [
    section.title,
    section.description,
    section.chapters.some((chapter) =>
      section.chapters.some((chapter) => chapter.isPublished)
    ),
  ];

  const totalFields = requiredFields.length;
  const completedFields = requiredFields.filter(Boolean).length;
  const completionText = `(${completedFields}/${totalFields})`;
  const isComplete = requiredFields.every(Boolean);

  return (
    <div className="w-full h-[100vh] max-md:h-full z-20 flex flex-col">
      <Link
        href={`/teacher/courses/${params.courseId}`}
        className="flex items-center text-sm hover:opacity-75 transition mb-6"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Retour à la configuration du cours
      </Link>

      <div className="flex items-start justify-between gap-y-4 max-2xl:flex-col">
        <DashboardPageTitle
          title="Gestion de la section"
          description={`Champs complétés ${completionText}`}
        />
        <SectionActions
          disabled={!isComplete}
          courseId={params.courseId}
          sectionId={params.sectionId}
          isPublished={section.isPublished}
        />
      </div>

      <Separator className="mt-6 mb-8" />

      <div className="w-full h-full flex items-start justify-start gap-12 max-xl:flex-col max-sm:gap-6">
        {/* Colonne de gauche */}
        <div className="w-full flex flex-col grow gap-2 justify-between">
          <div className="flex flex-col gap-12">
            {/* Titre de la section */}
            <div className="flex flex-col gap-4">
              <DashboardSection
                icon={BookOpen}
                title="Titre de la section"
                description="Définissez un titre clair pour cette section"
              />
              <SectionTitleForm
                initialData={section}
                courseId={params.courseId}
                sectionId={params.sectionId}
              />
            </div>

            {/* Description de la section */}
            <div className="flex flex-col gap-4">
              <DashboardSection
                icon={FileText}
                title="Description de la section"
                description="Rédigez une description détaillée"
              />
              <SectionDescriptionForm
                initialData={section}
                courseId={params.courseId}
                sectionId={params.sectionId}
              />
            </div>
          </div>
        </div>

        {/* Colonne de droite */}
        <div className="w-full flex flex-col gap-2">
          {/* Chapitres */}
          <div className="flex flex-col gap-4">
            <DashboardSection
              icon={ListTodo}
              title="Chapitres"
              description="Organisez les chapitres de cette section"
            />
            <ChaptersForm
              initialData={section}
              courseId={params.courseId}
              sectionId={params.sectionId}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SectionIdPage;
