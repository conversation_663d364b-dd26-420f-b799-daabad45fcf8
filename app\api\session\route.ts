import { db } from "@/lib/db";
import { NextResponse } from "next/server";

export async function POST(req: Request) {
  const { action, userId } = await req.json();

  if (action === "start") {
    const newSession = await db.session.create({
      data: { userId, startTime: new Date() },
    });
    return NextResponse.json({
      message: "Session started",
      session: newSession,
    });
  }

  if (action === "end") {
    await db.session.updateMany({
      where: { userId, endTime: null },
      data: { endTime: new Date() },
    });
    return NextResponse.json({ message: "Session ended" });
  }

  return NextResponse.json({ error: "Invalid action" }, { status: 400 });
}
