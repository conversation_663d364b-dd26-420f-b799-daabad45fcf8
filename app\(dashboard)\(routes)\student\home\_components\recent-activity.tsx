import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Activity, BookOpen, MessageSquare, Trophy, Clock, ArrowRight } from "lucide-react";

const RecentActivity = () => {
  // Dummy data - replace with real data later
  const activities = [
    {
      id: 1,
      type: "course",
      title: "Chapitre 3 terminé",
      description: "Algèbre Linéaire - Les matrices et déterminants",
      timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
      score: 95,
      icon: BookOpen,
      color: "text-blue-600 dark:text-blue-400"
    },
    {
      id: 2,
      type: "blog",
      title: "Commentaire ajouté",
      description: "Article: 'Astuces pour résoudre les équations différentielles'",
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      icon: MessageSquare,
      color: "text-green-600 dark:text-green-400"
    },
    {
      id: 3,
      type: "achievement",
      title: "Badge obtenu",
      description: "Maître des Mathématiques - Niveau 2",
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
      icon: Trophy,
      color: "text-orange-600 dark:text-orange-400"
    },
    {
      id: 4,
      type: "test",
      title: "Quiz terminé",
      description: "Physique Mécanique - Test de révision",
      timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
      score: 87,
      icon: BookOpen,
      color: "text-purple-600 dark:text-purple-400"
    },
    {
      id: 5,
      type: "forum",
      title: "Question posée",
      description: "Comment résoudre les intégrales par parties ?",
      timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      icon: MessageSquare,
      color: "text-indigo-600 dark:text-indigo-400"
    }
  ];

  const formatTimestamp = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return "À l'instant";
    if (diffInMinutes < 60) return `Il y a ${diffInMinutes}min`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `Il y a ${diffInHours}h`;

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `Il y a ${diffInDays}j`;

    return date.toLocaleDateString('fr-FR');
  };

  const getActivityTypeLabel = (type: string) => {
    switch (type) {
      case "course": return "Cours";
      case "blog": return "Blog";
      case "achievement": return "Succès";
      case "test": return "Test";
      case "forum": return "Forum";
      default: return "Activité";
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Activity className="w-5 h-5 text-orange-500" />
            Activité Récente
          </CardTitle>
          <Button variant="ghost" size="sm">
            Voir tout
            <ArrowRight className="w-4 h-4 ml-1" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity, index) => {
            const IconComponent = activity.icon;

            return (
              <div key={activity.id} className="flex gap-4 p-3 hover:bg-gray-50 dark:hover:bg-neutral-800/50 rounded-lg transition-colors duration-200">
                <div className="flex-shrink-0">
                  <div className={`w-8 h-8 rounded-full bg-gray-100 dark:bg-neutral-800 flex items-center justify-center ${activity.color}`}>
                    <IconComponent className="w-4 h-4" />
                  </div>
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between gap-2 mb-1">
                    <h4 className="font-medium text-gray-900 dark:text-white text-sm">
                      {activity.title}
                    </h4>
                    {activity.score && (
                      <Badge variant="outline" className="text-xs">
                        {activity.score}%
                      </Badge>
                    )}
                  </div>

                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-2 line-clamp-1">
                    {activity.description}
                  </p>

                  <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                    <Badge variant="secondary" className="text-xs px-2 py-0">
                      {getActivityTypeLabel(activity.type)}
                    </Badge>
                    <Clock className="w-3 h-3" />
                    <span>{formatTimestamp(activity.timestamp)}</span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {activities.length === 0 && (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <Activity className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>Aucune activité récente</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default RecentActivity;