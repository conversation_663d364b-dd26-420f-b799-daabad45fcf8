import Image from "next/image";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { 
  ArrowLeft,
  Calendar, 
  AlertTriangle, 
  Newspaper, 
  Settings, 
  Star,
  Pin,
  Clock,
  User
} from "lucide-react";
import { AnnouncementWithAuthor } from "@/actions/get-announcements";
import { MDXContent } from "@/components/mdx-content";

interface AnnouncementDetailProps {
  announcement: AnnouncementWithAuthor;
}

export const AnnouncementDetail = ({ announcement }: AnnouncementDetailProps) => {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case "EVENT": return Calendar;
      case "NOTICE": return AlertTriangle;
      case "NEWS": return Newspaper;
      case "MAINTENANCE": return Settings;
      case "UPDATE": return Star;
      default: return Newspaper;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "EVENT": return "Événement";
      case "NOTICE": return "Avis";
      case "NEWS": return "Actualité";
      case "MAINTENANCE": return "Maintenance";
      case "UPDATE": return "Mise à jour";
      default: return "Annonce";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "HIGH": 
      case "URGENT": return "bg-red-100 text-red-800 dark:bg-red-950/20 dark:text-red-400";
      case "MEDIUM": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-950/20 dark:text-yellow-400";
      case "LOW": return "bg-blue-100 text-blue-800 dark:bg-blue-950/20 dark:text-blue-400";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-950/20 dark:text-gray-400";
    }
  };

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case "HIGH": return "Élevée";
      case "URGENT": return "Urgente";
      case "MEDIUM": return "Moyenne";
      case "LOW": return "Faible";
      default: return priority;
    }
  };

  const TypeIcon = getTypeIcon(announcement.type);
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="max-w-4xl mx-auto px-6 py-8">
      {/* Navigation */}
      <div className="mb-6">
        <Link href="/student/announcements">
          <Button variant="ghost" className="mb-4">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Retour aux annonces
          </Button>
        </Link>
      </div>

      {/* Header */}
      <Card className="mb-8">
        <CardHeader>
          <div className="flex items-start justify-between gap-4 mb-4">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-full flex items-center justify-center">
                <TypeIcon className="w-6 h-6 text-white" />
              </div>
              <div className="flex flex-col gap-2">
                <Badge variant="outline" className="w-fit">
                  {getTypeLabel(announcement.type)}
                </Badge>
                {announcement.isPinned && (
                  <Badge className="bg-orange-500 text-white w-fit">
                    <Pin className="w-3 h-3 mr-1" />
                    Épinglé
                  </Badge>
                )}
              </div>
            </div>
            <Badge variant="outline" className={getPriorityColor(announcement.priority)}>
              {getPriorityLabel(announcement.priority)}
            </Badge>
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            {announcement.title}
          </h1>
          
          <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">
            {announcement.description}
          </p>
          
          {/* Meta information */}
          <div className="flex flex-wrap items-center gap-6 text-sm text-gray-500 dark:text-gray-400 border-t pt-4">
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4" />
              <span>Publié le {formatDate(announcement.createdAt)}</span>
            </div>
            <div className="flex items-center gap-2">
              <User className="w-4 h-4" />
              <span>Par {announcement.author.firstName} {announcement.author.lastName}</span>
            </div>
            {announcement.expiresAt && (
              <div className="flex items-center gap-2 text-amber-600 dark:text-amber-400">
                <Calendar className="w-4 h-4" />
                <span>Expire le {formatDate(announcement.expiresAt)}</span>
              </div>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* Image */}
      {announcement.imageUrl && (
        <Card className="mb-8">
          <CardContent className="p-0">
            <div className="relative h-64 md:h-96 w-full overflow-hidden rounded-lg">
              <Image
                src={announcement.imageUrl}
                alt={announcement.title}
                fill
                className="object-cover"
                priority
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Content */}
      {announcement.content && (
        <Card>
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none dark:prose-invert">
              <MDXContent content={announcement.content} />
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
