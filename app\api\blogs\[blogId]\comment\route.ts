import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/auth";

export async function GET(
  req: Request,
  { params }: { params: { blogId: string } }
) {
  const session = await auth();
  const userId = session?.user.id;
  const { blogId } = params;

  try {
    const comments = await db.comment.findMany({
      where: {
        blogId: params.blogId,
        parentId: null,
      },
      include: {
        user: true,
        blog: true,
        replies: {
          include: {
            user: true,
            replies: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json(comments);
  } catch (error) {
    console.error("[GET_COMMENTS]", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

export async function POST(
  req: Request,
  { params }: { params: { blogId: string } }
) {
  try {
    const session = await auth();
    const userId = session?.user.id;
    const { blogId } = params;
    const { content, parentId } = await req.json();

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const newComment = await db.comment.create({
      data: {
        content,
        userId,
        blogId: params.blogId,
        parentId: parentId || null,
      },
      include: {
        user: true,
        blog: true,
      },
    });

    return NextResponse.json(newComment);
  } catch (error) {
    console.error("[CREATE_COMMENT]", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
