import { Badge } from "@/components/ui/badge";
import { X, Tag } from "lucide-react";

interface TagSelectorProps {
  allTags: string[];
  selectedTags: string[];
  onTagClick: (tag: string) => void;
}

export function TagSelector({
  allTags,
  selectedTags,
  onTagClick,
}: TagSelectorProps) {
  const clearAllTags = () => {
    selectedTags.forEach(tag => onTagClick(tag));
  };

  return (
    <div className="flex flex-col gap-6 w-full lg:w-[60%] self-start">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Tag className="w-5 h-5 text-orange-500" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Filtrer par tags
          </h3>
        </div>
        {selectedTags.length > 0 && (
          <button
            onClick={clearAllTags}
            className="text-sm text-gray-500 hover:text-orange-500 transition-colors duration-200 flex items-center gap-1"
          >
            <X className="w-4 h-4" />
            Effacer tout
          </button>
        )}
      </div>

      {/* Selected tags */}
      {selectedTags.length > 0 && (
        <div className="flex flex-wrap gap-2">
          <span className="text-sm text-gray-600 dark:text-gray-400 self-center">
            Sélectionnés:
          </span>
          {selectedTags.map((tag) => (
            <Badge
              key={`selected-${tag}`}
              variant="default"
              onClick={() => onTagClick(tag)}
              className="bg-orange-500 hover:bg-orange-600 text-white cursor-pointer transition-all duration-200 transform hover:scale-105 flex items-center gap-1"
            >
              {tag}
              <X className="w-3 h-3" />
            </Badge>
          ))}
        </div>
      )}

      {/* All available tags */}
      <div className="flex flex-wrap gap-3">
        {allTags.map((tag) => {
          const isSelected = selectedTags.includes(tag);
          return (
            <Badge
              key={tag}
              variant="outline"
              onClick={() => onTagClick(tag)}
              className={`cursor-pointer text-sm transition-all duration-200 transform hover:scale-105 ${
                isSelected
                  ? "bg-orange-100 border-orange-500 text-orange-700 dark:bg-orange-900/30 dark:border-orange-400 dark:text-orange-300"
                  : "hover:bg-orange-50 hover:border-orange-300 dark:hover:bg-orange-900/20 dark:hover:border-orange-400"
              }`}
            >
              {tag}
            </Badge>
          );
        })}
      </div>

      {/* Tags count */}
      <div className="text-sm text-gray-500 dark:text-gray-400">
        {selectedTags.length > 0
          ? `${selectedTags.length} tag${selectedTags.length > 1 ? 's' : ''} sélectionné${selectedTags.length > 1 ? 's' : ''}`
          : `${allTags.length} tags disponibles`
        }
      </div>
    </div>
  );
}
