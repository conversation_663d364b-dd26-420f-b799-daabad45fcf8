import { Category } from "@prisma/client";

export const colorMap: Record<Category["name"], string> = {
  "1ère Bac": "bg-purple-800 border-purple-500 text-purple-300",
  "2ème Bac": "bg-pink-800 border-pink-500 text-pink-300",
  "S.EX": "bg-indigo-800 border-indigo-500 text-indigo-300",
  "S.M": "bg-orange-800 border-orange-500 text-orange-300",
  "P.C": "bg-teal-800 border-teal-500 text-teal-300",
  SVT: "bg-red-800 border-red-500 text-red-300",
  SMA: "bg-green-800 border-green-500 text-green-300",
  SMB: "bg-yellow-800 border-yellow-500 text-yellow-300",
};
