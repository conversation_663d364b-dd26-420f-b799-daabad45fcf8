import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { db } from "@/lib/db";
import { ArrowRight } from "lucide-react";

const Teachers = async () => {
  const teachers = await db.user.findMany({
    where: { role: "TEACHER" },
  });

  return (
    <div className="w-[40%]">
      <div className="flex justify-between items-center">
        <h1 className="dashboard-section-title">Teachers</h1>
        <Button variant={"ghost"} className="text-orange-500">
          See More <ArrowRight />
        </Button>
      </div>
      <div className="mt-4 flex gap-4 items-start">
        {teachers.map((teacher) => (
          <div key={teacher.id} className="flex flex-col items-center gap-1">
            <Avatar className="w-16 h-16">
              <AvatarImage
                src={teacher.image || ""}
                alt={teacher.firstName || ""}
                className="w-full rounded-full object-cover"
              />
              <AvatarFallback>
                {teacher.firstName?.charAt(0) || ""}
              </AvatarFallback>
            </Avatar>
            <div className="dark:text-white text-black font-medium">
              {teacher.firstName} {teacher.lastName}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Teachers;
