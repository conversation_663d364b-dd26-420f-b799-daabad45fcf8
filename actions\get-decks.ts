import { db } from "@/lib/db";

export const getDecks = async (id: string) => {
  try {
    const user = await db.user.findUnique({
      where: {
        id,
      },
      include: {
        decks: true,
      },
    });

    if (!user) {
      console.error("[GET_USER] User not found");
      return null;
    }

    const decks = await db.deck.findMany({
      where: {
        userId: user.id,
      },
    });

    return decks;
  } catch (error) {
    console.error("[GET_DECKS]", error);
    return null;
  }
};
