// "use client";

// import { useState } from "react";
// import { Button } from "@/components/ui/button";
// import { Textarea } from "@/components/ui/textarea";

// interface ReplyFormProps {
//   onSubmit: (content: string) => void; // Callback to handle reply submission
//   onCancel: () => void; // Callback to hide the reply form
// }

// const ReplyForm = ({ onSubmit, onCancel }: ReplyFormProps) => {
//   const [content, setContent] = useState("");

//   const handleSubmit = (e: React.FormEvent) => {
//     e.preventDefault();
//     onSubmit(content);
//     setContent("");
//   };

//   return (
//     <form onSubmit={handleSubmit} className="mt-2">
//       <div className="flex gap-4">
//         <Textarea
//           value={content}
//           onChange={(e) => setContent(e.target.value)}
//           placeholder="Write a reply..."
//           required
//         />
//         <div className="w-fit flex flex-col gap-2">
//           <Button type="submit" variant={"default"}>
//             Reply
//           </Button>
//           <Button type="button" variant={"outline"} onClick={onCancel}>
//             Cancel
//           </Button>
//         </div>
//       </div>
//     </form>
//   );
// };

// export default ReplyForm;
