"use client";

import * as React from "react";
import { <PERSON>, AreaChart, CartesianGrid, XAxis } from "recharts";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const chartData = [
  { date: "2024-04-01", Professeurs: 222, Étudiants: 150 },
  { date: "2024-04-02", Professeurs: 97, Étudiants: 180 },
  { date: "2024-04-03", Professeurs: 167, Étudiants: 120 },
  { date: "2024-04-04", Professeurs: 242, Étudiants: 260 },
  { date: "2024-04-05", Professeurs: 373, Étudiants: 290 },
  { date: "2024-04-06", Professeurs: 301, Étudiants: 340 },
  { date: "2024-04-07", Professeurs: 245, Étudiants: 180 },
  { date: "2024-04-08", Professeurs: 409, Étudiants: 320 },
  { date: "2024-04-09", Professeurs: 59, Étudiants: 110 },
  { date: "2024-04-10", Professeurs: 261, Étudiants: 190 },
  { date: "2024-04-11", Professeurs: 327, Étudiants: 350 },
  { date: "2024-04-12", Professeurs: 292, Étudiants: 210 },
  { date: "2024-04-13", Professeurs: 342, Étudiants: 380 },
  { date: "2024-04-14", Professeurs: 137, Étudiants: 220 },
  { date: "2024-04-15", Professeurs: 120, Étudiants: 170 },
  { date: "2024-04-16", Professeurs: 138, Étudiants: 190 },
  { date: "2024-04-17", Professeurs: 446, Étudiants: 360 },
  { date: "2024-04-18", Professeurs: 364, Étudiants: 410 },
  { date: "2024-04-19", Professeurs: 243, Étudiants: 180 },
  { date: "2024-04-20", Professeurs: 89, Étudiants: 150 },
  { date: "2024-04-21", Professeurs: 137, Étudiants: 200 },
  { date: "2024-04-22", Professeurs: 224, Étudiants: 170 },
  { date: "2024-04-23", Professeurs: 138, Étudiants: 230 },
  { date: "2024-04-24", Professeurs: 387, Étudiants: 290 },
  { date: "2024-04-25", Professeurs: 215, Étudiants: 250 },
  { date: "2024-04-26", Professeurs: 75, Étudiants: 130 },
  { date: "2024-04-27", Professeurs: 383, Étudiants: 420 },
  { date: "2024-04-28", Professeurs: 122, Étudiants: 180 },
  { date: "2024-04-29", Professeurs: 315, Étudiants: 240 },
  { date: "2024-04-30", Professeurs: 454, Étudiants: 380 },
  { date: "2024-05-01", Professeurs: 165, Étudiants: 220 },
  { date: "2024-05-02", Professeurs: 293, Étudiants: 310 },
  { date: "2024-05-03", Professeurs: 247, Étudiants: 190 },
  { date: "2024-05-04", Professeurs: 385, Étudiants: 420 },
  { date: "2024-05-05", Professeurs: 481, Étudiants: 390 },
  { date: "2024-05-06", Professeurs: 498, Étudiants: 520 },
  { date: "2024-05-07", Professeurs: 388, Étudiants: 300 },
  { date: "2024-05-08", Professeurs: 149, Étudiants: 210 },
  { date: "2024-05-09", Professeurs: 227, Étudiants: 180 },
  { date: "2024-05-10", Professeurs: 293, Étudiants: 330 },
  { date: "2024-05-11", Professeurs: 335, Étudiants: 270 },
  { date: "2024-05-12", Professeurs: 197, Étudiants: 240 },
  { date: "2024-05-13", Professeurs: 197, Étudiants: 160 },
  { date: "2024-05-14", Professeurs: 448, Étudiants: 490 },
  { date: "2024-05-15", Professeurs: 473, Étudiants: 380 },
  { date: "2024-05-16", Professeurs: 338, Étudiants: 400 },
  { date: "2024-05-17", Professeurs: 499, Étudiants: 420 },
  { date: "2024-05-18", Professeurs: 315, Étudiants: 350 },
  { date: "2024-05-19", Professeurs: 235, Étudiants: 180 },
  { date: "2024-05-20", Professeurs: 177, Étudiants: 230 },
  { date: "2024-05-21", Professeurs: 82, Étudiants: 140 },
  { date: "2024-05-22", Professeurs: 81, Étudiants: 120 },
  { date: "2024-05-23", Professeurs: 252, Étudiants: 290 },
  { date: "2024-05-24", Professeurs: 294, Étudiants: 220 },
  { date: "2024-05-25", Professeurs: 201, Étudiants: 250 },
  { date: "2024-05-26", Professeurs: 213, Étudiants: 170 },
  { date: "2024-05-27", Professeurs: 420, Étudiants: 460 },
  { date: "2024-05-28", Professeurs: 233, Étudiants: 190 },
  { date: "2024-05-29", Professeurs: 78, Étudiants: 130 },
  { date: "2024-05-30", Professeurs: 340, Étudiants: 280 },
  { date: "2024-05-31", Professeurs: 178, Étudiants: 230 },
  { date: "2024-06-01", Professeurs: 178, Étudiants: 200 },
  { date: "2024-06-02", Professeurs: 470, Étudiants: 410 },
  { date: "2024-06-03", Professeurs: 103, Étudiants: 160 },
  { date: "2024-06-04", Professeurs: 439, Étudiants: 380 },
  { date: "2024-06-05", Professeurs: 88, Étudiants: 140 },
  { date: "2024-06-06", Professeurs: 294, Étudiants: 250 },
  { date: "2024-06-07", Professeurs: 323, Étudiants: 370 },
  { date: "2024-06-08", Professeurs: 385, Étudiants: 320 },
  { date: "2024-06-09", Professeurs: 438, Étudiants: 480 },
  { date: "2024-06-10", Professeurs: 155, Étudiants: 200 },
  { date: "2024-06-11", Professeurs: 92, Étudiants: 150 },
  { date: "2024-06-12", Professeurs: 492, Étudiants: 420 },
  { date: "2024-06-13", Professeurs: 81, Étudiants: 130 },
  { date: "2024-06-14", Professeurs: 426, Étudiants: 380 },
  { date: "2024-06-15", Professeurs: 307, Étudiants: 350 },
  { date: "2024-06-16", Professeurs: 371, Étudiants: 310 },
  { date: "2024-06-17", Professeurs: 475, Étudiants: 520 },
  { date: "2024-06-18", Professeurs: 107, Étudiants: 170 },
  { date: "2024-06-19", Professeurs: 341, Étudiants: 290 },
  { date: "2024-06-20", Professeurs: 408, Étudiants: 450 },
  { date: "2024-06-21", Professeurs: 169, Étudiants: 210 },
  { date: "2024-06-22", Professeurs: 317, Étudiants: 270 },
  { date: "2024-06-23", Professeurs: 480, Étudiants: 530 },
  { date: "2024-06-24", Professeurs: 132, Étudiants: 180 },
  { date: "2024-06-25", Professeurs: 141, Étudiants: 190 },
  { date: "2024-06-26", Professeurs: 434, Étudiants: 380 },
  { date: "2024-06-27", Professeurs: 448, Étudiants: 490 },
  { date: "2024-06-28", Professeurs: 149, Étudiants: 200 },
  { date: "2024-06-29", Professeurs: 103, Étudiants: 160 },
  { date: "2024-06-30", Professeurs: 446, Étudiants: 400 },
];

const chartConfig = {
  visitors: {
    label: "Visiteurs",
  },
  Professeurs: {
    label: "Professeurs",
    color: "hsl(var(--chart-1))",
  },
  Étudiants: {
    label: "Étudiants",
    color: "hsl(var(--chart-2))",
  },
} satisfies ChartConfig;

export function UserChart() {
  const [timeRange, setTimeRange] = React.useState("90d");

  const filteredData = chartData.filter((item) => {
    const date = new Date(item.date);
    const referenceDate = new Date("2024-06-30");
    let daysToSubtract = 90;
    if (timeRange === "30d") {
      daysToSubtract = 30;
    } else if (timeRange === "7d") {
      daysToSubtract = 7;
    }
    const startDate = new Date(referenceDate);
    startDate.setDate(startDate.getDate() - daysToSubtract);
    return date >= startDate;
  });

  return (
    <Card>
      <CardHeader className="flex items-center gap-2 space-y-0 border-b py-5 sm:flex-row">
        <div className="grid flex-1 gap-1 text-center sm:text-left">
          <CardTitle>Nouveaux Utilisateurs Quotidiens</CardTitle>
          <CardDescription>
            Affichant le nombre d'utilisateurs qui rejoignent la plateforme
            chaque jour au cours des 3 derniers mois
          </CardDescription>
        </div>
        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger
            className="w-[160px] rounded-lg sm:ml-auto"
            aria-label="Select a value"
          >
            <SelectValue placeholder="Last 3 months" />
          </SelectTrigger>
          <SelectContent className="rounded-xl">
            <SelectItem value="90d" className="rounded-lg">
              Dernier 3 mois
            </SelectItem>
            <SelectItem value="30d" className="rounded-lg">
              Dernier 30 jours
            </SelectItem>
            <SelectItem value="7d" className="rounded-lg">
              Dernier 7 jours
            </SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
        <ChartContainer
          config={chartConfig}
          className="aspect-auto h-[250px] w-full"
        >
          <AreaChart data={filteredData}>
            <defs>
              <linearGradient id="fillDesktop" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-Professeurs)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-Professeurs)"
                  stopOpacity={0.1}
                />
              </linearGradient>
              <linearGradient id="fillMobile" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-Étudiants)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-Étudiants)"
                  stopOpacity={0.1}
                />
              </linearGradient>
            </defs>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => {
                const date = new Date(value);
                return date.toLocaleDateString("en-US", {
                  month: "short",
                  day: "numeric",
                });
              }}
            />
            <ChartTooltip
              cursor={false}
              content={
                <ChartTooltipContent
                  labelFormatter={(value) => {
                    return new Date(value).toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                    });
                  }}
                  indicator="dot"
                />
              }
            />
            <Area
              dataKey="Étudiants"
              type="natural"
              fill="url(#fillMobile)"
              stroke="var(--color-Étudiants)"
              stackId="a"
            />
            <Area
              dataKey="Professeurs"
              type="natural"
              fill="url(#fillDesktop)"
              stroke="var(--color-Professeurs)"
              stackId="a"
            />
            <ChartLegend content={<ChartLegendContent />} />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
