import DashboardTitle from "@/app/(dashboard)/_components/dashboard-page-title";
import { Spotlight } from "@/components/ui/spotlight";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

const page = () => {
  return (
    <div className="h-[100vh] overflow-hidden dark:bg-grid-white/[0.04] bg-grid-black/[0.04] relative w-full flex flex-col items-center p-12 gap-3">
      <div className="absolute pointer-events-none inset-0 flex items-center justify-center dark:bg-black bg-white [mask-image:radial-gradient(ellipse_50%_30%_at_top,transparent_40%,black)]"></div>
      <Spotlight
        className="-top-100 left-0 md:left-60 md:-top-20 opacity-10"
        fill="orange"
      />
      <div className="w-full z-20 flex flex-col">
        <Link
          href={`/teacher/courses`}
          className="flex items-center text-sm hover:opacity-75 transition mb-6"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Retour au tableau des cours
        </Link>
        <div className="flex items-start justify-between">
          <DashboardTitle
            title="Configuration du directs"
            description={`Complétez tous les champs du directs`}
          />
        </div>
      </div>
    </div>
  );
};

export default page;
