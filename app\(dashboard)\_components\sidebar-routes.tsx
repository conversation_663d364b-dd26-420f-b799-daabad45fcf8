"use client";

import {
  <PERSON><PERSON><PERSON>,
  ChartNoAxesCombined,
  Compass,
  Layout,
  List,
  Newspaper,
  Radio,
  Settings,
  UserCog,
  MessageCircle,
  Clipboard,
  Video,
  LifeBuoy,
  HelpCircle,
  Star,
  CheckSquare,
  Users,
  Layers,
  FileText,
  PlusCircle,
  BookOpen,
  Home,
  Edit,
} from "lucide-react";
import { SidebarItem } from "./sidebar-item";

import { usePathname } from "next/navigation";

const studentRoutes = [
  {
    title: "Main Menu",
    items: [
      { icon: Layout, label: "Home", href: "/student/home" },
      { icon: Bar<PERSON><PERSON>, label: "Analytics", href: "/student/analytics" },
    ],
  },
  {
    title: "Academic",
    items: [
      { icon: List, label: "Courses", href: "/student/courses" },
      { icon: Newspaper, label: "Blogs", href: "/student/blogs" },
      { icon: MessageCircle, label: "Chat", href: "/student/chat" },
      { icon: Clipboard, label: "Tests", href: "/student/tests" },
      { icon: Video, label: "Meetings", href: "/student/meetings" },
      { icon: Layers, label: "Flashcards", href: "/student/flashcards" },
    ],
  },
  {
    title: "Other Menu",
    items: [
      { icon: LifeBuoy, label: "Help Center", href: "/student/help" },
      { icon: Settings, label: "Settings", href: "/student/settings" },
    ],
  },
];

const teacherRoutes = [
  {
    title: "Main Menu",
    items: [{ icon: Home, label: "Teacher Home", href: "/teacher/home" }],
  },
  {
    title: "Course Management",
    items: [
      { icon: BookOpen, label: "My Courses", href: "/teacher/courses" },
      {
        icon: PlusCircle,
        label: "Create Course",
        href: "/teacher/courses/create",
      },
      { icon: FileText, label: "Course Details", href: "/teacher/courses/:id" },
      {
        icon: Layers,
        label: "Manage Modules",
        href: "/teacher/courses/:id/modules",
      },
      {
        icon: Users,
        label: "Enrolled Students",
        href: "/teacher/courses/:id/students",
      },
    ],
  },
  {
    title: "Student Interactions",
    items: [
      {
        icon: MessageCircle,
        label: "Student Q&A",
        href: "/teacher/discussions",
      },
      {
        icon: CheckSquare,
        label: "Assignment Reviews",
        href: "/teacher/assignments",
      },
      { icon: Video, label: "Schedule Livestreams", href: "/teacher/meetings" },
    ],
  },
  {
    title: "Blog Management",
    items: [
      { icon: Edit, label: "Create Blog", href: "/teacher/create-blog" },
      { icon: FileText, label: "Manage Blogs", href: "/teacher/blogs" },
    ],
  },
  {
    title: "Analytics & Reports",
    items: [
      {
        icon: BarChart,
        label: "Course Analytics",
        href: "/teacher/performance",
      },
      { icon: Star, label: "Reviews & Ratings", href: "/teacher/feedback" },
    ],
  },
  {
    title: "Settings & Support",
    items: [
      {
        icon: Settings,
        label: "Profile & Preferences",
        href: "/teacher/settings",
      },
      { icon: HelpCircle, label: "Help & Support", href: "/teacher/support" },
    ],
  },
];

const adminRoutes = [
  {
    title: "Main Menu",
    items: [
      { icon: Compass, label: "Home", href: "/admin/home" },
      { icon: UserCog, label: "Users", href: "/admin/users" },
      { icon: List, label: "Courses", href: "/admin/courses" },
    ],
  },
  {
    title: "Analytics & Reports",
    items: [
      {
        icon: ChartNoAxesCombined,
        label: "Platform Reports",
        href: "/admin/reports",
      },
      { icon: Newspaper, label: "Content Moderation", href: "/admin/reviews" },
    ],
  },
  {
    title: "Financials & Monetization",
    items: [
      {
        icon: BarChart,
        label: "Payments & Revenue",
        href: "/admin/monetization",
      },
    ],
  },
  {
    title: "Settings & Support",
    items: [
      { icon: Settings, label: "General Settings", href: "/admin/settings" },
      { icon: LifeBuoy, label: "Support Tickets", href: "/admin/support" },
    ],
  },
];

export const SidebarRoutes = () => {
  const pathname = usePathname();
  const isStudent = pathname?.startsWith("/student");

  let routes = studentRoutes;

  if (pathname?.startsWith("/teacher")) {
    routes = teacherRoutes;
  } else if (pathname?.startsWith("/admin")) {
    routes = adminRoutes;
  }

  return (
    <div className="w-full flex flex-col gap-1 text-black">
      {routes.map((section) => (
        <div key={section.title} className="w-full mt-2">
          <h3 className="text-xs font-semibold text-gray-500 px-4 mb-1">
            {section.title}
          </h3>
          <div className="w-full flex flex-col gap-y-1">
            {section.items.map((route) => (
              <SidebarItem
                key={route.href}
                icon={route.icon}
                label={route.label}
                href={route.href}
              />
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};
