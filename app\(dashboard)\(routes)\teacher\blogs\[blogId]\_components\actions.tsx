"use client";

import { ConfirmModal } from "@/components/modals/confirm-modal";
import { Button } from "@/components/ui/button";
import axios from "axios";
import { Trash } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import toast from "react-hot-toast";

interface ActionsProps {
  disabled: boolean;
  blogId: string;
  isPublished: boolean;
}

export const Actions = ({ disabled, blogId, isPublished }: ActionsProps) => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const onClick = async () => {
    try {
      setIsLoading(true);
      if (isPublished) {
        await axios.patch(`/api/blogs/${blogId}/unpublish`);
        toast.success("Blog dépublié! 📝", {
          description: "Votre blog n'est plus visible publiquement",
          duration: 4000,
        });
      } else {
        await axios.patch(`/api/blogs/${blogId}/publish`);
        toast.success("Blog publié! 🚀", {
          description: "Votre blog est maintenant visible publiquement",
          duration: 4000,
        });
      }

      router.refresh();
    } catch (error) {
      toast.error("Erreur de publication", {
        description: "Impossible de modifier le statut du blog",
        duration: 4000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const onDelete = async () => {
    try {
      setIsLoading(true);
      await axios.delete(`/api/blogs/${blogId}`);
      toast.success("Blog supprimé! 🗑️", {
        description: "Le blog a été supprimé définitivement",
        duration: 4000,
      });
      router.refresh();
      router.push(`/teacher/blogs`);
    } catch (error) {
      toast.error("Erreur de suppression", {
        description: "Impossible de supprimer le blog. Veuillez réessayer.",
        duration: 4000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center gap-x-2">
      <Button
        onClick={onClick}
        disabled={disabled || isLoading}
        variant="outline"
        size="sm"
      >
        {isPublished ? "Retirer de la publication" : "publier"}
      </Button>
      <ConfirmModal onConfirm={onDelete}>
        <Button size="sm" disabled={isLoading}>
          <Trash className="h-4 w-4" />
        </Button>
      </ConfirmModal>
    </div>
  );
};
