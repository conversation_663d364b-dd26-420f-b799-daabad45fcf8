"use client";

import axios from "axios";
import { Trash } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { ConfirmModal } from "@/components/modals/confirm-modal";

interface ActionsProps {
  disabled: boolean;
  announcementId: string;
  isPublished: boolean;
}

export const Actions = ({
  disabled,
  announcementId,
  isPublished
}: ActionsProps) => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const onClick = async () => {
    try {
      setIsLoading(true);

      if (isPublished) {
        await axios.patch(`/api/announcements/${announcementId}`, { isPublished: false });
        toast.success("Annonce dépubliée! 📝", {
          description: "L'annonce n'est plus visible par les utilisateurs",
          duration: 4000,
        });
      } else {
        await axios.patch(`/api/announcements/${announcementId}`, { isPublished: true });
        toast.success("Annonce publiée! 🚀", {
          description: "L'annonce est maintenant visible par les utilisateurs",
          duration: 4000,
        });
      }

      router.refresh();
    } catch (error) {
      toast.error("Erreur de publication", {
        description: "Impossible de modifier le statut de l'annonce",
        duration: 4000,
      });
    } finally {
      setIsLoading(false);
    }
  }

  const onDelete = async () => {
    try {
      setIsLoading(true);

      await axios.delete(`/api/announcements/${announcementId}`);

      toast.success("Annonce supprimée! 🗑️", {
        description: "L'annonce a été supprimée définitivement",
        duration: 4000,
      });
      router.refresh();
      router.push(`/admin/announcements`);
    } catch (error) {
      toast.error("Erreur de suppression", {
        description: "Impossible de supprimer l'annonce. Veuillez réessayer.",
        duration: 4000,
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="flex items-center gap-x-2">
      <Button
        onClick={onClick}
        disabled={disabled || isLoading}
        variant="outline"
        size="sm"
      >
        {isPublished ? "Dépublier" : "Publier"}
      </Button>
      <ConfirmModal onConfirm={onDelete}>
        <Button size="sm" disabled={isLoading}>
          <Trash className="h-4 w-4" />
        </Button>
      </ConfirmModal>
    </div>
  )
}
