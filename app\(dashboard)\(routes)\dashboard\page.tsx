import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { getDashboardRoute } from "@/lib/role-utils";

export default async function DashboardPage() {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return redirect("/sign-in");
  }

  const userRole = session.user.role;
  const dashboardRoute = getDashboardRoute(userRole);

  return redirect(dashboardRoute);
}
