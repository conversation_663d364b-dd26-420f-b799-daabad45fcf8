"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  ArrowUpDown, 
  MoreHorizontal, 
  Pencil, 
  Eye, 
  Trash2,
  Pin,
  PinOff,
  CheckCircle,
  XCircle
} from "lucide-react";
import Link from "next/link";
import { AnnouncementWithAuthor } from "@/actions/get-announcements";
import { publishAnnouncement, deleteAnnouncement } from "@/actions/announcement";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

const ActionsCell = ({ announcement }: { announcement: AnnouncementWithAuthor }) => {
  const router = useRouter();

  const handlePublish = async () => {
    try {
      const result = await publishAnnouncement(announcement.id, !announcement.isPublished);
      if (result.success) {
        toast.success(result.success, {
          description: `L'annonce "${announcement.title}" a été ${!announcement.isPublished ? 'publiée' : 'dépubliée'}`,
          duration: 4000,
        });
        router.refresh();
      } else {
        toast.error("Erreur de publication", {
          description: result.error || "Impossible de modifier le statut de publication",
          duration: 4000,
        });
      }
    } catch (error) {
      toast.error("Erreur inattendue", {
        description: "Une erreur est survenue lors de la publication",
        duration: 4000,
      });
    }
  };

  const handleDelete = async () => {
    if (confirm("Êtes-vous sûr de vouloir supprimer cette annonce ?")) {
      try {
        const result = await deleteAnnouncement(announcement.id);
        if (result.success) {
          toast.success(result.success, {
            description: `L'annonce "${announcement.title}" a été supprimée définitivement`,
            duration: 4000,
          });
          router.refresh();
        } else {
          toast.error("Erreur de suppression", {
            description: result.error || "Impossible de supprimer l'annonce",
            duration: 4000,
          });
        }
      } catch (error) {
        toast.error("Erreur inattendue", {
          description: "Une erreur est survenue lors de la suppression",
          duration: 4000,
        });
      }
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-4 w-8 p-0">
          <span className="sr-only">Ouvrir le menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <Link href={`/admin/announcements/${announcement.id}`}>
          <DropdownMenuItem>
            <Pencil className="h-4 w-4 mr-2" />
            Modifier
          </DropdownMenuItem>
        </Link>
        <DropdownMenuItem onClick={handlePublish}>
          {announcement.isPublished ? (
            <>
              <XCircle className="h-4 w-4 mr-2" />
              Dépublier
            </>
          ) : (
            <>
              <CheckCircle className="h-4 w-4 mr-2" />
              Publier
            </>
          )}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleDelete} className="text-red-600">
          <Trash2 className="h-4 w-4 mr-2" />
          Supprimer
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export const columns: ColumnDef<AnnouncementWithAuthor>[] = [
  {
    accessorKey: "title",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Titre
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const announcement = row.original;
      return (
        <div className="flex items-center gap-2">
          {announcement.isPinned && (
            <Pin className="h-4 w-4 text-orange-500" />
          )}
          <span className="font-medium">{announcement.title}</span>
        </div>
      );
    },
  },
  {
    accessorKey: "type",
    header: "Type",
    cell: ({ row }) => {
      const type = row.getValue("type") as string;
      const typeLabels = {
        EVENT: "Événement",
        NOTICE: "Avis",
        NEWS: "Actualité",
        MAINTENANCE: "Maintenance",
        UPDATE: "Mise à jour",
      };
      
      const typeColors = {
        EVENT: "bg-blue-100 text-blue-800 dark:bg-blue-950/20 dark:text-blue-400",
        NOTICE: "bg-yellow-100 text-yellow-800 dark:bg-yellow-950/20 dark:text-yellow-400",
        NEWS: "bg-green-100 text-green-800 dark:bg-green-950/20 dark:text-green-400",
        MAINTENANCE: "bg-red-100 text-red-800 dark:bg-red-950/20 dark:text-red-400",
        UPDATE: "bg-purple-100 text-purple-800 dark:bg-purple-950/20 dark:text-purple-400",
      };

      return (
        <Badge variant="outline" className={typeColors[type as keyof typeof typeColors]}>
          {typeLabels[type as keyof typeof typeLabels]}
        </Badge>
      );
    },
  },
  {
    accessorKey: "priority",
    header: "Priorité",
    cell: ({ row }) => {
      const priority = row.getValue("priority") as string;
      const priorityLabels = {
        LOW: "Faible",
        MEDIUM: "Moyenne",
        HIGH: "Élevée",
        URGENT: "Urgente",
      };
      
      const priorityColors = {
        LOW: "bg-gray-100 text-gray-800 dark:bg-gray-950/20 dark:text-gray-400",
        MEDIUM: "bg-blue-100 text-blue-800 dark:bg-blue-950/20 dark:text-blue-400",
        HIGH: "bg-orange-100 text-orange-800 dark:bg-orange-950/20 dark:text-orange-400",
        URGENT: "bg-red-100 text-red-800 dark:bg-red-950/20 dark:text-red-400",
      };

      return (
        <Badge variant="outline" className={priorityColors[priority as keyof typeof priorityColors]}>
          {priorityLabels[priority as keyof typeof priorityLabels]}
        </Badge>
      );
    },
  },
  {
    accessorKey: "isPublished",
    header: "Statut",
    cell: ({ row }) => {
      const isPublished = row.getValue("isPublished") as boolean;
      return (
        <Badge variant={isPublished ? "default" : "secondary"}>
          {isPublished ? "Publié" : "Brouillon"}
        </Badge>
      );
    },
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Date de création
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const date = new Date(row.getValue("createdAt"));
      return date.toLocaleDateString("fr-FR");
    },
  },
  {
    id: "actions",
    cell: ({ row }) => <ActionsCell announcement={row.original} />,
  },
];
