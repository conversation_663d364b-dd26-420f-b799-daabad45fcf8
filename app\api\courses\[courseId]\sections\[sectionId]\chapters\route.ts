import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/lib/db";

export async function POST(
  req: Request,
  { params }: { params: { courseId: string; sectionId: string } }
) {
  try {
    const session = await auth();
    const userId = session?.user.id;

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { title, description, position, videoUrl } = await req.json();

    const chapter = await db.chapter.create({
      data: {
        title,
        description,
        position,
        videoUrl,
        sectionId: params.sectionId,
      },
    });

    return NextResponse.json(chapter);
  } catch (error) {
    console.log("[CHAPTER_CREATE_ERROR]: ", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}
