"use client";

import React, { useState } from "react";
import { Play, Pause, Volume2, VolumeX, Maximize } from "lucide-react";

interface VideoPlayerProps {
  videoUrl: string;
  posterImage?: string;
  title?: string;
}

const VideoPlayer = ({
  videoUrl,
  posterImage = "https://images.unsplash.com/photo-1593642634524-b40b5baae6bb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80",
  title = "Course Introduction",
}: VideoPlayerProps) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [showControls, setShowControls] = useState(true);

  const handlePlayClick = () => {
    setIsPlaying(!isPlaying);
    // In a real implementation, you would control the video element here
  };

  return (
    <div
      className="relative rounded-xl overflow-hidden shadow-lg transition-all duration-300 group"
      onMouseEnter={() => setShowControls(true)}
      onMouseLeave={() => !isPlaying && setShowControls(true)}
    >
      {/* Video or poster image */}
      <div className="relative aspect-video bg-black rounded-xl overflow-hidden">
        {!isPlaying ? (
          <>
            <img
              src={posterImage}
              alt={title}
              className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
              <button
                onClick={handlePlayClick}
                className="w-20 h-20 rounded-full bg-white/20 backdrop-blur-sm text-white flex items-center justify-center transition-transform duration-300 hover:scale-110 group-hover:scale-105"
              >
                <Play className="w-8 h-8 fill-white" />
              </button>
            </div>
            <div className="absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-t from-black/80 via-black/40 to-transparent">
              <h3 className="text-xl font-medium text-white">{title}</h3>
              <p className="text-white/80 text-sm mt-1">
                Click to play the introduction video
              </p>
            </div>
          </>
        ) : (
          <>
            <iframe
              src={`${videoUrl}?autoplay=1&mute=${isMuted ? 1 : 0}`}
              title={title}
              className="w-full h-full rounded-xl"
              allowFullScreen
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            ></iframe>
            {showControls && (
              <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/80 via-black/40 to-transparent flex items-center justify-between animate-fade-in">
                <button
                  onClick={handlePlayClick}
                  className="text-white hover:text-course-blue transition-colors"
                >
                  {isPlaying ? (
                    <Pause className="w-6 h-6" />
                  ) : (
                    <Play className="w-6 h-6" />
                  )}
                </button>
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => setIsMuted(!isMuted)}
                    className="text-white hover:text-course-blue transition-colors"
                  >
                    {isMuted ? (
                      <VolumeX className="w-5 h-5" />
                    ) : (
                      <Volume2 className="w-5 h-5" />
                    )}
                  </button>
                  <button className="text-white hover:text-course-blue transition-colors">
                    <Maximize className="w-5 h-5" />
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default VideoPlayer;
