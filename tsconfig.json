{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"], "#site/content": ["./.velite"]}}, "include": [".env.local", "next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "app/config/mongoose.ts", "utils/LaTeX.jsx", "utils/LaTeX.jsx"], "exclude": ["node_modules"]}