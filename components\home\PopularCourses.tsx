import React from "react";
import Link from "next/link";
import { getCourses } from "@/actions/get-courses";
import { CourseCard } from "@/components/shared/course-card";

const PopularCourses = async () => {
  const courses = await getCourses();
  const popularCourses = courses.slice(0, 6); // Show only first 6 courses

  return (
    <section className="w-full min-h-screen py-24 px-8 relative">

      <div className="relative z-10 max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-16 animate-fade-in-up">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 text-sm font-medium mb-4">
            Formations populaires
          </div>
          <h2 className="text-5xl font-bold mb-6 bg-gradient-to-r from-gray-900 via-orange-400 to-gray-900 dark:from-white dark:via-orange-400 dark:to-white bg-clip-text text-transparent">
            Nos cours populaires
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Découvrez les formations les plus appréciées par notre communauté d'apprenants. 
            Des cours conçus par des experts pour vous faire progresser rapidement.
          </p>
        </div>

        {/* Courses Grid */}
        {popularCourses.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {popularCourses.map((course, index) => (
              <div 
                key={course.id}
                className="animate-fade-in-up"
                style={{ animationDelay: `${index * 150}ms` }}
              >
                <div className="group relative">
                  {/* Glow effect on hover */}
                  <div className="absolute -inset-1 bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl blur opacity-0 group-hover:opacity-25 transition duration-500"></div>
                  
                  <div className="relative bg-white dark:bg-neutral-800 rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                    <CourseCard
                      id={course.id}
                      title={course.title}
                      imageUrl={course.imageUrl || "/images/placeholder.png"}
                      price={course.price !== null ? course.price : 0}
                      description={course.description || "No description available"}
                      categories={
                        course.categories.length > 0
                          ? course.categories.map((category) => category.name)
                          : ["Uncategorized"]
                      }
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">📚</div>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Bientôt disponible
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Nos cours seront bientôt disponibles. Restez connectés !
            </p>
          </div>
        )}

        {/* Call to action */}
        <div className="text-center animate-fade-in-up delay-1000">
          <Link href="/cours">
            <div className="inline-flex items-center gap-4 px-8 py-4 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 cursor-pointer">
              <span className="font-semibold">Voir tous les cours</span>
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </div>
          </Link>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-20 animate-fade-in-up delay-1200">
          <div className="text-center p-6 bg-white/50 dark:bg-neutral-800/50 backdrop-blur-sm rounded-xl border border-gray-200/50 dark:border-neutral-700/50">
            <div className="text-3xl font-bold text-orange-500 mb-2">{courses.length}+</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Cours disponibles</div>
          </div>
          <div className="text-center p-6 bg-white/50 dark:bg-neutral-800/50 backdrop-blur-sm rounded-xl border border-gray-200/50 dark:border-neutral-700/50">
            <div className="text-3xl font-bold text-orange-500 mb-2">15+</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Domaines d'expertise</div>
          </div>
          <div className="text-center p-6 bg-white/50 dark:bg-neutral-800/50 backdrop-blur-sm rounded-xl border border-gray-200/50 dark:border-neutral-700/50">
            <div className="text-3xl font-bold text-orange-500 mb-2">98%</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Taux de réussite</div>
          </div>
          <div className="text-center p-6 bg-white/50 dark:bg-neutral-800/50 backdrop-blur-sm rounded-xl border border-gray-200/50 dark:border-neutral-700/50">
            <div className="text-3xl font-bold text-orange-500 mb-2">∞</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Accès à vie</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PopularCourses;
