import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Bell, Calendar, AlertCircle, Info, Star, ArrowRight, Megaphone, Settings, Newspaper } from "lucide-react";
import { AnnouncementWithAuthor } from "@/actions/get-announcements";
import Link from "next/link";

interface AnnouncementsProps {
  announcements: AnnouncementWithAuthor[];
}

const Announcements = ({ announcements }: AnnouncementsProps) => {
  // Dummy data - fallback when no real data
  const dummyAnnouncements = [
    {
      id: 1,
      type: "event",
      title: "Nouveau cours de Physique Quantique disponible",
      description: "Découvrez les mystères de la physique quantique avec notre nouveau cours interactif.",
      date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      priority: "high",
      icon: Star
    },
    {
      id: 2,
      type: "notice",
      title: "Maintenance programmée du système",
      description: "Le système sera indisponible le 15 janvier de 2h à 4h du matin pour maintenance.",
      date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
      priority: "medium",
      icon: AlertCircle
    },
    {
      id: 3,
      type: "news",
      title: "Nouvelle fonctionnalité: Assistant IA amélioré",
      description: "Notre assistant IA peut maintenant vous aider avec des problèmes plus complexes.",
      date: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
      priority: "low",
      icon: Info
    }
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "HIGH":
      case "URGENT": return "bg-red-100 text-red-800 dark:bg-red-950/20 dark:text-red-400";
      case "MEDIUM": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-950/20 dark:text-yellow-400";
      case "LOW": return "bg-blue-100 text-blue-800 dark:bg-blue-950/20 dark:text-blue-400";
      // Legacy support for dummy data
      case "high": return "bg-red-100 text-red-800 dark:bg-red-950/20 dark:text-red-400";
      case "medium": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-950/20 dark:text-yellow-400";
      case "low": return "bg-blue-100 text-blue-800 dark:bg-blue-950/20 dark:text-blue-400";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-950/20 dark:text-gray-400";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "EVENT": return Calendar;
      case "NOTICE": return AlertCircle;
      case "NEWS": return Newspaper;
      case "MAINTENANCE": return Settings;
      case "UPDATE": return Star;
      // Legacy support for dummy data
      case "event": return Calendar;
      case "notice": return AlertCircle;
      case "news": return Info;
      default: return Bell;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "EVENT": return "Événement";
      case "NOTICE": return "Avis";
      case "NEWS": return "Actualité";
      case "MAINTENANCE": return "Maintenance";
      case "UPDATE": return "Mise à jour";
      // Legacy support for dummy data
      case "event": return "Événement";
      case "notice": return "Avis";
      case "news": return "Actualité";
      default: return "Annonce";
    }
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return "Il y a quelques minutes";
    if (diffInHours < 24) return `Il y a ${diffInHours}h`;
    if (diffInHours < 48) return "Hier";
    return date.toLocaleDateString('fr-FR');
  };

  // Use real data if available, otherwise fallback to dummy data
  const displayAnnouncements = announcements.length > 0 ? announcements : dummyAnnouncements;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Bell className="w-5 h-5 text-orange-500" />
            Annonces & Mises à jour
          </CardTitle>
          <Link href="/student/announcements">
            <Button variant="ghost" size="sm">
              Voir tout
              <ArrowRight className="w-4 h-4 ml-1" />
            </Button>
          </Link>
        </div>
      </CardHeader>
      <CardContent>
        {displayAnnouncements.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <Megaphone className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>Aucune annonce pour le moment</p>
            <p className="text-sm mt-1">Les nouvelles annonces apparaîtront ici</p>
          </div>
        ) : (
          <div className="space-y-4">
            {displayAnnouncements.map((announcement) => {
              const TypeIcon = getTypeIcon(announcement.type);
              // For real data, use TypeIcon; for dummy data, use announcement.icon
              const ItemIcon = announcement.icon || TypeIcon;

              return (
                <Link key={announcement.id} href={`/student/announcements/${announcement.id}`}>
                  <div className="flex gap-4 p-4 bg-gray-50 dark:bg-neutral-800/50 rounded-lg hover:bg-gray-100 dark:hover:bg-neutral-800 transition-colors duration-200 cursor-pointer">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-full flex items-center justify-center">
                        <ItemIcon className="w-5 h-5 text-white" />
                      </div>
                    </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between gap-2 mb-2">
                    <h4 className="font-semibold text-gray-900 dark:text-white line-clamp-1">
                      {announcement.title}
                    </h4>
                    <Badge variant="outline" className={getPriorityColor(announcement.priority)}>
                      {announcement.priority === "HIGH" ? "Élevée" :
                       announcement.priority === "URGENT" ? "Urgente" :
                       announcement.priority === "MEDIUM" ? "Moyenne" :
                       announcement.priority === "LOW" ? "Faible" :
                       announcement.priority}
                    </Badge>
                  </div>

                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-2 line-clamp-2">
                    {announcement.description}
                  </p>

                  <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                    <TypeIcon className="w-3 h-3" />
                    <span>{getTypeLabel(announcement.type)}</span>
                    <span>•</span>
                    <span>{formatDate(announcement.date || announcement.createdAt)}</span>
                  </div>
                </div>
                  </div>
                </Link>
              );
          })}
        </div>
        )}
      </CardContent>
    </Card>
  );
};

export default Announcements;
