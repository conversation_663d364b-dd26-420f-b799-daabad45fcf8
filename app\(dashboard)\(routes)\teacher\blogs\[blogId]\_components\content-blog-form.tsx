"use client";

import { useForm } from "react-hook-form";
import * as z from "zod";
import axios from "axios";
import { zodResolver } from "@hookform/resolvers/zod";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Blog } from "@prisma/client";
import Editor from "../../../../../../../lib/editor";

interface ContentBlogFormProps {
  initialData: Blog;
  blogId: string;
}

const formSchema = z.object({
  content: z.string().min(1, {
    message: "Content is required",
  }),
});

export const ContentBlogForm = ({
  initialData,
  blogId,
}: ContentBlogFormProps) => {
  const router = useRouter();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      content: initialData?.content || "",
    },
  });

  const { isSubmitting, isValid } = form.formState;

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      await axios.patch(`/api/blogs/${blogId}`, values);
      toast.success("Blog mise à jour");
      router.refresh();
    } catch {
      toast.error("Une erreur s'est produite!");
    }
  };

  return (
    <div className="w-full">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full flex gap-4 items-start max-sm:flex-col max-sm:items-end"
        >
          <FormField
            control={form.control}
            name="content"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormControl>
                  <div className="rounded-lg border border-input">
                    <Editor
                      value={field.value}
                      onChange={field.onChange}
                      height={400}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex items-center">
            <Button type="submit" disabled={!isValid || isSubmitting}>
              Enregistrer
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};
