import { auth } from "@/auth";
import DashboardTitle from "@/app/(dashboard)/_components/dashboard-page-title";
import BreadCrumb from "@/components/shared/breadcrumb";
import { IconBadge } from "@/components/shared/icon-badge";

import { Separator } from "@/components/ui/separator";
import { Spotlight } from "@/components/ui/spotlight";
import { MonitorPlay, Settings2 } from "lucide-react";
import { redirect } from "next/navigation";
import { DataTable } from "./courses-table/data-table";
import { columns } from "./courses-table/columns";
import { db } from "@/lib/db";

const page = async () => {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return redirect("/");
  }

  const courses = await db.course.findMany({
    include: {
      categories: true,
    },
  });

  return (
    <div className="w-full z-20 flex flex-col">
      <BreadCrumb />
      <DashboardTitle
        title="Gestion des utilisateurs"
        description="Ravi de vous revoir! <PERSON><PERSON>rez vos cours et suivez vos performances
          facilement."
      />
      <Separator />
      <div className="flex items-center gap-x-4 mb-8">
        <IconBadge icon={MonitorPlay} />
        <h2 className="big-text">Gestion des Cours</h2>
      </div>
      {/* <DataTable columns={columns} data={courses} /> */}
      <Separator />
      <div className="flex items-center gap-x-4 mb-8">
        <IconBadge icon={Settings2} />
        <h2 className="big-text">Création / Modification des cours</h2>
      </div>
    </div>
  );
};

export default page;
