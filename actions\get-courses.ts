import { Category, Course } from "@prisma/client";
import { db } from "@/lib/db";

type SimpleCourse = Course & {
  categories: Category[];
};

export const getCourses = async (): Promise<SimpleCourse[]> => {
  try {
    const courses = await db.course.findMany({
      where: {
        isPublished: true,
      },
      include: {
        categories: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return courses;
  } catch (error) {
    console.error("[GET_COURSES]", error);
    return [];
  }
};
