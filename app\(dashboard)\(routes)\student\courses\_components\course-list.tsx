"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { BookOpen, Clock, User, Play, CheckCircle } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

interface Course {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  price: number;
  progress: number;
  lastAccessed: Date;
  instructor: string;
  category: string;
  totalChapters: number;
  completedChapters: number;
  isCompleted: boolean;
}

interface CourseListProps {
  courses: Course[];
}

const CourseList = ({ courses }: CourseListProps) => {
  const formatLastAccessed = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return "Il y a quelques minutes";
    if (diffInHours < 24) return `Il y a ${diffInHours}h`;
    if (diffInHours < 48) return "Hier";
    return date.toLocaleDateString('fr-FR');
  };

  if (courses.length === 0) {
    return (
      <div className="text-center py-16">
        <BookOpen className="w-16 h-16 mx-auto mb-4 text-gray-400" />
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Aucun cours trouvé
        </h3>
        <p className="text-gray-600 dark:text-gray-300">
          Essayez de modifier vos filtres ou explorez notre catalogue de cours.
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {courses.map((course, index) => (
        <Card 
          key={course.id} 
          className="group hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 animate-fade-in-up"
          style={{ animationDelay: `${index * 150}ms` }}
        >
          <CardContent className="p-0">
            {/* Course Thumbnail */}
            <div className="relative aspect-video overflow-hidden rounded-t-lg">
              <Image
                src={course.imageUrl || "/images/placeholder-course.jpg"}
                alt={course.title}
                fill
                className="object-cover transition-transform duration-300 group-hover:scale-105"
              />
              
              {/* Progress Overlay */}
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
                <div className="flex items-center justify-between text-white text-sm mb-2">
                  <span>{course.completedChapters}/{course.totalChapters} chapitres</span>
                  <span>{Math.round(course.progress)}%</span>
                </div>
                <Progress value={course.progress} className="h-2 bg-white/20" />
              </div>

              {/* Completion Badge */}
              {course.isCompleted && (
                <div className="absolute top-4 right-4">
                  <Badge className="bg-green-500 hover:bg-green-600 text-white">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Terminé
                  </Badge>
                </div>
              )}

              {/* Play Button Overlay */}
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="w-16 h-16 bg-white/90 rounded-full flex items-center justify-center shadow-lg">
                  <Play className="w-6 h-6 text-orange-500 ml-1" />
                </div>
              </div>
            </div>

            {/* Course Content */}
            <div className="p-6">
              <div className="flex items-start justify-between mb-3">
                <Badge variant="outline" className="text-xs">
                  {course.category}
                </Badge>
                <div className="text-right">
                  <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    {formatLastAccessed(course.lastAccessed)}
                  </div>
                </div>
              </div>

              <h3 className="font-bold text-lg text-gray-900 dark:text-white mb-2 line-clamp-2 group-hover:text-orange-600 dark:group-hover:text-orange-400 transition-colors duration-300">
                {course.title}
              </h3>

              <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2">
                {course.description}
              </p>

              <div className="flex items-center gap-2 mb-4 text-sm text-gray-500 dark:text-gray-400">
                <User className="w-4 h-4" />
                <span>{course.instructor}</span>
              </div>

              <div className="flex gap-2">
                <Link href={`/student/courses/${course.id}`} className="flex-1">
                  <Button className="w-full" variant={course.isCompleted ? "outline" : "default"}>
                    {course.isCompleted ? "Revoir" : "Continuer"}
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default CourseList;
