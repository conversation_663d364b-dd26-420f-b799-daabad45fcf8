import React, { FC, ReactNode } from "react";
import { Badge } from "../ui/badge";
import { SparklesCore } from "../ui/sparkles";

interface TitleProps {
  label: string;
  title: string;
  description: string;
  additionalStyles?: string;
}

const Title: FC<TitleProps> = ({
  label,
  title,
  description,
  additionalStyles = "",
}: TitleProps) => {
  return (
    <div
      className={`flex w-full flex-col gap-4 items-center justify-start max-lg:items-start ${additionalStyles}`}
    >
      <Badge
        className="w-fit text-sm font-medium max-xl:text-xs"
        variant={"outline"}
      >
        {label}
      </Badge>
      <div className="h-[100%] w-full flex flex-col items-center max-lg:items-start justify-center overflow-hidden">
        <h1 className="text-6xl max-2xl:text-5xl max-lg:text-4xl font-bold text-center dark:text-white text-black relative z-20 mb-2">
          {title}
        </h1>
        <p className="p-text max-lg:text-left">{description}</p>
        <div className="max-lg:hidden w-[100rem] max-2xl:w-[80rem] max-xl:w-[60rem] h-20 relative mt-2">
          {/* Gradients */}
          <div className="absolute inset-x-10 top-0 bg-gradient-to-r from-transparent via-amber-500 to-transparent h-[2px]  blur-sm" />
          <div className="absolute inset-x-10 top-0 bg-gradient-to-r from-transparent via-amber-500 to-transparent h-px" />
          <div className="absolute inset-x-40 top-0 bg-gradient-to-r from-transparent via-orange-500 to-transparent h-[5px] blur-sm" />
          <div className="absolute inset-x-40 top-0 bg-gradient-to-r from-transparent via-orange-500 to-transparent h-px" />
        </div>
      </div>
    </div>
  );
};

export default Title;
