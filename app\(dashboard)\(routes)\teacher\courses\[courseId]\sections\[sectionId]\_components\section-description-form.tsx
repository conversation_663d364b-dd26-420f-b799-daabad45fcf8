"use client";

import * as z from "zod";
import axios from "axios";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Section } from "@prisma/client";
import { Textarea } from "@/components/ui/textarea";
import { useEffect } from "react";

interface SectionDescriptionFormProps {
  initialData: Section;
  courseId: string;
  sectionId: string;
}

const formSchema = z.object({
  description: z.string().min(1, {
    message: "La description est requise!",
  }),
});

export const SectionDescriptionForm = ({
  initialData,
  courseId,
  sectionId,
}: SectionDescriptionFormProps) => {
  const router = useRouter();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      description: initialData?.description || "",
    },
  });

  const { isSubmitting, isValid } = form.formState;

  const handleContentChange = (markdown: string) => {
    form.setValue("description", markdown, { shouldValidate: true });
  };

  useEffect(() => {
    form.setValue("description", initialData?.description ?? ""); // Sync initial data
  }, [initialData?.description, form]);

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      await axios.patch(
        `/api/courses/${courseId}/sections/${sectionId}`,
        values
      );
      toast.success("Description de la section mise à jour");
      router.refresh();
    } catch {
      toast.error("Une erreur s'est produite!");
    }
  };

  return (
    <div className="w-full">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex gap-4 items-start w-full justify-between max-sm:flex-col max-sm:items-end"
        >
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormControl>
                  <Textarea {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="flex items-center">
            <Button type="submit" disabled={!isValid || isSubmitting}>
              Enregistrer
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};
