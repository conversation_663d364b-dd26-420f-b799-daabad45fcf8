import React from "react";
import User from "../cards/User";
import Image from "next/image";
import { Badge } from "../ui/badge";

const PageQuote = () => {
  return (
    <div>
      <Badge className="text-sm" variant={"outline"}>
        Votre Alephblog est activé!
      </Badge>

      {/* Title */}
      <div className="mt-8 md:mb-12 max-w-2xl">
        <h1 className="mb-4 font-semibold text-gray-800 text-4xl lg:text-5xl dark:text-neutral-200">
          Apprentissage interactif personnalisé.
        </h1>
        <p className="text-gray-600 dark:text-neutral-400">
          Découvrez des sessions live, des travaux dirigés, des devoirs
          surveillés et des vidéos éducatives pour une expérience
          d'apprentissage complète et personnalisée.
        </p>
      </div>

      <blockquote className="relative max-w-sm mt-10">
        <Image
          width={81}
          height={81}
          src="/assets/icons/quote.svg"
          alt="quote"
          className="absolute top-[-35px] left-[-25px] opacity-20 rotate-180"
        />

        <div className="relative z-10">
          <p className="text-xl italic text-gray-800 dark:text-white">
            Amazing people to work with. Very fast and professional partner.
          </p>
        </div>

        <footer className="mt-4">
          <User
            image="/assets/instructors/ihind.jpg"
            name="Hamza Ihind"
            level="CEO & Founder | ALEPHNULL"
          />
        </footer>
      </blockquote>
    </div>
  );
};

export default PageQuote;
