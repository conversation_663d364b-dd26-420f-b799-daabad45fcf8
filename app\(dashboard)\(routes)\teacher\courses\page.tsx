import React from "react";

import { auth } from "@/auth";
import { db } from "@/lib/db";
import { redirect } from "next/navigation";

import { columns } from "./_components/columns";
import { DataTable } from "./_components/data-table";
import BreadCrumb from "@/components/shared/breadcrumb";
import { Separator } from "@/components/ui/separator";
import DashboardPageTitle from "@/app/(dashboard)/_components/dashboard-page-title";

const CoursesPage = async () => {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return redirect("/");
  }

  const courses = await db.course.findMany({
    where: {
      userId,
    },
    orderBy: {
      createdAt: "desc",
    },
    include: {
      categories: true,
    },
  });

  return (
    <div className="w-full h-[100vh] z-20 flex flex-col">
      <BreadCrumb />
      <DashboardPageTitle
        title="Parcours d'Apprentissage"
        description="Explorez et gérez l'ensemble de vos formations. Suivez votre progression, reprenez là où vous vous êtes arrêté, ou découvrez de nouveaux contenus."
      />
      <Separator className="my-4" />
      <DataTable columns={columns} data={courses} />
    </div>
  );
};

export default CoursesPage;
