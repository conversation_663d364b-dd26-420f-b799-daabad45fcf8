"use client";

import { useState } from "react";
import axios from "axios";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { FileUpload } from "@/components/shared/file-upload";
import { Attachment, Chapter } from "@prisma/client";
import { File, Trash2, PlusCircle, X } from "lucide-react";
import Image from "next/image";

interface ChapterAssociatedFilesFormProps {
  initialData: Chapter & { attachments: Attachment[] };
  courseId: string;
  sectionId: string;
  chapterId: string;
}

const fileIcons: Record<string, string> = {
  pdf: "/assets/icons/files/pdf.png",
  doc: "/assets/icons/files/doc.png",
  docx: "/assets/icons/files/doc.png",
  png: "/assets/icons/files/png.png",
  jpg: "/assets/icons/files/jpg.png",
  jpeg: "/assets/icons/files/jpg.png",
};

export const ChapterAttachmentForm = ({
  initialData,
  courseId,
  sectionId,
  chapterId,
}: ChapterAssociatedFilesFormProps) => {
  const router = useRouter();
  const { toast } = useToast();
  const [isEditing, setIsEditing] = useState(false);

  const toggleEdit = () => setIsEditing((current) => !current);

  const handleFileAction = async (
    action: "add" | "delete",
    data: { url: string; name: string } | string
  ) => {
    try {
      if (action === "add") {
        await axios.post(
          `/api/courses/${courseId}/sections/${sectionId}/chapters/${chapterId}/attachments`,
          data
        );
        toast({
          title: "Fichier ajouté",
          description: "Le fichier a été associé au chapitre avec succès",
        });
      } else {
        await axios.delete(
          `/api/courses/${courseId}/sections/${sectionId}/chapters/${chapterId}/attachments/${data}`
        );
        toast({
          title: "Fichier supprimé",
          description: "Le fichier a été retiré du chapitre",
        });
      }
      router.refresh();
    } catch {
      toast({
        title: "Erreur",
        description: `Une erreur est survenue lors de ${
          action === "add" ? "l'ajout" : "la suppression"
        } du fichier`,
        variant: "destructive",
      });
    }
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split(".").pop()?.toLowerCase() || "";
    const iconSrc = fileIcons[extension];

    return iconSrc ? (
      <Image
        src={iconSrc}
        alt={extension.toUpperCase()}
        width={32}
        height={32}
        className="mr-2"
      />
    ) : (
      <File className="h-8 w-8 mr-2" />
    );
  };

  return (
    <div className="w-full space-y-4">
      <div className="flex items-center justify-between">
        <Button
          onClick={toggleEdit}
          variant={isEditing ? "destructive" : "outline"}
          className="flex items-center gap-2"
        >
          {isEditing ? (
            <>
              <X className="h-4 w-4" />
              Annuler
            </>
          ) : (
            <>
              <PlusCircle className="h-4 w-4" />
              Ajouter des fichiers
            </>
          )}
        </Button>
      </div>

      {!isEditing ? (
        initialData.attachments.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-40 bg-slate-100 dark:bg-slate-800 rounded-lg gap-2">
            <File className="h-10 w-10 text-slate-400" />
            <p className="text-slate-500 dark:text-slate-400">
              Aucun fichier associé
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {initialData.attachments.map((attachment) => (
              <div
                key={attachment.id}
                className="group flex items-center p-3 border rounded-lg bg-slate-50 dark:bg-neutral-950 hover:bg-slate-100 dark:hover:bg-neutral-800 transition-colors"
              >
                {getFileIcon(attachment.name)}
                <a
                  href={attachment.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm font-medium hover:underline flex-1 line-clamp-1"
                  title={attachment.name}
                >
                  {attachment.name}
                </a>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleFileAction("delete", attachment.id)}
                  className="text-red-600 hover:text-white hover:bg-red-600/90 dark:text-red-600 dark:hover:text-white dark:hover:bg-red-600/90 opacity-0 group-hover:opacity-100 transition-all flex items-center gap-2 px-3 py-1.5 rounded-md"
                >
                  <Trash2 className="h-4 w-4" />
                  <span className="hidden sm:inline">Supprimer</span>
                </Button>
              </div>
            ))}
          </div>
        )
      ) : (
        <div className="space-y-4">
          <FileUpload
            endpoint="chapterAttachment"
            onChange={(fileData) => {
              if (fileData) {
                handleFileAction("add", fileData);
                setIsEditing(false);
              }
            }}
          />

          {initialData.attachments.length > 0 && (
            <div className="space-y-3">
              <h5 className="text-sm font-medium">Fichiers existants</h5>
              {initialData.attachments.map((attachment) => (
                <div
                  key={attachment.id}
                  className="group flex items-center p-3 border rounded-lg bg-slate-50 dark:bg-netural-950"
                >
                  {getFileIcon(attachment.name)}
                  <span className="text-sm flex-1 line-clamp-1">
                    {attachment.name}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleFileAction("delete", attachment.id)}
                    className="text-red-600 hover:text-white hover:bg-red-500/90 dark:text-red-400 dark:hover:text-white dark:hover:bg-red-600/90 opacity-0 group-hover:opacity-100 transition-all flex items-center gap-2 px-3 py-1.5 rounded-md"
                  >
                    <Trash2 className="h-4 w-4" />
                    <span className="hidden sm:inline">Supprimer</span>
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
