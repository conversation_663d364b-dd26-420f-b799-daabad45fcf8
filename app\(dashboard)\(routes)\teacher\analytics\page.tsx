import { getAnalytics } from "@/actions/get-analytics";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import React from "react";
import { DataCard } from "./_components/data-card";
import { Spotlight } from "@/components/ui/spotlight";
import BreadCrumb from "@/components/shared/breadcrumb";
import DashboardPageTitle from "@/app/(dashboard)/_components/dashboard-page-title";
import { Separator } from "@/components/ui/separator";

const page = async () => {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return redirect("/");
  }

  const { data, totalRevenue, totalSales } = await getAnalytics(userId);

  return (
    <div className="w-full z-20 flex flex-col">
      <BreadCrumb />
      <DashboardPageTitle
        title="Mes Analytiques"
        description="Voici la page des analytiques"
      />
      <Separator />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <DataCard label="Total Sales" value={totalSales} />
        <DataCard label="Total Revenue" value={totalRevenue} shouldFormat />
      </div>
    </div>
  );
};

export default page;
