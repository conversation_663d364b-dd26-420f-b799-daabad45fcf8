import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Play, Clock, BookOpen } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

interface ResumeCourseProps {
  course: {
    id: string;
    title: string;
    imageUrl: string;
    progress: number;
    lastChapter: string;
    timeSpent: number; // in minutes
    category: string;
  } | null;
}

const ResumeCourse = ({ course }: ResumeCourseProps) => {
  if (!course) {
    return (
      <Card className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900">
        <CardContent className="p-6 text-center">
          <BookOpen className="w-12 h-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Aucun cours en cours
          </h3>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Commencez un nouveau cours pour voir vos progrès ici.
          </p>
          <Link href="/student/browse">
            <Button>Explorer les cours</Button>
          </Link>
        </CardContent>
      </Card>
    );
  }

  const formatTimeSpent = (minutes: number) => {
    if (minutes < 60) return `${minutes}min`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}min`;
  };

  return (
    <Card className="bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-950/20 dark:to-orange-900/20 border-orange-200 dark:border-orange-800 hover:shadow-xl transition-all duration-300">
      <CardContent className="p-0">
        <div className="flex flex-col md:flex-row">
          {/* Course Image */}
          <div className="relative w-full md:w-48 h-32 md:h-auto">
            <Image
              src={course.imageUrl || "/images/placeholder-course.jpg"}
              alt={course.title}
              fill
              className="object-cover rounded-t-lg md:rounded-l-lg md:rounded-t-none"
            />
            <div className="absolute inset-0 bg-black/20 rounded-t-lg md:rounded-l-lg md:rounded-t-none"></div>
          </div>

          {/* Course Info */}
          <div className="flex-1 p-6">
            <div className="flex items-start justify-between mb-4">
              <div>
                <Badge variant="outline" className="mb-2 border-orange-300 text-orange-700 dark:border-orange-600 dark:text-orange-400">
                  {course.category}
                </Badge>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  Reprendre où vous vous êtes arrêté
                </h3>
                <h4 className="text-lg font-semibold text-orange-700 dark:text-orange-400 mb-2">
                  {course.title}
                </h4>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-orange-600 mb-1">
                  {Math.round(course.progress)}%
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-300">
                  Terminé
                </div>
              </div>
            </div>

            <div className="mb-4">
              <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-300 mb-2">
                <span>Dernier chapitre: {course.lastChapter}</span>
                <span className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  {formatTimeSpent(course.timeSpent)} passées
                </span>
              </div>
              <Progress value={course.progress} className="h-3 bg-orange-200 dark:bg-orange-900" />
            </div>

            <div className="flex gap-3">
              <Link href={`/student/courses/${course.id}`} className="flex-1">
                <Button className="w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg hover:shadow-xl transition-all duration-300">
                  <Play className="w-4 h-4 mr-2" />
                  Continuer le cours
                </Button>
              </Link>
              <Button variant="outline" className="border-orange-300 text-orange-700 hover:bg-orange-50 dark:border-orange-600 dark:text-orange-400 dark:hover:bg-orange-950/20">
                Détails
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ResumeCourse;
