"use client";

import Image from "next/image";
import { Badge } from "../ui/badge";

interface CourseProps {
  image: string;
  title: string;
  desc: string;
  instructor_name: string;
  instructor_pic: string;
}

const Course: React.FC<CourseProps> = ({
  image,
  title,
  desc,
  instructor_name,
  instructor_pic,
}) => {
  const truncatedDesc = desc.slice(0, 200);
  return (
    <div className="flex flex-col items-start  w-fit h-[580] p-6 pt-0 gap-10 flex-1 bg-white dark:bg-zinc-800 border border-solid border-gray-200 dark:border-zinc-500 rounded-lg">
      <Image
        src={image}
        alt={image}
        width={1050}
        height={200}
        className="p-4"
      />
      <div className="flex flex-col gap-4">
        <div className="flex w-full items-center justify-between">
          <h1 className="text-gray-900 dark:text-white text-2xl font-semibold">
            {title}
          </h1>
          <Image
            src="/assets/icons/arrow-up-right.svg"
            alt="arrow"
            height={12}
            width={12}
            className="invert(1)"
          />
        </div>
        <p className="desc-text dark:text-gray-200 text-left max-w-[400px]">
          {truncatedDesc}...
        </p>
        <div className="flex items-center gap-3 mt-4">
          <Image
            src={instructor_pic}
            alt={instructor_name}
            height={40}
            width={40}
            className="rounded-3xl"
          />
          <p className="desc-text dark:text-gray-100">{instructor_name}</p>
        </div>
      </div>
    </div>
  );
};

export default Course;
