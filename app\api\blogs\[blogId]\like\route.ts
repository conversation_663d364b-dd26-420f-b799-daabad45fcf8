import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/lib/db";

export async function POST(
  req: Request,
  { params }: { params: { blogId: string } }
) {
  try {
    const session = await auth();
    const userId = session?.user.id;
    const { blogId } = params;

    if (!userId) {
      return new NextResponse("Non autorisé", { status: 401 });
    }

    // Vérifier si l'utilisateur a déjà liké ce blog
    const existingLike = await db.like.findFirst({
      where: {
        userId,
        blogId,
      },
    });

    if (existingLike) {
      // Si l'utilisateur a déjà liké, retirer le like
      await db.like.delete({
        where: {
          id: existingLike.id,
        },
      });

      return NextResponse.json({ message: "Like retiré" });
    } else {
      await db.like.create({
        data: {
          userId,
          blogId,
        },
      });

      return NextResponse.json({ message: "Blog liké" });
    }
  } catch (error) {
    console.error("[LIKE_ERROR]", error);
    return new NextResponse("Erreur interne", { status: 500 });
  }
}
