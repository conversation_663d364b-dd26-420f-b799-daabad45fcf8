"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { 
  Upload, 
  FileText, 
  Calendar, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  Download,
  Eye,
  Star,
  MessageSquare,
  Paperclip
} from "lucide-react";

interface Assignment {
  id: string;
  title: string;
  description: string;
  dueDate: Date;
  maxScore: number;
  submittedAt?: Date;
  score?: number;
  feedback?: string;
  status: "pending" | "submitted" | "graded" | "late";
  attachments: string[];
  submissionFile?: string;
  gradedAt?: Date;
}

interface AssignmentUploadsProps {
  assignments: Assignment[];
}

const AssignmentUploads = ({ assignments }: AssignmentUploadsProps) => {
  const [selectedTab, setSelectedTab] = useState("pending");
  const [uploadingFiles, setUploadingFiles] = useState<{[key: string]: boolean}>({});

  // Dummy data
  const dummyAssignments: Assignment[] = [
    {
      id: "assign-1",
      title: "Résolution de problèmes algébriques",
      description: "Résolvez les 10 problèmes d'algèbre fournis dans le document PDF. Montrez toutes vos étapes de calcul.",
      dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
      maxScore: 100,
      status: "pending",
      attachments: ["problemes_algebre.pdf", "formules_reference.pdf"]
    },
    {
      id: "assign-2",
      title: "Projet de géométrie - Construction",
      description: "Créez une construction géométrique utilisant les théorèmes vus en cours. Incluez une explication détaillée.",
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      maxScore: 150,
      status: "pending",
      attachments: ["instructions_projet.pdf"]
    },
    {
      id: "assign-3",
      title: "Analyse de fonctions",
      description: "Analysez les fonctions données et tracez leurs graphiques. Déterminez les domaines, images et points critiques.",
      dueDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      maxScore: 80,
      submittedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      score: 75,
      feedback: "Excellent travail ! Les graphiques sont précis et l'analyse est complète. Attention aux calculs de dérivées dans l'exercice 3.",
      status: "graded",
      attachments: ["fonctions_exercices.pdf"],
      submissionFile: "analyse_fonctions_marie.pdf",
      gradedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
    },
    {
      id: "assign-4",
      title: "Exercices de calcul intégral",
      description: "Calculez les intégrales définies et indéfinies des fonctions proposées.",
      dueDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      maxScore: 60,
      submittedAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000),
      status: "submitted",
      attachments: ["integrales_exercices.pdf"],
      submissionFile: "calcul_integral_marie.pdf"
    }
  ];

  const displayAssignments = assignments.length > 0 ? assignments : dummyAssignments;

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-950/20 dark:text-yellow-400";
      case "submitted": return "bg-blue-100 text-blue-800 dark:bg-blue-950/20 dark:text-blue-400";
      case "graded": return "bg-green-100 text-green-800 dark:bg-green-950/20 dark:text-green-400";
      case "late": return "bg-red-100 text-red-800 dark:bg-red-950/20 dark:text-red-400";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-950/20 dark:text-gray-400";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "pending": return "En attente";
      case "submitted": return "Soumis";
      case "graded": return "Noté";
      case "late": return "En retard";
      default: return "Inconnu";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending": return <Clock className="w-4 h-4" />;
      case "submitted": return <CheckCircle className="w-4 h-4" />;
      case "graded": return <Star className="w-4 h-4" />;
      case "late": return <AlertTriangle className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getDaysUntilDue = (dueDate: Date) => {
    const now = new Date();
    const diffInDays = Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return diffInDays;
  };

  const handleFileUpload = (assignmentId: string) => {
    setUploadingFiles(prev => ({ ...prev, [assignmentId]: true }));
    
    // Simulate file upload
    setTimeout(() => {
      setUploadingFiles(prev => ({ ...prev, [assignmentId]: false }));
      // Update assignment status to submitted
    }, 2000);
  };

  const pendingAssignments = displayAssignments.filter(a => a.status === "pending");
  const submittedAssignments = displayAssignments.filter(a => a.status === "submitted");
  const gradedAssignments = displayAssignments.filter(a => a.status === "graded");

  const averageScore = gradedAssignments.length > 0 
    ? gradedAssignments.reduce((acc, a) => acc + (a.score || 0), 0) / gradedAssignments.length 
    : 0;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="w-5 h-5 text-orange-500" />
          Devoirs & Soumissions
        </CardTitle>
        
        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4">
          <div className="bg-yellow-50 dark:bg-yellow-950/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="w-5 h-5 text-yellow-600" />
              <span className="font-medium text-yellow-900 dark:text-yellow-100">En attente</span>
            </div>
            <div className="text-2xl font-bold text-yellow-600">
              {pendingAssignments.length}
            </div>
          </div>
          
          <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle className="w-5 h-5 text-blue-600" />
              <span className="font-medium text-blue-900 dark:text-blue-100">Soumis</span>
            </div>
            <div className="text-2xl font-bold text-blue-600">
              {submittedAssignments.length}
            </div>
          </div>
          
          <div className="bg-green-50 dark:bg-green-950/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Star className="w-5 h-5 text-green-600" />
              <span className="font-medium text-green-900 dark:text-green-100">Notés</span>
            </div>
            <div className="text-2xl font-bold text-green-600">
              {gradedAssignments.length}
            </div>
          </div>
          
          <div className="bg-orange-50 dark:bg-orange-950/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Star className="w-5 h-5 text-orange-600" />
              <span className="font-medium text-orange-900 dark:text-orange-100">Moyenne</span>
            </div>
            <div className="text-2xl font-bold text-orange-600">
              {averageScore > 0 ? `${Math.round(averageScore)}%` : "N/A"}
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <Tabs defaultValue="pending" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="pending">
              En attente ({pendingAssignments.length})
            </TabsTrigger>
            <TabsTrigger value="submitted">
              Soumis ({submittedAssignments.length})
            </TabsTrigger>
            <TabsTrigger value="graded">
              Notés ({gradedAssignments.length})
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="pending" className="mt-6">
            <div className="space-y-6">
              {pendingAssignments.map((assignment) => {
                const daysUntilDue = getDaysUntilDue(assignment.dueDate);
                const isUrgent = daysUntilDue <= 2;
                
                return (
                  <Card key={assignment.id} className={`${
                    isUrgent ? "border-red-200 dark:border-red-800" : ""
                  }`}>
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold text-lg text-gray-900 dark:text-white">
                              {assignment.title}
                            </h3>
                            <Badge variant="outline" className={getStatusColor(assignment.status)}>
                              {getStatusIcon(assignment.status)}
                              <span className="ml-1">{getStatusLabel(assignment.status)}</span>
                            </Badge>
                            {isUrgent && (
                              <Badge variant="outline" className="bg-red-100 text-red-800 dark:bg-red-950/20 dark:text-red-400">
                                <AlertTriangle className="w-3 h-3 mr-1" />
                                Urgent
                              </Badge>
                            )}
                          </div>
                          
                          <p className="text-gray-600 dark:text-gray-300 mb-4">
                            {assignment.description}
                          </p>
                          
                          <div className="flex items-center gap-6 text-sm text-gray-500 mb-4">
                            <div className="flex items-center gap-1">
                              <Calendar className="w-4 h-4" />
                              <span>Échéance: {formatDate(assignment.dueDate)}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Star className="w-4 h-4" />
                              <span>Points: {assignment.maxScore}</span>
                            </div>
                            <div className={`flex items-center gap-1 ${
                              isUrgent ? "text-red-600 font-medium" : ""
                            }`}>
                              <Clock className="w-4 h-4" />
                              <span>
                                {daysUntilDue > 0 
                                  ? `Dans ${daysUntilDue} jour${daysUntilDue > 1 ? 's' : ''}`
                                  : daysUntilDue === 0 
                                    ? "Aujourd'hui"
                                    : `En retard de ${Math.abs(daysUntilDue)} jour${Math.abs(daysUntilDue) > 1 ? 's' : ''}`
                                }
                              </span>
                            </div>
                          </div>
                          
                          {assignment.attachments.length > 0 && (
                            <div className="mb-4">
                              <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                                Documents fournis:
                              </h4>
                              <div className="flex flex-wrap gap-2">
                                {assignment.attachments.map((file, index) => (
                                  <Button key={index} variant="outline" size="sm">
                                    <Paperclip className="w-3 h-3 mr-1" />
                                    {file}
                                  </Button>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button 
                          className="flex-1"
                          disabled={uploadingFiles[assignment.id]}
                          onClick={() => handleFileUpload(assignment.id)}
                        >
                          {uploadingFiles[assignment.id] ? (
                            <>
                              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                              Téléchargement...
                            </>
                          ) : (
                            <>
                              <Upload className="w-4 h-4 mr-2" />
                              Soumettre le devoir
                            </>
                          )}
                        </Button>
                        <Button variant="outline">
                          <Eye className="w-4 h-4 mr-1" />
                          Détails
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>
          
          <TabsContent value="submitted" className="mt-6">
            <div className="space-y-6">
              {submittedAssignments.map((assignment) => (
                <Card key={assignment.id}>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold text-lg text-gray-900 dark:text-white">
                            {assignment.title}
                          </h3>
                          <Badge variant="outline" className={getStatusColor(assignment.status)}>
                            {getStatusIcon(assignment.status)}
                            <span className="ml-1">{getStatusLabel(assignment.status)}</span>
                          </Badge>
                        </div>
                        
                        <p className="text-gray-600 dark:text-gray-300 mb-4">
                          {assignment.description}
                        </p>
                        
                        <div className="flex items-center gap-6 text-sm text-gray-500 mb-4">
                          <div className="flex items-center gap-1">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span>Soumis le: {assignment.submittedAt && formatDate(assignment.submittedAt)}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Star className="w-4 h-4" />
                            <span>Points: {assignment.maxScore}</span>
                          </div>
                        </div>
                        
                        {assignment.submissionFile && (
                          <div className="mb-4">
                            <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                              Fichier soumis:
                            </h4>
                            <Button variant="outline" size="sm">
                              <FileText className="w-3 h-3 mr-1" />
                              {assignment.submissionFile}
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button variant="outline">
                        <Download className="w-4 h-4 mr-1" />
                        Télécharger
                      </Button>
                      <Button variant="outline">
                        <Eye className="w-4 h-4 mr-1" />
                        Voir les détails
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="graded" className="mt-6">
            <div className="space-y-6">
              {gradedAssignments.map((assignment) => (
                <Card key={assignment.id}>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold text-lg text-gray-900 dark:text-white">
                            {assignment.title}
                          </h3>
                          <Badge variant="outline" className={getStatusColor(assignment.status)}>
                            {getStatusIcon(assignment.status)}
                            <span className="ml-1">{getStatusLabel(assignment.status)}</span>
                          </Badge>
                        </div>
                        
                        <p className="text-gray-600 dark:text-gray-300 mb-4">
                          {assignment.description}
                        </p>
                        
                        <div className="flex items-center gap-6 text-sm text-gray-500 mb-4">
                          <div className="flex items-center gap-1">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span>Noté le: {assignment.gradedAt && formatDate(assignment.gradedAt)}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Star className="w-4 h-4" />
                            <span>Score: {assignment.score}/{assignment.maxScore}</span>
                          </div>
                        </div>
                        
                        {assignment.score && (
                          <div className="mb-4">
                            <div className="flex items-center justify-between mb-2">
                              <span className="font-medium">Score obtenu:</span>
                              <span className="text-2xl font-bold text-green-600">
                                {Math.round((assignment.score / assignment.maxScore) * 100)}%
                              </span>
                            </div>
                            <Progress 
                              value={(assignment.score / assignment.maxScore) * 100} 
                              className="h-3"
                            />
                          </div>
                        )}
                        
                        {assignment.feedback && (
                          <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                            <div className="flex items-center gap-2 mb-2">
                              <MessageSquare className="w-4 h-4 text-blue-500" />
                              <h4 className="font-medium text-blue-900 dark:text-blue-100">
                                Commentaires du professeur:
                              </h4>
                            </div>
                            <p className="text-blue-800 dark:text-blue-200">
                              {assignment.feedback}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button variant="outline">
                        <Download className="w-4 h-4 mr-1" />
                        Télécharger
                      </Button>
                      <Button variant="outline">
                        <Eye className="w-4 h-4 mr-1" />
                        Voir les détails
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default AssignmentUploads;
