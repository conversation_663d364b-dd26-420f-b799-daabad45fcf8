import { getBlog } from "@/actions/get-blog";
import LaTe<PERSON><PERSON>enderer from "@/components/shared/latex-renderer";
import Title from "@/components/shared/Title";
import { MDXRemote } from "next-mdx-remote/rsc";
import axios from "axios";
import Image from "next/image";
import React from "react";
import User from "@/components/cards/User";
import { Badge } from "@/components/ui/badge";
import { TracingBeam } from "@/components/ui/tracing-beam";
import BlogDisplay from "./_components/blog-display";
import { Separator } from "@/components/ui/separator";
import LikeButton from "./_components/like-button";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
// import Comments from "./_components/comments";
// import CommentForm from "./_components/comment-form";
import BlogAuthor from "./_components/blog-author";

const page = async ({ params }: { params: { blogId: string } }) => {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return redirect("/");
  }

  const blog = await getBlog(params.blogId);

  return (
    <div className="w-full flex items-center justify-center">
      <div className="w-full max-w-[1000px] z-10 flex flex-col items-center justify-center">
        {/* Post title, description, and user info */}
        <div className="w-full flex flex-col gap-4">
          <h1 className="blog-title">{blog?.title}</h1>
          <p className="p-text text-left">{blog?.description}</p>
          <div className="w-full flex items-end justify-between max-md:flex-col max-md:gap-4 max-md:items-start">
            <BlogAuthor
              firstName={blog?.author.firstName || ""}
              lastName={blog?.author.lastName || ""}
              createdAt={blog?.createdAt || ""}
              content={blog?.content || ""}
              image={blog?.author.image || ""}
            />
            <div className="flex justify-end gap-2">
              {blog?.categories?.map((tag) => (
                <Badge variant={"outline"}>{tag.name}</Badge>
              ))}
            </div>
          </div>
          <Separator className="mb-0 mt-2" />
          <LikeButton
            blogId={params.blogId || ""}
            initialLikes={blog?.likes.length || 0}
            isLikedbyUser={blog?.likes.some((like) => like.userId === userId)}
          />
          <Separator className="my-0" />
          <div className="border border-orange-400 w-full mt-4 rounded-md">
            <Image
              src={blog?.image || ""}
              alt={blog?.title || ""}
              width={1920}
              height={1080}
              className="rounded-md"
            />
          </div>
        </div>
        <BlogDisplay content={blog?.content || ""} className="mt-8" />
        {/* <CommentForm blogId={params.blogId} />

        {/* Comment Section */}
        {/* <Comments blogId={params.blogId} /> */}
      </div>
    </div>
  );
};

export default page;
