import Link from "next/link";
import Image from "next/image";
import { ArrowUpRight, BookOpen } from "lucide-react";
import { formatPrice } from "@/lib/format";
import { CourseProgress } from "./course-progress";
import { CategoryItem } from "@/app/(dashboard)/(routes)/student/search/_components/category-item";
import { colorMap } from "@/styles/color-map";

interface CourseCardProps {
  id: string;
  title: string;
  imageUrl: string;
  description: string;
  price: number;
  categories: string[];
}

export const CourseCard = ({
  id,
  title,
  imageUrl,
  price,
  description,
  categories,
}: CourseCardProps) => {
  return (
    <Link href={`/courses/${id}`}>
      <div className="flex flex-col gap-2 group hover:shadow-lg transition rounded-xl h-full border border-color">
        {/* Image Section */}
        <div className="relative w-full aspect-video rounded-t-xl overflow-hidden">
          <Image
            fill
            className="object-cover"
            alt={title}
            src={imageUrl}
            placeholder="blur"
            blurDataURL="/images/placeholder.png"
          />
        </div>

        {/* Content Section */}
        <div className="pt-0 px-4 pb-4 flex flex-col w-full">
          {/* CHAPTERS + DATE */}
          <div className="w-full flex justify-between mb-2 items-center">
            <div className="flex flex-wrap gap-2 mt-2 justify-end">
              {categories.map((cat) => (
                <CategoryItem key={cat} label={cat} color={colorMap[cat]} />
              ))}
            </div>
          </div>

          {/* Title */}
          <div className="flex flex-col gap-1 mb-6">
            <div className="mt-1 w-full flex justify-between items-center">
              <p className="text-2xl max-sm:text-xl text-white font-semibold group-hover:text-orange-500 transition">
                {title}
              </p>
              <ArrowUpRight className="h-6 w-6 text-gray-400 group-hover:text-orange-500 transition" />
            </div>

            {/* Description */}
            <p className="desc-text line-clamp-2 max-md:text-sm">
              {description}
            </p>
          </div>
        </div>
      </div>
    </Link>
  );
};
