"use client";

import React, { useState } from "react";

const FAQ = () => {
  const [openFAQ, setOpenFAQ] = useState<number | null>(0);

  const faqs = [
    {
      id: 1,
      question: "Qu'est-ce qu'ALEPHNULL ?",
      answer: "ALEPHNULL est une plateforme d'apprentissage en ligne innovante qui propose des cours de qualité supérieure dans divers domaines. Nous combinons technologie moderne et pédagogie experte pour offrir une expérience d'apprentissage personnalisée et interactive."
    },
    {
      id: 2,
      question: "Comment puis-je m'inscrire aux cours ?",
      answer: "L'inscription est simple ! Créez votre compte gratuit, explorez notre catalogue de cours, et inscrivez-vous aux formations qui vous intéressent. Vous pouvez commencer immédiatement après l'inscription."
    },
    {
      id: 3,
      question: "Les cours sont-ils accessibles à vie ?",
      answer: "Oui ! Une fois inscrit à un cours, vous y avez accès à vie. Vous pouvez apprendre à votre rythme, revenir sur les contenus quand vous le souhaitez, et bénéficier des mises à jour gratuites."
    },
    {
      id: 4,
      question: "Y a-t-il un support technique disponible ?",
      answer: "Absolument ! Notre équipe de support est disponible 24/7 pour vous aider. Vous pouvez nous contacter via le chat en direct, par email, ou consulter notre centre d'aide complet."
    },
    {
      id: 5,
      question: "Puis-je obtenir un certificat à la fin du cours ?",
      answer: "Oui, vous recevrez un certificat de completion vérifié pour chaque cours terminé avec succès. Ces certificats sont reconnus par l'industrie et peuvent être partagés sur LinkedIn et votre CV."
    },
    {
      id: 6,
      question: "Quelle est votre politique de remboursement ?",
      answer: "Nous offrons une garantie de remboursement de 30 jours sans questions posées. Si vous n'êtes pas satisfait de votre cours, contactez-nous dans les 30 jours pour un remboursement complet."
    },
    {
      id: 7,
      question: "Les cours sont-ils adaptés aux débutants ?",
      answer: "Nos cours sont conçus pour tous les niveaux ! Chaque cours indique clairement le niveau requis, et nous proposons des parcours progressifs pour vous accompagner depuis les bases jusqu'à l'expertise."
    },
    {
      id: 8,
      question: "Puis-je accéder aux cours sur mobile ?",
      answer: "Bien sûr ! Notre plateforme est entièrement responsive et optimisée pour tous les appareils. Apprenez où que vous soyez, sur ordinateur, tablette ou smartphone."
    }
  ];

  const toggleFAQ = (id: number) => {
    setOpenFAQ(openFAQ === id ? null : id);
  };

  return (
    <section className="w-full min-h-screen py-24 px-8 relative">

      <div className="relative z-10 max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-16 animate-fade-in-up">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 text-sm font-medium mb-4">
            Questions fréquentes
          </div>
          <h2 className="text-5xl font-bold mb-6 bg-gradient-to-r from-gray-900 via-orange-400 to-gray-900 dark:from-white dark:via-orange-400 dark:to-white bg-clip-text text-transparent">
            FAQ
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Trouvez rapidement les réponses à vos questions les plus fréquentes. 
            Notre équipe est également disponible pour vous aider personnellement.
          </p>
        </div>

        {/* FAQ Items */}
        <div className="space-y-4 mb-16">
          {faqs.map((faq, index) => (
            <div 
              key={faq.id}
              className="animate-fade-in-up"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-neutral-700/50 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
                <button
                  onClick={() => toggleFAQ(faq.id)}
                  className="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50/50 dark:hover:bg-neutral-700/50 transition-colors duration-200"
                >
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white pr-4">
                    {faq.question}
                  </h3>
                  <div className={`flex-shrink-0 w-8 h-8 bg-gradient-to-br from-orange-500 to-orange-600 rounded-full flex items-center justify-center text-white transition-transform duration-300 ${
                    openFAQ === faq.id ? 'rotate-45' : ''
                  }`}>
                    <span className="text-lg font-bold">+</span>
                  </div>
                </button>
                
                <div className={`overflow-hidden transition-all duration-300 ${
                  openFAQ === faq.id ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                }`}>
                  <div className="px-8 pb-6">
                    <div className="h-px bg-gradient-to-r from-orange-500/20 to-transparent mb-4"></div>
                    <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                      {faq.answer}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Contact CTA */}
        <div className="text-center animate-fade-in-up delay-1000">
          <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-3xl p-12 text-white shadow-2xl">
            <h3 className="text-3xl font-bold mb-4">
              Vous ne trouvez pas votre réponse ?
            </h3>
            <p className="text-xl mb-8 opacity-90">
              Notre équipe d'experts est là pour vous aider personnellement
            </p>
            <div className="flex gap-4 justify-center max-md:flex-col">
              <button className="px-8 py-4 bg-white text-orange-600 rounded-full font-semibold hover:bg-gray-100 transition-colors duration-300 transform hover:scale-105 shadow-lg">
                💬 Chat en direct
              </button>
              <button className="px-8 py-4 bg-orange-700 text-white rounded-full font-semibold hover:bg-orange-800 transition-colors duration-300 transform hover:scale-105 shadow-lg">
                📧 Nous contacter
              </button>
            </div>
          </div>
        </div>

        {/* Quick stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-20 animate-fade-in-up delay-1200">
          <div className="text-center p-6 bg-white/50 dark:bg-neutral-800/50 backdrop-blur-sm rounded-xl border border-gray-200/50 dark:border-neutral-700/50">
            <div className="text-3xl font-bold text-orange-500 mb-2">&lt; 2h</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Temps de réponse moyen</div>
          </div>
          <div className="text-center p-6 bg-white/50 dark:bg-neutral-800/50 backdrop-blur-sm rounded-xl border border-gray-200/50 dark:border-neutral-700/50">
            <div className="text-3xl font-bold text-orange-500 mb-2">24/7</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Support disponible</div>
          </div>
          <div className="text-center p-6 bg-white/50 dark:bg-neutral-800/50 backdrop-blur-sm rounded-xl border border-gray-200/50 dark:border-neutral-700/50">
            <div className="text-3xl font-bold text-orange-500 mb-2">99%</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Problèmes résolus</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQ;
