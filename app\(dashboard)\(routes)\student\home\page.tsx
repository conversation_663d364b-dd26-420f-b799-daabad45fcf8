import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { db } from "@/lib/db";
import BreadCrumb from "@/components/shared/breadcrumb";
import WelcomeWidget from "./_components/welcome-widget";
import ProgressOverview from "./_components/progress-overview";
import QuickAccess from "./_components/quick-access";
import Announcements from "./_components/announcements";
import RecentActivity from "./_components/recent-activity";
import { getPublishedAnnouncements } from "@/actions/get-announcements";

interface SearchPageProps {
  searchParams: {
    title?: string;
    categoryId?: string;
  };
}

export default async function Home({ searchParams }: SearchPageProps) {
  const session = await auth();
  const userId = session?.user.id;
  const userRole = session?.user.role;

  if (!userId) {
    return redirect("/");
  }

  // Students can access this page, but redirect others to their appropriate dashboard
  if (userRole === "ADMIN") {
    return redirect("/admin/home");
  } else if (userRole === "TEACHER") {
    return redirect("/teacher/home");
  }

  const purchases = await db.purchase.findMany({
    where: { userId },
    include: {
      course: {
        include: {
          categories: true,
          sections: {
            where: {
              isPublished: true,
            },
            include: {
              chapters: {
                where: {
                  isPublished: true,
                },
                include: {
                  userProgress: {
                    where: { userId },
                    take: 1,
                  },
                },
                orderBy: {
                  position: "asc",
                },
              },
            },
            orderBy: {
              position: "asc",
            },
          },
        },
      },
    },
  });

  const purchasedCourses = purchases
    .map((purchase) => {
      // Flatten all chapters from all sections
      const allChapters = purchase.course.sections.flatMap(
        (section) => section.chapters
      );

      const completedChapters = allChapters.filter(
        (chapter) => chapter.userProgress[0]?.isCompleted
      ).length;
      const totalChapters = allChapters.length;

      return {
        ...purchase.course,
        progress:
          totalChapters > 0
            ? Math.round((completedChapters / totalChapters) * 100)
            : 0,
        tags: purchase.course.categories.map((category) => category.name),
        chapters: allChapters.map((chapter) => ({
          id: chapter.id,
          title: chapter.title,
          position: chapter.position,
          userProgress: chapter.userProgress,
          sectionId: chapter.sectionId,
        })),
        sections: purchase.course.sections.map((section) => ({
          id: section.id,
          title: section.title,
        })),
      };
    })
    .sort((a, b) => b.progress - a.progress)
    .slice(0, 2);

  // Fetch published announcements for students
  const announcements = await getPublishedAnnouncements("STUDENTS", 5);

  return (
    <div className="relative w-full min-h-screen">
      {/* Enhanced background with more glows */}
      <div className="fixed inset-0 w-full h-full bg-white dark:bg-neutral-950 bg-dot-black/[0.03] dark:bg-dot-white/[0.03] -z-10"></div>

      {/* Multiple floating glows for more dynamic effect */}
      <div className="fixed top-10 left-10 w-96 h-96 bg-orange-400/10 rounded-full blur-3xl animate-pulse -z-10"></div>
      <div className="fixed top-20 right-20 w-80 h-80 bg-purple-400/12 rounded-full blur-3xl animate-pulse delay-1000 -z-10"></div>
      <div className="fixed bottom-20 left-20 w-72 h-72 bg-blue-400/8 rounded-full blur-3xl animate-pulse delay-500 -z-10"></div>
      <div className="fixed bottom-10 right-10 w-64 h-64 bg-orange-400/15 rounded-full blur-3xl animate-pulse delay-2000 -z-10"></div>
      <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-56 h-56 bg-white/8 rounded-full blur-3xl animate-pulse delay-1500 -z-10"></div>
      <div className="fixed top-1/3 left-1/4 w-48 h-48 bg-purple-400/10 rounded-full blur-3xl animate-pulse delay-3000 -z-10"></div>
      <div className="fixed bottom-1/3 right-1/4 w-40 h-40 bg-orange-400/12 rounded-full blur-3xl animate-pulse delay-2500 -z-10"></div>

      <div className="relative z-10 w-full flex flex-col space-y-6">
        <BreadCrumb />

        {/* Welcome Widget */}
        <WelcomeWidget
          firstName={session.user?.firstName || "Étudiant"}
          lastName={session.user?.lastName || ""}
          lastLogin={new Date(Date.now() - 2 * 60 * 60 * 1000)} // 2 hours ago - dummy data
        />

        {/* Main Dashboard Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          {/* Left Column - Progress & Quick Access */}
          <div className="xl:col-span-2 space-y-6">
            <ProgressOverview courses={purchasedCourses} />
            <QuickAccess />
          </div>

          {/* Right Column - Announcements & Activity */}
          <div className="space-y-6">
            <Announcements announcements={announcements} />
            <RecentActivity />
          </div>
        </div>
      </div>
    </div>
  );
}
