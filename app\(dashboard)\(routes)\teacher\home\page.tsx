// import { getDashboardCourses } from "@/actions/get-dashboard-courses";
import { auth } from "@/auth";
import { CoursesList } from "@/components/shared/courses-list";
import { CheckCircle, Clock } from "lucide-react";
import { redirect } from "next/navigation";
import { Spotlight } from "@/components/ui/spotlight";
import BreadCrumb from "@/components/shared/breadcrumb";
import DashboardTitle from "@/app/(dashboard)/_components/dashboard-page-title";

export default async function Home() {
  const session = await auth();
  const userId = session?.user.id;
  const userRole = session?.user.role;

  if (!userId) {
    return redirect("/");
  }

  // Ensure only teachers and admins can access this page
  if (userRole !== "TEACHER" && userRole !== "ADMIN") {
    return redirect("/dashboard");
  }

  return (
    <div className="w-full z-20 flex flex-col">
      <BreadCrumb />
      <DashboardTitle
        title={`Bon retour, ${session.user?.firstName}`}
        description="Ravi de vous revoir! <PERSON><PERSON><PERSON> vos cours et suivez vos performances
            facilement."
      />
    </div>
  );
}
