import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { db } from "@/lib/db";
import BreadCrumb from "@/components/shared/breadcrumb";
import { getCourses } from "@/actions/get-courses";
import CoursesClient from "./_components/courses-client";

export default async function Courses() {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return redirect("/");
  }

  const courses = await getCourses();

  return (
    <div className="relative w-full min-h-screen">
      {/* Enhanced background with more glows - same as student home */}
      <div className="fixed inset-0 w-full h-full bg-white dark:bg-neutral-950 bg-dot-black/[0.03] dark:bg-dot-white/[0.03] -z-10"></div>

      {/* Multiple floating glows for dynamic effect */}
      <div className="fixed top-10 left-10 w-96 h-96 bg-orange-400/10 rounded-full blur-3xl animate-pulse -z-10"></div>
      <div className="fixed top-20 right-20 w-80 h-80 bg-purple-400/12 rounded-full blur-3xl animate-pulse delay-1000 -z-10"></div>
      <div className="fixed bottom-20 left-20 w-72 h-72 bg-blue-400/8 rounded-full blur-3xl animate-pulse delay-500 -z-10"></div>
      <div className="fixed bottom-10 right-10 w-64 h-64 bg-orange-400/15 rounded-full blur-3xl animate-pulse delay-2000 -z-10"></div>
      <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-56 h-56 bg-white/8 rounded-full blur-3xl animate-pulse delay-1500 -z-10"></div>
      <div className="fixed top-1/3 left-1/4 w-48 h-48 bg-purple-400/10 rounded-full blur-3xl animate-pulse delay-3000 -z-10"></div>
      <div className="fixed bottom-1/3 right-1/4 w-40 h-40 bg-orange-400/12 rounded-full blur-3xl animate-pulse delay-2500 -z-10"></div>

      <div className="relative z-10 w-full flex flex-col">
        <div className="px-6 space-y-6">
          <BreadCrumb />

          {/* Page Header */}
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              Mes Cours
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              Gérez et suivez vos cours inscrits avec facilité, {session.user?.firstName}.
            </p>
          </div>

          {/* Main Content */}
          <CoursesClient initialCourses={courses} />
        </div>
      </div>
    </div>
  );
}
