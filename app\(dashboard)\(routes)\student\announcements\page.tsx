import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { getPublishedAnnouncements } from "@/actions/get-announcements";
import BreadCrumb from "@/components/shared/breadcrumb";
import DashboardPageTitle from "@/app/(dashboard)/_components/dashboard-page-title";
import { Separator } from "@/components/ui/separator";
import { AnnouncementCard } from "./_components/announcement-card";

const AnnouncementsPage = async () => {
  const session = await auth();

  if (!session?.user?.id) {
    return redirect("/");
  }

  if (session.user.role !== "USER") {
    return redirect("/");
  }

  // Récupérer toutes les annonces publiées pour les étudiants
  const announcements = await getPublishedAnnouncements("STUDENTS");

  return (
    <div className="w-full h-[100vh] z-20 flex flex-col">
      <BreadCrumb />
      <DashboardPageTitle
        title="Annonces & Mises à jour"
        description="Toutes les annonces et mises à jour importantes"
      />
      <Separator className="my-4" />
      
      <div className="flex-1 overflow-y-auto">
        {announcements.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 text-center">
            <div className="w-16 h-16 bg-orange-100 dark:bg-orange-950/20 rounded-full flex items-center justify-center mb-4">
              <svg
                className="w-8 h-8 text-orange-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Aucune annonce disponible
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              Les nouvelles annonces apparaîtront ici dès qu'elles seront publiées.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {announcements.map((announcement) => (
              <AnnouncementCard
                key={announcement.id}
                announcement={announcement}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default AnnouncementsPage;
