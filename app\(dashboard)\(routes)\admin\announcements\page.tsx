import React from "react";

import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { getAnnouncements } from "@/actions/get-announcements";
import BreadCrumb from "@/components/shared/breadcrumb";
import { Separator } from "@/components/ui/separator";
import DashboardPageTitle from "@/app/(dashboard)/_components/dashboard-page-title";
import { DataTable } from "./_components/data-table";
import { columns } from "./_components/columns";

const AnnouncementsPage = async () => {
  const session = await auth();

  if (!session?.user?.id) {
    return redirect("/");
  }

  if (session.user.role !== "ADMIN" && session.user.role !== "TEACHER") {
    return redirect("/");
  }

  // Récupérer toutes les annonces (brouillons et publiées)
  const announcements = await getAnnouncements({ published: undefined });

  return (
    <div className="w-full h-[100vh] z-20 flex flex-col">
      <BreadCrumb />
      <DashboardPageTitle
        title="Gestion des Annonces"
        description="Créez et gérez les annonces pour vos étudiants"
      />
      <Separator className="my-4" />
      <DataTable columns={columns} data={announcements} />
    </div>
  );
};

export default AnnouncementsPage;
