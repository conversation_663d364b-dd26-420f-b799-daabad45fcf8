import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface SortSelectorProps {
  sortOption: string;
  onSortChange: (option: string) => void;
}

export function SortSelector({ sortOption, onSortChange }: SortSelectorProps) {
  return (
    <Select value={sortOption} onValueChange={onSortChange}>
      <SelectTrigger className="w-[280px]">
        <SelectValue placeholder="Select Sorting" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectItem value="Most Recent">
            Most Recent
          </SelectItem>
          <SelectItem value="Most Popular">
            Most Popular
          </SelectItem>
        </SelectGroup>
      </SelectContent>
    </Select>
  );
}
