"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import Image from "next/image";
import { useEffect, useState } from "react";

interface BlogAuthorProps {
  firstName: string;
  lastName: string;
  createdAt: string | Date;
  content: string;
  image: string;
}

const BlogAuthor: React.FC<BlogAuthorProps> = ({
  firstName,
  lastName,
  createdAt,
  content,
  image,
}) => {
  const [readTime, setReadTime] = useState<number>(0);
  const [formattedDate, setFormattedDate] = useState<string>("");

  // Calculate readTime based on word count
  useEffect(() => {
    const words = content.split(/\s+/).length;
    const averageReadingSpeed = 200;
    const calculatedReadTime = Math.ceil(words / averageReadingSpeed);
    setReadTime(calculatedReadTime);
  }, [content]);

  // Format createdAt date
  useEffect(() => {
    const date = new Date(createdAt);
    const formatted = `Écrit le ${date
      .getDate()
      .toString()
      .padStart(2, "0")} ${date.toLocaleString("fr-FR", {
      month: "long",
    })} ${date.getFullYear()}`;
    setFormattedDate(formatted);
  }, [createdAt]);

  return (
    <div className="flex gap-3 items-center">
      <div className="relative">
        <Avatar className="w-12 h-12">
          <AvatarImage
            src={image}
            alt={firstName || ""}
            className="w-12 h-12 object-cover rounded-3xl border dark:border-orange-400 border-orange-600"
          />
          <AvatarFallback>{firstName}</AvatarFallback>
        </Avatar>
      </div>
      <div className="flex flex-col items-start">
        <p className="text-lg dark:text-gray-400 text-gray-600 font-medium">
          {firstName} {lastName}
        </p>
        <p className="text-sm text-gray-500">
          {readTime} min de lecture · {formattedDate}
        </p>
      </div>
    </div>
  );
};

export default BlogAuthor;
