import { auth } from "@/auth";
import DashboardTitle from "@/app/(dashboard)/_components/dashboard-page-title";
import BreadCrumb from "@/components/shared/breadcrumb";
import { IconBadge } from "@/components/shared/icon-badge";

import { Separator } from "@/components/ui/separator";
import { Spotlight } from "@/components/ui/spotlight";
import { GraduationCap } from "lucide-react";
import { redirect } from "next/navigation";
import React from "react";
import { db } from "@/lib/db";
import { DataTable } from "./_components/users-table/data-table";
import { columns } from "./_components/users-table/columns";

const page = async () => {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return redirect("/");
  }

  const users = await db.user.findMany({});

  return (
    <div className="h-[100vh] w-full z-20 flex flex-col">
      <BreadCrumb />
      <DashboardTitle
        title="Administration des Utilisateurs"
        description="Ravi de vous revoir! G<PERSON>rez vos cours, suivez vos performances, et administrez vos utilisateurs avec facilité et efficacité."
      />
      <Separator />
      <DataTable columns={columns} data={users} />
    </div>
  );
};

export default page;
