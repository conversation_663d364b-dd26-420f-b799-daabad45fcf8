import { db } from "@/lib/db";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import Link from "next/link";
import {
  ArrowLeft,
  BookOpen,
  CircleDollarSign,
  FileText,
  ImageIcon,
  ListChecks,
  ListTodo,
  SquareCheck,
  Tag,
  User,
  Video,
} from "lucide-react";
import { Separator } from "@/components/ui/separator";
import DashboardPageTitle from "@/app/(dashboard)/_components/dashboard-page-title";
import { DashboardSection } from "@/app/(dashboard)/_components/dashboard-section";
import { TitleForm } from "./_components/title-form";
import { DescriptionForm } from "./_components/description-form";
import { ImageForm } from "./_components/image-form";
import { CategoryForm } from "./_components/category-form";
import { AttachmentForm } from "./_components/attachment-form";
import { SectionsForm } from "./_components/sections-form";
import { Actions } from "./_components/actions";
import { OverviewForm } from "./_components/overview-form";
import { LearnForm } from "./_components/learn-form";
import { PrerequisitesForm } from "./_components/prerequisites-form";
import { CourseIncludesForm } from "./_components/course-includes-form";
import { PriceForm } from "./_components/price-form";
import { InstructorForm } from "./_components/instructor-form";

const CourseIdPage = async ({ params }: { params: { courseId: string } }) => {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return redirect("/");
  }

  const course = await db.course.findUnique({
    where: {
      id: params.courseId,
      userId,
    },
    include: {
      sections: {
        include: {
          chapters: {
            orderBy: {
              position: "asc",
            },
          },
        },
        orderBy: {
          position: "asc",
        },
      },
      attachments: {
        orderBy: {
          createdAt: "desc",
        },
      },
      categories: true,
    },
  });

  const categories = await db.category.findMany({
    orderBy: {
      name: "asc",
    },
  });

  if (!course) {
    return redirect("/");
  }

  const requiredFields = [
    course.title,
    course.description,
    course.imageUrl,
    course.price,
    course.overview,
    course.categories?.length > 0,
    course.sections.some((section) =>
      section.chapters.some((chapter) => chapter.isPublished)
    ),
  ];

  const totalFields = requiredFields.length;
  const completedFields = requiredFields.filter(Boolean).length;
  const completionText = `(${completedFields}/${totalFields})`;
  const isComplete = requiredFields.every(Boolean);

  return (
    <div className="w-full z-20 flex flex-col">
      <Link
        href={`/teacher/courses`}
        className="flex items-center text-sm hover:opacity-75 transition mb-6"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Retour aux cours
      </Link>

      <div className="flex items-start justify-between gap-y-4 max-2xl:flex-col">
        <DashboardPageTitle
          title="Configuration du cours"
          description={`Champs complétés ${completionText}`}
        />
        <Actions
          disabled={!isComplete}
          courseId={params.courseId}
          isPublished={course.isPublished}
        />
      </div>

      <Separator className="mt-6 mb-8" />

      <div className="w-full h-full flex items-start justify-start gap-12 max-xl:flex-col max-sm:gap-6">
        {/* Colonne de gauche */}
        <div className="w-full flex flex-col grow gap-2 justify-between">
          <div className="flex flex-col gap-12">
            {/* Titre du cours */}
            <div className="flex flex-col gap-4">
              <DashboardSection
                icon={BookOpen}
                title="Titre du cours"
                description="Définissez un titre attractif pour votre cours"
              />
              <TitleForm initialData={course} courseId={course.id} />
            </div>

            {/* Description du cours */}
            <div className="flex flex-col gap-4">
              <DashboardSection
                icon={FileText}
                title="Description du cours"
                description="Rédigez une description claire de votre cours"
              />
              <DescriptionForm initialData={course} courseId={course.id} />
            </div>

            {/* Aperçu du cours */}
            <div className="flex flex-col gap-4">
              <DashboardSection
                icon={ListTodo}
                title="Aperçu du cours"
                description="Fournissez un aperçu détaillé de votre cours"
              />
              <OverviewForm initialData={course} courseId={course.id} />
            </div>

            {/* Ce que vous apprendrez */}
            <div className="flex flex-col gap-4">
              <DashboardSection
                icon={SquareCheck}
                title="Ce que vous apprendrez"
                description="Listez les compétences que les étudiants acquerront"
              />
              <LearnForm initialData={course} courseId={course.id} />
            </div>

            {/* Prérequis */}
            <div className="flex flex-col gap-4">
              <DashboardSection
                icon={ListChecks}
                title="Prérequis"
                description="Listez les prérequis nécessaires"
              />
              <PrerequisitesForm initialData={course} courseId={course.id} />
            </div>

            {/* Ce que comprend le cours */}
            <div className="flex flex-col gap-4">
              <DashboardSection
                icon={Tag}
                title="Contenu du cours"
                description="Listez ce qui est inclus dans le cours"
              />
              <CourseIncludesForm initialData={course} courseId={course.id} />
            </div>
          </div>
        </div>

        {/* Séparateur vertical */}
        <Separator orientation="vertical" decorative className="h-full" />

        {/* Colonne de droite */}
        <div className="w-full flex flex-col grow gap-2 justify-between">
          {/* Image du cours */}
          <div className="flex flex-col gap-12">
            <div className="flex flex-col gap-4">
              <DashboardSection
                icon={ImageIcon}
                title="Image du cours"
                description="Téléchargez une image représentative de votre cours"
              />
              <ImageForm initialData={course} courseId={course.id} />
            </div>

            {/* Instructeur */}
            <div className="flex flex-col gap-4">
              <DashboardSection
                icon={User}
                title="Instructeur"
                description="Sélectionnez l'instructeur du cours"
              />
              <InstructorForm initialData={course} courseId={course.id} />
            </div>

            {/* Catégories */}
            <div className="flex flex-col gap-4">
              <DashboardSection
                icon={Tag}
                title="Catégories"
                description="Sélectionnez les catégories pertinentes"
              />
              <CategoryForm
                initialData={course}
                courseId={course.id}
                options={categories.map((category) => ({
                  label: category.name,
                  value: category.id,
                  type: category.type,
                }))}
              />
            </div>

            {/* Prix */}
            <div className="flex flex-col gap-4">
              <DashboardSection
                icon={CircleDollarSign}
                title="Prix"
                description="Définissez le prix du cours"
              />
              <PriceForm initialData={course} courseId={course.id} />
            </div>

            {/* Structure du cours */}
            <div className="flex flex-col gap-4">
              <DashboardSection
                icon={ListTodo}
                title="Structure du cours"
                description="Organisez vos sections et chapitres"
              />
              <SectionsForm initialData={course} courseId={course.id} />
            </div>

            {/* Fichiers joints */}
            <div className="flex flex-col gap-4">
              <DashboardSection
                icon={FileText}
                title="Fichiers joints"
                description="Ajoutez des documents complémentaires"
              />
              <AttachmentForm initialData={course} courseId={course.id} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseIdPage;
