"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { FileUpload } from "@/components/shared/file-upload";
import { Attachment, Course } from "@prisma/client";
import { PlusCircle, X, Trash2 } from "lucide-react";
import axios from "axios";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { formatDate } from "@/lib/utils";

interface AttachmentFormProps {
  initialData: Course & { attachments: Attachment[] };
  courseId: string;
}

const fileIcons: Record<string, string> = {
  pdf: "/assets/icons/files/pdf.png",
  doc: "/assets/icons/files/doc.png",
  docx: "/assets/icons/files/doc.png",
  xls: "/assets/icons/files/xls.png",
  xlsx: "/assets/icons/files/xls.png",
  ppt: "/assets/icons/files/ppt.png",
  pptx: "/assets/icons/files/ppt.png",
  jpg: "/assets/icons/files/jpg.png",
  jpeg: "/assets/icons/files/jpg.png",
  png: "/assets/icons/files/png.png",
  gif: "/assets/icons/files/gif.png",
  mp4: "/assets/icons/files/mp4.png",
};

export const AttachmentForm = ({
  initialData,
  courseId,
}: AttachmentFormProps) => {
  const router = useRouter();
  const { toast } = useToast();
  const [isUploading, setIsUploading] = useState(false);

  const handleFileAction = async (action: "add" | "delete", data: any) => {
    try {
      setIsUploading(true);
      if (action === "add") {
        await axios.post(`/api/courses/${courseId}/attachments`, data);
        toast({
          title: "Fichier ajouté",
          description: "La pièce jointe a été ajoutée avec succès",
        });
      } else {
        await axios.delete(`/api/courses/${courseId}/attachments/${data}`);
        toast({
          title: "Fichier supprimé",
          description: "La pièce jointe a été retirée",
        });
      }
      router.refresh();
    } catch {
      toast({
        title: "Erreur",
        description: `Une erreur est survenue lors de ${
          action === "add" ? "l'ajout" : "la suppression"
        } du fichier`,
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split(".").pop()?.toLowerCase() || "";
    const iconSrc = fileIcons[extension];

    return iconSrc ? (
      <Image
        src={iconSrc}
        alt={extension.toUpperCase()}
        width={48}
        height={48}
        className="object-contain"
      />
    ) : (
      <div className="w-12 h-12 flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-lg">
        <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
          {extension}
        </span>
      </div>
    );
  };

  return (
    <div className="space-y-4 w-full">
      <div className="flex justify-between items-center">
        <Button
          variant="outline"
          onClick={() => setIsUploading(!isUploading)}
          disabled={isUploading}
          className="flex items-center gap-2"
        >
          {isUploading ? (
            <>
              <X className="h-4 w-4" />
              Annuler
            </>
          ) : (
            <>
              <PlusCircle className="h-4 w-4" />
              Ajouter un fichier
            </>
          )}
        </Button>
      </div>

      {isUploading && (
        <FileUpload
          endpoint="courseAttachment"
          onChange={(fileData) => {
            if (fileData) {
              handleFileAction("add", fileData);
              setIsUploading(false);
            }
          }}
        />
      )}

      {initialData.attachments.length > 0 ? (
        <div className="grid gap-3">
          {initialData.attachments.map((attachment) => (
            <div
              key={attachment.id}
              className="w-full flex flex-wrap items-center gap-4 p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-neutral-800 transition-colors group"
            >
              {getFileIcon(attachment.name)}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">
                  {attachment.name}
                </p>
                <p className="text-xs text-muted-foreground">
                  Ajouté le {formatDate(String(attachment.createdAt))}
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleFileAction("delete", attachment.id)}
                disabled={isUploading}
                className="text-red-600 hover:bg-red-600/90 dark:text-red-600 dark:hover:text-white dark:hover:bg-red-600/90 opacity-0 group-hover:opacity-100 transition-all flex items-center gap-2 px-3 py-1.5 rounded-md"
              >
                <Trash2 className="h-4 w-4" />
                <span className="hidden sm:inline">Supprimer</span>
              </Button>
            </div>
          ))}
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center py-8 text-center">
          <p className="text-sm text-muted-foreground">
            Aucune pièce jointe pour l'instant
          </p>
          <p className="text-xs text-muted-foreground mt-2">
            Ajoutez des documents, images ou vidéos complémentaires
          </p>
        </div>
      )}
    </div>
  );
};
