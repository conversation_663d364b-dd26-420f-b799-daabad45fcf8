import { Badge } from "@/components/ui/badge";

interface TagSelectorProps {
  allTags: string[];
  selectedTags: string[];
  onTagClick: (tag: string) => void;
}

export function TagSelector({
  allTags,
  selectedTags,
  onTagClick,
}: TagSelectorProps) {
  return (
    <div className="flex flex-col gap-6 w-[50%] max-md:w-full self-start">
      <h3 className="dash-text">Search By Tags</h3>
      <div className="flex flex-wrap gap-3 max-md:w-full">
        {allTags.map((tag) => (
          <Badge
            key={tag}
            variant="outline"
            onClick={() => onTagClick(tag)}
            className={`${
              selectedTags.includes(tag) ? "bg-orange-600" : ""
            } cursor-pointer text-sm`}
          >
            {tag}
          </Badge>
        ))}
      </div>
    </div>
  );
}
