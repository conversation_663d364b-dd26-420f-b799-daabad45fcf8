import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Ra<PERSON><PERSON><PERSON> } from "@/components/shared/radial-chart";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { BookOpen, Clock, Target, Calendar } from "lucide-react";

interface ProgressOverviewProps {
  courses: {
    id: string;
    title: string;
    progress: number;
    tags: string[];
    chapters: {
      id: string;
      title: string;
      position: number;
      userProgress: {
        isCompleted: boolean;
      }[];
    }[];
  }[];
}

const ProgressOverview = ({ courses }: ProgressOverviewProps) => {
  // Calculate overall stats
  const totalCourses = courses.length;
  const completedCourses = courses.filter(course => course.progress === 100).length;
  const averageProgress = totalCourses > 0
    ? Math.round(courses.reduce((sum, course) => sum + course.progress, 0) / totalCourses)
    : 0;

  // Dummy upcoming deadlines
  const upcomingDeadlines = [
    { title: "Quiz Algèbre Linéaire", date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), course: "Mathématiques" },
    { title: "Projet Physique", date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), course: "Physique" },
  ];

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="w-5 h-5 text-orange-500" />
            Aperçu des Progrès
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Current Courses */}
            <div className="text-center p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
              <BookOpen className="w-8 h-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-blue-600">{totalCourses}</div>
              <div className="text-sm text-gray-600 dark:text-gray-300">Cours Actifs</div>
              <div className="text-xs text-gray-500 mt-1">{completedCourses} terminés</div>
            </div>

            {/* Overall Progress */}
            <div className="text-center p-4 bg-orange-50 dark:bg-orange-950/20 rounded-lg">
              <Target className="w-8 h-8 text-orange-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-orange-600">{averageProgress}%</div>
              <div className="text-sm text-gray-600 dark:text-gray-300">Progression Moyenne</div>
              <Progress value={averageProgress} className="mt-2 h-2" />
            </div>

            {/* Upcoming Deadlines */}
            <div className="text-center p-4 bg-red-50 dark:bg-red-950/20 rounded-lg">
              <Clock className="w-8 h-8 text-red-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-red-600">{upcomingDeadlines.length}</div>
              <div className="text-sm text-gray-600 dark:text-gray-300">Échéances Proches</div>
              <div className="text-xs text-gray-500 mt-1">Cette semaine</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Course Progress Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {courses.map((course) => {
          const completedChapters = course.chapters.filter(
            (chapter) => chapter.userProgress[0]?.isCompleted
          ).length;
          const totalChapters = course.chapters.length;

          return (
            <RadialChart
              key={course.id}
              title={course.title}
              progress={Math.round(course.progress)}
              footerText={`${completedChapters} / ${totalChapters} chapitres terminés`}
              tags={course.tags}
            >
              {course.progress === 100 ? (
                <Badge variant="default" className="bg-green-500 hover:bg-green-600">
                  ✓ Terminé
                </Badge>
              ) : (
                <Button size="sm" variant={"default"} className="w-fit">
                  Continuer
                </Button>
              )}
            </RadialChart>
          );
        })}
      </div>

      {/* Upcoming Deadlines */}
      {upcomingDeadlines.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="w-5 h-5 text-orange-500" />
              Échéances Prochaines
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {upcomingDeadlines.map((deadline, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-neutral-800/50 rounded-lg">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">{deadline.title}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">{deadline.course}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-orange-600">
                      {deadline.date.toLocaleDateString('fr-FR')}
                    </div>
                    <div className="text-xs text-gray-500">
                      Dans {Math.ceil((deadline.date.getTime() - Date.now()) / (1000 * 60 * 60 * 24))} jours
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ProgressOverview;
