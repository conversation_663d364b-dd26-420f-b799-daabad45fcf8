import { db } from "@/lib/db";
import { Attachment, Chapter, Section } from "@prisma/client";

interface GetChapterProps {
  userId: string;
  courseId: string;
  sectionId: string;
  chapterId: string;
}

export const getChapter = async ({
  userId,
  courseId,
  sectionId,
  chapterId,
}: GetChapterProps) => {
  try {
    // Check if user purchased the course
    const purchase = await db.purchase.findUnique({
      where: {
        userId_courseId: {
          userId,
          courseId,
        },
      },
    });

    // Get course details
    const course = await db.course.findUnique({
      where: {
        isPublished: true,
        id: courseId,
      },
      select: {
        price: true,
      },
    });

    // Get the current section
    const section = await db.section.findUnique({
      where: {
        id: sectionId,
        isPublished: true,
        courseId,
      },
    });

    // Get the current chapter
    const chapter = await db.chapter.findUnique({
      where: {
        id: chapterId,
        isPublished: true,
        sectionId,
      },
    });

    if (!chapter || !course || !section) {
      throw new Error("Chapter, Section, or Course not found!");
    }

    let muxData = null;
    let attachments: Attachment[] = [];
    let nextChapter: Chapter | null = null;

    // If purchased, get all course attachments
    if (purchase) {
      attachments = await db.attachment.findMany({
        where: {
          courseId,
        },
      });
    }

    // If chapter is free or course is purchased
    if (chapter.isFree || purchase) {
      // Get MUX data for video playback
      muxData = await db.muxData.findUnique({
        where: {
          chapterId,
        },
      });

      // Find next chapter in the same section first
      nextChapter = await db.chapter.findFirst({
        where: {
          sectionId,
          isPublished: true,
          position: {
            gt: chapter.position,
          },
        },
        orderBy: {
          position: "asc",
        },
      });

      // If no next chapter in current section, find first chapter in next section
      if (!nextChapter) {
        const nextSection = await db.section.findFirst({
          where: {
            courseId,
            isPublished: true,
            position: {
              gt: section.position,
            },
          },
          orderBy: {
            position: "asc",
          },
          include: {
            chapters: {
              where: {
                isPublished: true,
              },
              orderBy: {
                position: "asc",
              },
              take: 1,
            },
          },
        });

        if (nextSection && nextSection.chapters.length > 0) {
          nextChapter = nextSection.chapters[0];
        }
      }
    }

    // Get user progress for this chapter
    const userProgress = await db.userProgress.findUnique({
      where: {
        userId_chapterId: {
          userId,
          chapterId,
        },
      },
    });

    return {
      chapter,
      section,
      course,
      muxData,
      attachments,
      nextChapter,
      userProgress,
      purchase,
    };
  } catch (error) {
    console.log("[GET_CHAPTER]", error);
    return {
      chapter: null,
      section: null,
      course: null,
      muxData: null,
      attachments: [],
      nextChapter: null,
      userProgress: null,
      purchase: null,
    };
  }
};
