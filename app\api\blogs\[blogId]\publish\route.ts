import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/lib/db";

export async function PATCH(
  req: Request,
  { params }: { params: { blogId: string } }
) {
  try {
    const session = await auth();
    const userId = session?.user.id;

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const blog = await db.blog.findUnique({
      where: {
        id: params.blogId,
        userId,
      },
      include: {
        categories: true,
      },
    });

    if (!blog) {
      return new NextResponse("Not Found", { status: 404 });
    }

    if (
      !blog.title ||
      !blog.description ||
      !blog.image ||
      blog.categories.length === 0
    ) {
      return new NextResponse("Missing required fields", { status: 400 });
    }

    // Publish the blog
    const publishedBlog = await db.blog.update({
      where: {
        id: params.blogId,
        userId,
      },
      data: {
        isPublished: true,
      },
    });

    return NextResponse.json(publishedBlog);
  } catch (error) {
    console.log("[BLOG_ID_PUBLISH]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}
