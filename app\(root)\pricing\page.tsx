import React from "react";
import ComparisonTable from "./comparison-table";
import PricingSection from "./pricing-section";
import Title from "@/components/shared/Title";
import { Spotlight } from "@/components/ui/spotlight";

const page: React.FC = () => {
  return (
    <div className=" overflow-hidden dark:bg-grid-white/[0.04] bg-grid-black/[0.04] relative w-full flex flex-col items-center justify-center gap-3 p-32 max-lg:p-8">
      <div className="absolute pointer-events-none inset-0 flex items-center justify-center dark:bg-black bg-white [mask-image:radial-gradient(ellipse_50%_30%_at_top,transparent_40%,black)]"></div>
      <Spotlight
        className="-top-100 left-0 md:left-60 md:-top-20 opacity-10"
        fill="orange"
      />
      <div className="max-w-[100rem]">
        <Title
          label="Nos formules"
          title="Choisissez la formule qui vous convient le mieux"
          description=""
        />

        <div className="relative w-full mt-12">
          <PricingSection />
          <ComparisonTable />
        </div>
      </div>
    </div>
  );
};

export default page;
