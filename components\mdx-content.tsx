"use client";

import { MDXRemote } from "next-mdx-remote/rsc";
import { Prism as <PERSON>ynta<PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-syntax-highlighter";
import { oneDark } from "react-syntax-highlighter/dist/esm/styles/prism";

interface MDXContentProps {
  content: string;
}

const components = {
  code: ({ className, children, ...props }: any) => {
    const match = /language-(\w+)/.exec(className || "");
    return match ? (
      <SyntaxHighlighter
        style={oneDark}
        language={match[1]}
        PreTag="div"
        {...props}
      >
        {String(children).replace(/\n$/, "")}
      </SyntaxHighlighter>
    ) : (
      <code className={className} {...props}>
        {children}
      </code>
    );
  },
  h1: ({ children }: any) => (
    <h1 className="text-3xl font-bold mb-6 text-gray-900 dark:text-gray-100">
      {children}
    </h1>
  ),
  h2: ({ children }: any) => (
    <h2 className="text-2xl font-semibold mb-4 text-gray-900 dark:text-gray-100">
      {children}
    </h2>
  ),
  h3: ({ children }: any) => (
    <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-gray-100">
      {children}
    </h3>
  ),
  p: ({ children }: any) => (
    <p className="mb-4 text-gray-700 dark:text-gray-300 leading-relaxed">
      {children}
    </p>
  ),
  ul: ({ children }: any) => (
    <ul className="mb-4 pl-6 list-disc text-gray-700 dark:text-gray-300">
      {children}
    </ul>
  ),
  ol: ({ children }: any) => (
    <ol className="mb-4 pl-6 list-decimal text-gray-700 dark:text-gray-300">
      {children}
    </ol>
  ),
  li: ({ children }: any) => (
    <li className="mb-1">{children}</li>
  ),
  blockquote: ({ children }: any) => (
    <blockquote className="border-l-4 border-orange-500 pl-4 py-2 mb-4 bg-orange-50 dark:bg-orange-950/20 text-gray-700 dark:text-gray-300 italic">
      {children}
    </blockquote>
  ),
  a: ({ href, children }: any) => (
    <a
      href={href}
      className="text-orange-600 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300 underline"
      target="_blank"
      rel="noopener noreferrer"
    >
      {children}
    </a>
  ),
  strong: ({ children }: any) => (
    <strong className="font-semibold text-gray-900 dark:text-gray-100">
      {children}
    </strong>
  ),
  em: ({ children }: any) => (
    <em className="italic text-gray-700 dark:text-gray-300">
      {children}
    </em>
  ),
  table: ({ children }: any) => (
    <div className="overflow-x-auto mb-4">
      <table className="min-w-full border border-gray-300 dark:border-gray-600">
        {children}
      </table>
    </div>
  ),
  th: ({ children }: any) => (
    <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 bg-gray-100 dark:bg-gray-800 font-semibold text-left">
      {children}
    </th>
  ),
  td: ({ children }: any) => (
    <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
      {children}
    </td>
  ),
};

export const MDXContent = ({ content }: MDXContentProps) => {
  if (!content) {
    return (
      <div className="text-gray-500 dark:text-gray-400 italic">
        Aucun contenu détaillé disponible.
      </div>
    );
  }

  return (
    <div className="mdx-content">
      <MDXRemote source={content} components={components} />
    </div>
  );
};
