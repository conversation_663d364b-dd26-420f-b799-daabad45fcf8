import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ArrowUpDown, Calendar, TrendingUp } from "lucide-react";

interface SortSelectorProps {
  sortOption: string;
  onSortChange: (option: string) => void;
}

export function SortSelector({ sortOption, onSortChange }: SortSelectorProps) {
  const sortOptions = [
    {
      value: "Most Recent",
      label: "Plus récents",
      icon: Calendar,
      description: "Triés par date de publication"
    },
    {
      value: "Most Popular",
      label: "Plus populaires",
      icon: TrendingUp,
      description: "Triés par popularité"
    }
  ];

  return (
    <div className="flex flex-col gap-2 w-full lg:w-[35%]">
      <div className="flex items-center gap-2">
        <ArrowUpDown className="w-5 h-5 text-orange-500" />
        <span className="text-lg font-semibold text-gray-900 dark:text-white">
          Trier par
        </span>
      </div>

      <Select value={sortOption || "Most Recent"} onValueChange={onSortChange}>
        <SelectTrigger className="w-full bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm border border-gray-200/50 dark:border-neutral-700/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 focus:ring-2 focus:ring-orange-500 focus:border-transparent py-6">
          <SelectValue placeholder="Choisir un tri">
            <div className="flex items-center gap-2">
              {sortOptions.find(option => option.value === sortOption)?.icon && (
                <div className="w-4 h-4">
                  {React.createElement(sortOptions.find(option => option.value === sortOption)!.icon, {
                    className: "w-4 h-4 text-orange-500"
                  })}
                </div>
              )}
              <span>{sortOptions.find(option => option.value === sortOption)?.label || "Sélectionner"}</span>
            </div>
          </SelectValue>
        </SelectTrigger>
        <SelectContent className="bg-white/95 dark:bg-neutral-800/95 backdrop-blur-sm border border-gray-200/50 dark:border-neutral-700/50 rounded-xl shadow-xl">
          <SelectGroup>
            {sortOptions.map((option) => (
              <SelectItem
                key={option.value}
                value={option.value}
                className="cursor-pointer hover:bg-orange-50 dark:hover:bg-orange-900/20 transition-colors duration-200 rounded-lg m-1"
              >
                <div className="flex items-center gap-3 py-2">
                  <option.icon className="w-4 h-4 text-orange-500" />
                  <div className="flex flex-col">
                    <span className="font-medium">{option.label}</span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {option.description}
                    </span>
                  </div>
                </div>
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  );
}

// Add React import for createElement
import React from "react";
