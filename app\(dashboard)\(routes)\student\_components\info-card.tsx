import { IconBadge } from "@/components/shared/icon-badge";
import { LucideIcon } from "lucide-react";

interface infoCardProps {
  numberOfItems: number;
  variant?: "default" | "success";
  label: string;
  icon: LucideIcon;
}

export const InfoCard = ({
  variant,
  icon: Icon,
  label,
  numberOfItems,
}: infoCardProps) => {
  return (
    <div className="border rounded-lg min-w-80 p-6">
      <div className="flex flex-col gap-4">
        <p className="font-medium text-gray-500">{label}</p>
        <p className="text-white text-3xl font-semibold">
          {numberOfItems} {numberOfItems === 1 ? "Course" : "Courses"}
        </p>
      </div>
    </div>
  );
};
