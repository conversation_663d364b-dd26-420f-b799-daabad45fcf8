import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { 
  BookOpen, 
  Clock, 
  Users, 
  Star, 
  Calendar, 
  Award, 
  Play,
  Download,
  Share2
} from "lucide-react";
import Image from "next/image";

interface CourseOverviewProps {
  course: {
    id: string;
    title: string;
    description: string;
    imageUrl: string;
    instructor: string;
    instructorImage: string;
    category: string;
    level: string;
    duration: string;
    totalChapters: number;
    completedChapters: number;
    progress: number;
    rating: number;
    totalStudents: number;
    lastAccessed: Date;
    enrolledDate: Date;
    certificate: boolean;
  };
}

const CourseOverview = ({ course }: CourseOverviewProps) => {
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'débutant': return 'bg-green-100 text-green-800 dark:bg-green-950/20 dark:text-green-400';
      case 'intermédiaire': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-950/20 dark:text-yellow-400';
      case 'avancé': return 'bg-red-100 text-red-800 dark:bg-red-950/20 dark:text-red-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-950/20 dark:text-gray-400';
    }
  };

  return (
    <div className="space-y-6">
      {/* Hero Section */}
      <Card className="overflow-hidden">
        <div className="relative h-64 md:h-80">
          <Image
            src={course.imageUrl || "/images/placeholder-course.jpg"}
            alt={course.title}
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
          
          {/* Course Info Overlay */}
          <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
            <div className="flex flex-col md:flex-row md:items-end md:justify-between gap-4">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                    {course.category}
                  </Badge>
                  <Badge className={getLevelColor(course.level)}>
                    {course.level}
                  </Badge>
                </div>
                <h1 className="text-2xl md:text-4xl font-bold mb-2">{course.title}</h1>
                <p className="text-white/90 text-lg line-clamp-2">{course.description}</p>
              </div>
              
              <div className="flex gap-2">
                <Button size="lg" className="bg-orange-500 hover:bg-orange-600">
                  <Play className="w-5 h-5 mr-2" />
                  Continuer
                </Button>
                <Button size="lg" variant="outline" className="border-white/30 text-white hover:bg-white/10">
                  <Share2 className="w-5 h-5" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Stats & Progress */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Progress Card */}
        <Card>
          <CardContent className="p-6 text-center">
            <div className="w-16 h-16 bg-orange-100 dark:bg-orange-950/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <BookOpen className="w-8 h-8 text-orange-600" />
            </div>
            <div className="text-3xl font-bold text-orange-600 mb-1">{Math.round(course.progress)}%</div>
            <div className="text-sm text-gray-600 dark:text-gray-300 mb-3">Progression</div>
            <Progress value={course.progress} className="h-2" />
            <div className="text-xs text-gray-500 mt-2">
              {course.completedChapters}/{course.totalChapters} chapitres
            </div>
          </CardContent>
        </Card>

        {/* Duration Card */}
        <Card>
          <CardContent className="p-6 text-center">
            <div className="w-16 h-16 bg-blue-100 dark:bg-blue-950/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Clock className="w-8 h-8 text-blue-600" />
            </div>
            <div className="text-3xl font-bold text-blue-600 mb-1">{course.duration}</div>
            <div className="text-sm text-gray-600 dark:text-gray-300">Durée totale</div>
          </CardContent>
        </Card>

        {/* Students Card */}
        <Card>
          <CardContent className="p-6 text-center">
            <div className="w-16 h-16 bg-green-100 dark:bg-green-950/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Users className="w-8 h-8 text-green-600" />
            </div>
            <div className="text-3xl font-bold text-green-600 mb-1">{course.totalStudents.toLocaleString()}</div>
            <div className="text-sm text-gray-600 dark:text-gray-300">Étudiants</div>
          </CardContent>
        </Card>

        {/* Rating Card */}
        <Card>
          <CardContent className="p-6 text-center">
            <div className="w-16 h-16 bg-yellow-100 dark:bg-yellow-950/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Star className="w-8 h-8 text-yellow-600" />
            </div>
            <div className="text-3xl font-bold text-yellow-600 mb-1">{course.rating.toFixed(1)}</div>
            <div className="text-sm text-gray-600 dark:text-gray-300">Note moyenne</div>
            <div className="flex justify-center mt-2">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`w-4 h-4 ${
                    i < Math.floor(course.rating)
                      ? "text-yellow-400 fill-current"
                      : "text-gray-300"
                  }`}
                />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Instructor & Course Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Instructor Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5 text-orange-500" />
              Votre Instructeur
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <div className="relative w-16 h-16">
                <Image
                  src={course.instructorImage || "/images/default-instructor.jpg"}
                  alt={course.instructor}
                  fill
                  className="object-cover rounded-full"
                />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-lg text-gray-900 dark:text-white">
                  {course.instructor}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Professeur de {course.category}
                </p>
                <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                  <span>Expert certifié</span>
                  <span>•</span>
                  <span>5+ années d'expérience</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Course Info Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="w-5 h-5 text-orange-500" />
              Informations du Cours
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-300">Date d'inscription:</span>
              <span className="font-medium">{formatDate(course.enrolledDate)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-300">Dernier accès:</span>
              <span className="font-medium">{formatDate(course.lastAccessed)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-300">Certificat:</span>
              <div className="flex items-center gap-2">
                {course.certificate ? (
                  <>
                    <Award className="w-4 h-4 text-green-500" />
                    <span className="text-green-600 font-medium">Disponible</span>
                  </>
                ) : (
                  <span className="text-gray-500">Non disponible</span>
                )}
              </div>
            </div>
            {course.certificate && course.progress === 100 && (
              <Button className="w-full mt-4" variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Télécharger le Certificat
              </Button>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CourseOverview;
