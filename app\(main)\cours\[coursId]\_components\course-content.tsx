"use client";

import React, { useState } from "react";
import { ChevronDown, ChevronUp, Play, File, Clock, Lock } from "lucide-react";

interface Chapter {
  id: string;
  title: string;
  lessons: Lesson[];
}

interface Lesson {
  id: string;
  title: string;
  duration: string;
  type: "video" | "article" | "quiz";
  isPreview?: boolean;
}

interface CourseContentProps {
  chapters: Chapter[];
}

const CourseContent = ({ chapters }: CourseContentProps) => {
  const [expandedChapters, setExpandedChapters] = useState<string[]>([
    chapters[0]?.id,
  ]);

  const toggleChapter = (chapterId: string) => {
    setExpandedChapters((prev) =>
      prev.includes(chapterId)
        ? prev.filter((id) => id !== chapterId)
        : [...prev, chapterId]
    );
  };

  const totalLessons = chapters.reduce(
    (acc, chapter) => acc + chapter.lessons.length,
    0
  );

  const getIconForLessonType = (type: string) => {
    switch (type) {
      case "video":
        return Play;
      case "article":
        return File;
      case "quiz":
        return Clock;
      default:
        return File;
    }
  };

  return (
    <section className="py-14">
      <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-300 mb-6">
        Course Content
      </h1>
      <div className="course-container">
        <div className="flex items-center justify-between mb-8">
          <div className="text-course-gray">
            <span>{chapters.length} sections</span>
            <span className="mx-2">•</span>
            <span>{totalLessons} lessons</span>
          </div>
        </div>

        <div className="glass-card divide-y rounded-xl border border-color">
          {chapters.map((chapter, index) => {
            const isExpanded = expandedChapters.includes(chapter.id);

            return (
              <div key={chapter.id}>
                <button
                  className="w-full px-6 py-4 flex items-center justify-between  transition-colors duration-200"
                  onClick={() => toggleChapter(chapter.id)}
                >
                  <div className="flex items-center text-left">
                    <div className="w-8 h-8 rounded-full bg-course-light-blue flex items-center justify-center mr-4 flex-shrink-0">
                      <span className="text-course-blue font-medium">
                        {index + 1}
                      </span>
                    </div>
                    <div>
                      <h3 className="font-medium text-lg text-course-dark">
                        {chapter.title}
                      </h3>
                      <div className="text-sm text-course-gray mt-1">
                        <span>{chapter.lessons.length} lessons</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center">
                    {isExpanded ? (
                      <ChevronUp className="h-5 w-5 text-course-gray" />
                    ) : (
                      <ChevronDown className="h-5 w-5 text-course-gray" />
                    )}
                  </div>
                </button>

                {isExpanded && (
                  <div className="border-t border-gray-100">
                    {chapter.lessons.map((lesson) => {
                      const IconComponent = getIconForLessonType(lesson.type);

                      return (
                        <div
                          key={lesson.id}
                          className="px-6 py-3 flex items-center justify-between transition-colors duration-200"
                        >
                          <div className="flex items-center">
                            <div
                              className={`w-8 h-8 rounded-full flex items-center justify-center mr-4 flex-shrink-0 ${
                                lesson.isPreview
                                  ? "bg-green-100"
                                  : "bg-gray-100"
                              }`}
                            >
                              <IconComponent
                                className={`h-4 w-4 ${
                                  lesson.isPreview
                                    ? "text-green-600"
                                    : "text-gray-500"
                                }`}
                              />
                            </div>

                            <div className="flex-1">
                              <h4 className="text-course-dark">
                                {lesson.title}
                              </h4>
                              <div className="flex items-center text-sm text-course-gray mt-1">
                                <Clock className="h-3 w-3 mr-1" />
                                <span>{lesson.duration}</span>
                              </div>
                            </div>
                          </div>

                          {lesson.isPreview ? (
                            <span className="text-xs font-medium px-2 py-1 rounded-full bg-green-100 text-green-700">
                              Preview
                            </span>
                          ) : (
                            <Lock className="h-4 w-4 text-course-gray" />
                          )}
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default CourseContent;
