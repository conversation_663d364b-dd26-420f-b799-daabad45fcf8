import { NavbarRoutes } from "@/app/(dashboard)/_components/navbar-routes";
import { Chapter, Course, Section, UserProgress } from "@prisma/client";
import { CourseMobileSidebar } from "./course-mobile-sidebar";

interface CourseNavbarProps {
  course: Course & {
    sections: (Section & {
      chapters: (Chapter & {
        userProgress: UserProgress[] | null;
      })[];
    })[];
  };
  progressCount: number;
}

export const CourseNavbar = ({ course, progressCount }: CourseNavbarProps) => {
  return (
    <div className="p-4 border-b h-full flex items-center bg-white dark:bg-gray-900 shadow-sm">
      <CourseMobileSidebar course={course} progressCount={progressCount} />
      <NavbarRoutes />
    </div>
  );
};
