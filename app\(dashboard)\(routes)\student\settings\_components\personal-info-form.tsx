"use client";

import { onboard } from "@/actions/onboarding";
import { useUploadThing } from "@/lib/uploadthing";
import { isBase64Image } from "@/lib/utils";
import { OnboardingSchema } from "@/schemas";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter, usePathname } from "next/navigation";
import React, { useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  Form,
} from "@/components/ui/form";
import { FormError } from "@/components/uicomps/form-error";
import { FormSuccess } from "@/components/uicomps/form-success";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import ProfileImage from "@/components/auth/profile-image";
import { useImageUpload } from "@/hooks/use-image-upload";
import { z } from "zod";

const PersonalInfoForm = ({ user }: { user: any }) => {
  const [error, setError] = useState<string | undefined>("");
  const [success, setSuccess] = useState<string | undefined>("");
  const [isPending, startTransition] = useTransition();
  const { handleImage, files } = useImageUpload();
  const { startUpload } = useUploadThing("media");
  const router = useRouter();
  const pathname = usePathname();

  const form = useForm<z.infer<typeof OnboardingSchema>>({
    resolver: zodResolver(OnboardingSchema),
    defaultValues: {
      email: user?.email || "",
      image: user?.image || "",
      username: user?.username || "",
      firstName: user?.firstName || "",
      lastName: user?.lastName || "",
      bio: user?.bio || "",
      phone: user?.phone || "",
    },
  });

  const onSubmit = async (values: z.infer<typeof OnboardingSchema>) => {
    const blob = values.image;
    const hasImageChanged = isBase64Image(blob);
    // if (hasImageChanged) {
    //   const imgRes = await startUpload(files);
    //   if (imgRes && imgRes[0].fileUrl) {
    //     values.image = imgRes[0].fileUrl;
    //   }
    // }
    startTransition(() => {
      onboard(values)
        .then((data) => {
          setError(data.error);
          setSuccess(data.success);
        })
        .catch(() => setError("something went wrong!"));
    });

    router.refresh();
  };

  return (
    <div className="w-[720px] max-xl:w-full">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full flex flex-col space-y-6"
        >
          <div className="W-full space-y-8">
            <FormField
              control={form.control}
              name="image"
              render={({ field }) => (
                <ProfileImage
                  value={field.value}
                  onChange={(e) => handleImage(e, field.onChange)}
                />
              )}
            />
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem className="mt-8 w-full grid grid-cols-[1fr_3fr] space-x-12 max-xl:grid-cols-1 max-xl:space-x-0 max-xl:space-y-4 items-center">
                  <FormLabel>Prénom</FormLabel>
                  <FormControl className="w-auto">
                    <Input
                      {...field}
                      disabled={isPending}
                      placeholder="Votre prénom..."
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem className="mt-8 w-full grid grid-cols-[1fr_3fr] space-x-12 max-xl:grid-cols-1 max-xl:space-x-0 max-xl:space-y-4 items-center">
                  <FormLabel>Nom</FormLabel>
                  <FormControl className="w-auto">
                    <Input
                      {...field}
                      disabled={isPending}
                      placeholder="Votre nom..."
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem className="mt-8 w-full grid grid-cols-[1fr_3fr] space-x-12 max-xl:grid-cols-1 max-xl:space-x-0 max-xl:space-y-4 items-center">
                  <FormLabel>Email</FormLabel>
                  <FormControl className="w-auto">
                    <Input {...field} disabled />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem className="mt-8 w-full grid grid-cols-[1fr_3fr] space-x-12 max-xl:grid-cols-1 max-xl:space-x-0 max-xl:space-y-4 items-center">
                  <FormLabel>Numéro de téléphone</FormLabel>
                  <FormControl className="w-auto">
                    <Input
                      {...field}
                      disabled={isPending}
                      placeholder="Votre numéro de téléphone..."
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem className="mt-8 w-full grid grid-cols-[1fr_3fr] space-x-12 max-xl:grid-cols-1 max-xl:space-x-0 max-xl:space-y-4 items-center">
                  <FormLabel>Nom d'utilisateur</FormLabel>
                  <FormControl className="w-auto">
                    <Input
                      {...field}
                      disabled={isPending}
                      placeholder="Votre nom d'utilisateur..."
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="bio"
              render={({ field }) => (
                <FormItem className="mt-8 w-full grid grid-cols-[1fr_3fr] space-x-12 max-xl:grid-cols-1 max-xl:space-x-0 max-xl:space-y-4 items-start">
                  <FormLabel>Votre bio</FormLabel>
                  <FormControl className="w-auto">
                    <Textarea
                      {...field}
                      disabled={isPending}
                      placeholder="Votre bio..."
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <FormError message={error} />
          <FormSuccess message={success} />
          <Button type="submit" className="w-fit self-end justify-self-end">
            Confirmer
          </Button>
        </form>
      </Form>
    </div>
  );
};

export default PersonalInfoForm;
