"use client";

import React, { useState } from "react";
import { useToast } from "@/hooks/use-toast";

// Enhanced ContactForm component
const ContactForm: React.FC = () => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    setTimeout(() => {
      toast({
        title: "Message envoyé !",
        description: "Nous vous répondrons dans les plus brefs délais.",
      });
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        subject: '',
        message: ''
      });
      setIsSubmitting(false);
    }, 1000);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  return (
    <div className="bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm rounded-2xl p-8 border border-gray-200/50 dark:border-neutral-700/50 shadow-xl">
      <div className="mb-8">
        <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Envoyez-nous un message
        </h3>
        <p className="text-gray-600 dark:text-gray-300">
          Nous sommes là pour répondre à toutes vos questions
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Prénom *
            </label>
            <input
              type="text"
              name="firstName"
              value={formData.firstName}
              onChange={handleChange}
              required
              className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-neutral-600 bg-white dark:bg-neutral-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200"
              placeholder="Votre prénom"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Nom *
            </label>
            <input
              type="text"
              name="lastName"
              value={formData.lastName}
              onChange={handleChange}
              required
              className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-neutral-600 bg-white dark:bg-neutral-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200"
              placeholder="Votre nom"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Email *
          </label>
          <input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            required
            className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-neutral-600 bg-white dark:bg-neutral-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200"
            placeholder="<EMAIL>"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Téléphone
          </label>
          <input
            type="tel"
            name="phone"
            value={formData.phone}
            onChange={handleChange}
            className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-neutral-600 bg-white dark:bg-neutral-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200"
            placeholder="+212 6 XX XX XX XX"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Sujet *
          </label>
          <input
            type="text"
            name="subject"
            value={formData.subject}
            onChange={handleChange}
            required
            className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-neutral-600 bg-white dark:bg-neutral-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200"
            placeholder="Sujet de votre message"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Message *
          </label>
          <textarea
            name="message"
            value={formData.message}
            onChange={handleChange}
            required
            rows={5}
            className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-neutral-600 bg-white dark:bg-neutral-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 resize-none"
            placeholder="Décrivez votre demande en détail..."
          />
        </div>

        <button
          type="submit"
          disabled={isSubmitting}
          className="w-full py-4 px-6 bg-gradient-to-r from-orange-500 to-orange-600 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
        >
          {isSubmitting ? (
            <div className="flex items-center justify-center gap-2">
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              Envoi en cours...
            </div>
          ) : (
            <div className="flex items-center justify-center gap-2">
              <span>Envoyer le message</span>
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
            </div>
          )}
        </button>

        <p className="text-sm text-gray-500 dark:text-gray-400 text-center">
          Nous vous répondrons dans les <span className="font-semibold text-orange-600">24 heures</span>
        </p>
      </form>
    </div>
  );
};

// Sous-composant InputField
const InputField: React.FC<{
  id: string;
  label: string;
  placeholder: string;
  type?: string;
}> = ({ id, label, placeholder, type = "text" }) => {
  return (
    <div>
      <label htmlFor={id} className="sr-only">
        {label}
      </label>
      <input
        type={type}
        id={id}
        className="py-3 px-4 block w-full border-gray-200 rounded-lg text-sm focus:border-orange-500 focus:ring-orange-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600"
        placeholder={placeholder}
      />
    </div>
  );
};

// Sous-composant TextAreaField
const TextAreaField: React.FC<{
  id: string;
  label: string;
  placeholder: string;
}> = ({ id, label, placeholder }) => {
  return (
    <div>
      <label htmlFor={id} className="sr-only">
        {label}
      </label>
      <textarea
        id={id}
        rows={4}
        className="py-3 px-4 block w-full border-gray-200 rounded-lg text-sm focus:border-orange-500 focus:ring-orange-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600"
        placeholder={placeholder}
      />
    </div>
  );
};

// Sous-composant IconBlock
const IconBlock: React.FC<{
  title: string;
  description: string;
  linkText: string;
  href: string;
}> = ({ title, description, linkText, href }) => {
  return (
    <div className="flex gap-x-7 py-6">
      <div className="grow">
        <h3 className="font-semibold text-gray-800 dark:text-neutral-200">
          {title}
        </h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-neutral-500">
          {description}
        </p>
        <a
          className="mt-2 inline-flex items-center gap-x-2 text-sm font-medium text-gray-600 hover:text-gray-800 focus:outline-none focus:text-gray-800 dark:text-neutral-400 dark:hover:text-neutral-200 dark:focus:text-neutral-200"
          href={href}
        >
          {linkText}
        </a>
      </div>
    </div>
  );
};

const ContactUs: React.FC = () => {
  return (
    <section className="w-full min-h-screen py-24 px-8 relative">

      <div className="relative z-10 max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-16 animate-fade-in-up">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 text-sm font-medium mb-4">
            Contactez-nous
          </div>
          <h2 className="text-5xl font-bold mb-6 bg-gradient-to-r from-gray-900 via-orange-400 to-gray-900 dark:from-white dark:via-orange-400 dark:to-white bg-clip-text text-transparent">
            Restons en contact
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Une question ? Un projet ? Notre équipe d'experts est là pour vous accompagner
            dans votre parcours d'apprentissage.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-16 items-start">
          {/* Contact Form */}
          <div className="animate-fade-in-up">
            <ContactForm />
          </div>

          {/* Contact Information */}
          <div className="animate-fade-in-up delay-300">
            <div className="space-y-8">
              <div className="bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm rounded-2xl p-8 border border-gray-200/50 dark:border-neutral-700/50 shadow-lg">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                  Nos coordonnées
                </h3>

                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center flex-shrink-0">
                      <span className="text-white text-lg">📧</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white">Email</h4>
                      <p className="text-gray-600 dark:text-gray-300 text-sm mb-1">
                        Notre équipe vous répond rapidement
                      </p>
                      <a href="mailto:<EMAIL>" className="text-orange-600 hover:text-orange-700 font-medium">
                        <EMAIL>
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center flex-shrink-0">
                      <span className="text-white text-lg">💬</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white">Chat en direct</h4>
                      <p className="text-gray-600 dark:text-gray-300 text-sm mb-1">
                        Support instantané disponible
                      </p>
                      <button className="text-orange-600 hover:text-orange-700 font-medium">
                        Démarrer une conversation
                      </button>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center flex-shrink-0">
                      <span className="text-white text-lg">📍</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white">Adresse</h4>
                      <p className="text-gray-600 dark:text-gray-300 text-sm mb-1">
                        Venez nous rendre visite
                      </p>
                      <p className="text-gray-700 dark:text-gray-300">
                        ENSA AGADIR<br />
                        Avenue Tamsoult<br />
                        Agadir - 80000, Maroc
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0">
                      <span className="text-white text-lg">📞</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white">Téléphone</h4>
                      <p className="text-gray-600 dark:text-gray-300 text-sm mb-1">
                        Lun-Ven, 8h-17h
                      </p>
                      <a href="tel:+212621075329" className="text-orange-600 hover:text-orange-700 font-medium">
                        +212 6-21075329
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick stats */}
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-white/60 dark:bg-neutral-800/60 backdrop-blur-sm rounded-xl p-6 border border-gray-200/50 dark:border-neutral-700/50 text-center">
                  <div className="text-2xl font-bold text-orange-500 mb-1">&lt; 24h</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Temps de réponse</div>
                </div>
                <div className="bg-white/60 dark:bg-neutral-800/60 backdrop-blur-sm rounded-xl p-6 border border-gray-200/50 dark:border-neutral-700/50 text-center">
                  <div className="text-2xl font-bold text-orange-500 mb-1">24/7</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Support disponible</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactUs;
