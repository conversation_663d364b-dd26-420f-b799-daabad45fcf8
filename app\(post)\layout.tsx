import type { <PERSON>ada<PERSON> } from "next";
import { ThemeProvider } from "@/utils/theme-provider";
import Navbar from "@/components/shared/Navbar";
import "../globals.css";
import Footer from "@/components/shared/Footer";
import { SidebarProvider } from "@/components/ui/sidebar";

export const metadata: Metadata = {
  title: "Alephnull",
  description: "My startup",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div>
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
        <SidebarProvider defaultOpen={false}>
          <Navbar />
        </SidebarProvider>
        {children}
        <Footer />
      </ThemeProvider>
    </div>
  );
}
