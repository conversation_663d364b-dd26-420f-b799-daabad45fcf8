import React from "react";
import Image from "next/image";

interface ContactCardProps {
  image: string;
  title: string;
  subtitle: string;
  desc: string;
}

const ContactCard: React.FC<ContactCardProps> = ({
  image,
  title,
  subtitle,
  desc,
}) => {
  return (
    <div className="flex min-w-[220px] max-w-[260px] flex-col items-start gap-4">
      <Image src={image} alt={title} width={24} height={24} />
      <p className="p-text font-medium text-black">{title}</p>
      <p className="desc-text text-left">{subtitle}</p>
      <p className="text-teal-700 font-inter text-md font-semibold leading-6">
        {desc}
      </p>
    </div>
  );
};

export default ContactCard;
