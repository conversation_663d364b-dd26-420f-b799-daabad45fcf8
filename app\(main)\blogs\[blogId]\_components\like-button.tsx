"use client"; // Mark this component as a Client Component

import { useState } from "react";
import { Heart } from "lucide-react";
import axios from "axios";

interface LikeButtonProps {
  blogId: string;
  initialLikes: number;
  isLikedbyUser: boolean | undefined;
}

const LikeButton = ({
  blogId,
  initialLikes,
  isLikedbyUser,
}: LikeButtonProps) => {
  const [likes, setLikes] = useState(initialLikes);
  const [isLiked, setIsLiked] = useState(isLikedbyUser);

  const handleLike = async () => {
    try {
      const response = await axios.post(`/api/blogs/${blogId}/like`);

      if (response.data.message === "Blog liké") {
        setLikes((prev) => prev + 1);
        setIsLiked(true);
      } else if (response.data.message === "Like retiré") {
        setLikes((prev) => prev - 1);
        setIsLiked(false);
      }
    } catch (error) {
      console.error("Erreur:", error);
    }
  };

  return (
    <button
      onClick={handleLike}
      className="flex items-center gap-2"
      aria-label={isLiked ? "Retirer le like" : "Ajouter un like"}
    >
      <Heart
        className={isLiked ? "text-red-500 fill-red-500" : "text-gray-500"}
      />
      <span>{likes}</span>
    </button>
  );
};

export default LikeButton;
