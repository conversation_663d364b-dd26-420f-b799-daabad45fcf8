import React from "react";
import { Check } from "lucide-react";

interface WhatYoullLearnProps {
  items: string[] | undefined;
}

const WhatYoullLearn = ({ items }: WhatYoullLearnProps) => {
  return (
    <section className="py-14">
      <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-300 mb-6">
        What you'll learn
      </h1>
      <div className="border border-color p-6 md:p-8 rounded-xl">
        <div className="grid md:grid-cols-2 gap-x-10 gap-y-5">
          {items?.map((item, index) => (
            <div
              key={index}
              className="flex items-start group staggered-fade-in"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="flex-shrink-0 p-1 bg-course-light-blue rounded-full mr-3 mt-0.5 group-hover:bg-course-blue transition-colors duration-300">
                <Check className="h-4 w-4 text-course-blue group-hover:text-orange-500 transition-colors duration-300" />
              </div>
              <p className="text-course-dark">{item}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default WhatYoullLearn;
