// Import necessary modules and components
import { posts } from "#site/content"; // Importing posts data
import { MDXContent } from "@/lib/mdx/mdx-components"; // Component to render MDX content
import { notFound } from "next/navigation"; // Function to handle 404 Not Found
import Image from "next/image"; // Next.js Image component
import moment from "moment"; // Library for date formatting
import "@/styles/mdx.css"; // Custom styles for MDX content
import { Metadata } from "next"; // Type for metadata
import { siteConfig } from "@/config/site"; // Site configuration
import Title from "@/components/shared/Title"; // Shared Title component
import { Badge } from "@/components/ui/badge"; // UI Badge component
import { TracingBeam } from "@/components/ui/tracing-beam"; // UI Tracing Beam component
import User from "@/components/cards/User"; // User card component
import { Spotlight } from "@/components/ui/spotlight"; // UI Spotlight component
import Footer from "@/components/shared/Footer"; // Shared Footer component

// Interface for PostPageProps to define the expected params
interface PostPageProps {
  params: {
    slug: string[];
  };
}

// Function to get a post object from the params
async function getPostFromParams(params: PostPageProps["params"]) {
  const slug = params?.slug?.join("/"); // Join slug array into a single string
  return posts.find((post: any) => post.slugAsParams === slug); // Find post by slug
}

// Function to generate metadata for the post page
export async function generateMetadata({
  params,
}: PostPageProps): Promise<Metadata> {
  const post = await getPostFromParams(params);

  // Return empty object if no post is found
  if (!post) {
    return {};
  }

  // Set Open Graph (OG) search parameters for SEO
  const ogSearchParams = new URLSearchParams();
  ogSearchParams.set("title", post.title);

  return {
    title: post.title,
    description: post.description,
    authors: { name: siteConfig.author },
    openGraph: {
      title: post.title,
      description: post.description,
      type: "article",
      url: post.slug,
      images: [
        {
          url: `/api/og?${ogSearchParams.toString()}`,
          width: 1200,
          height: 630,
          alt: post.title,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: post.title,
      description: post.description,
      images: [`/api/og?${ogSearchParams.toString()}`],
    },
  };
}

// Function to generate static paths for all posts
export async function generateStaticParams(): Promise<
  PostPageProps["params"][]
> {
  return posts.map((post) => ({ slug: post.slugAsParams.split("/") })); // Split slug string into array
}

// Main component for displaying a single post page
export default async function PostPage({ params }: PostPageProps) {
  const post = await getPostFromParams(params);

  // If no post found or the post is not published, show 404 page
  if (!post || !post.published) {
    notFound();
  }

  // Format the post date using moment.js
  const formattedDate = moment(post.date).format("LL");

  // Render the post page
  return (
    <div className="dark:bg-grid-white/[0.04] bg-grid-black/[0.04] relative w-full flex flex-col items-center justify-center gap-3">
      {/* Background effect */}
      <div className="absolute pointer-events-none inset-0 flex items-center justify-center dark:bg-black bg-white [mask-image:radial-gradient(ellipse_50%_30%_at_top,transparent_40%,black)]"></div>

      {/* Spotlight and TracingBeam effects */}
      <Spotlight
        className="-top-100 left-0 md:left-60 md:-top-20 opacity-10"
        fill="orange"
      />
      <TracingBeam className="z-20 paddings">
        <div className="max-w-[1000px] flex items-center flex-col justify-center">
          {/* Post title, description, and user info */}
          <div className="w-full flex flex-col gap-4">
            <h1 className="blog-title">{post.title}</h1>
            <p className="p-text text-left">{post.description}</p>
            <div className="w-full flex items-end justify-between max-md:flex-col max-md:gap-4 max-md:items-start">
              <User
                image="/assets/instructors/ihind.jpg"
                level={`8 min read • ${formattedDate}`}
                name="Hamza Ihind"
              />
              <div className="flex justify-end gap-2">
                {post.tags?.map((tag) => (
                  <Badge key={tag} variant={"outline"}>
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          {/* Post content */}
          <article className="mt-4 prose-lg max-sm:prose-base dark:prose-invert prose-p:text-gray-300 prose-li:text-gray-300">
            {post.image && (
              <Image
                src={post.image}
                alt={post.title}
                width={1920}
                height={100}
                className="my-8"
              />
            )}
            <MDXContent code={post.body} />
          </article>
        </div>
      </TracingBeam>
    </div>
  );
}
