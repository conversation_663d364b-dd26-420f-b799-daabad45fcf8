"use client";

import { Category } from "@prisma/client";
import { CategoryItem } from "./category-item";
import { colorMap } from "@/styles/color-map";

interface CategoriesProps {
  items: Category[];
}

export const Categories = ({ items }: CategoriesProps) => {
  return (
    <div className="flex flex-wrap items-center gap-2 overflow-x-auto pb-8">
      {items.map((item) => (
        <CategoryItem
          key={item.id}
          label={item.name}
          color={colorMap[item.name]}
          value={item.id}
        />
      ))}
    </div>
  );
};
