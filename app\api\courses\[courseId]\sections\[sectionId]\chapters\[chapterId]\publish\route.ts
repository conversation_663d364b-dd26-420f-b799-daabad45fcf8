import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/lib/db";

export async function PATCH(
  req: Request,
  { params }: { params: { courseId: string; chapterId: string } }
) {
  try {
    const session = await auth();
    const userId = session?.user.id;

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Verify that the course belongs to the user
    const ownCourse = await db.course.findUnique({
      where: {
        id: params.courseId,
        userId,
      },
    });

    if (!ownCourse) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Fetch the chapter and its associated section
    const chapter = await db.chapter.findUnique({
      where: {
        id: params.chapterId,
        section: {
          courseId: params.courseId,
        },
      },
      include: {
        muxData: true, // Include Mux data for validation
      },
    });

    if (!chapter) {
      return new NextResponse("Chapter not found", { status: 404 });
    }

    // Validate required fields
    if (
      !chapter.title ||
      !chapter.description ||
      !chapter.videoUrl ||
      !chapter.muxData?.assetId
    ) {
      return new NextResponse("Missing required fields", { status: 400 });
    }

    // Publish the chapter
    const publishedChapter = await db.chapter.update({
      where: {
        id: params.chapterId,
      },
      data: {
        isPublished: true,
      },
    });

    return NextResponse.json(publishedChapter);
  } catch (error) {
    console.log("[CHAPTER_PUBLISH_ERROR]: ", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}
