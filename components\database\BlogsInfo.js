import { BlogTags } from "./BlogTags";

export const BlogsInfo = [
  {
    id: 1,
    name: "<PERSON><PERSON> Ihind",
    date: "24 Mar 2023",
    image: "/assets/BG1.jpg",
    tags: [BlogTags.chemistry, BlogTags.stats],
    title: "UX review presentation",
    desc: "How do you create compelling presentations that wow your colleagues and impress your managers?",
  },
  {
    id: 2,
    name: "Sara Aqnouch",
    date: "02 Apr 2022",
    image: "/assets/BG2.jpg",
    tags: [BlogTags.thermo, BlogTags.algebra],
    title: "Migrating to Linear 101",
    desc: "Linear helps streamline software projects, sprints, tasks, and bug tracking. Here’s how to get...",
  },
  {
    id: 3,

    name: "<PERSON><PERSON> Ihind",
    date: "24 Mar 2023",
    image: "/assets/BG1.jpg",
    tags: [BlogTags.proba, BlogTags.stats],
    title: "UX review presentation",
    desc: "How do you create compelling presentations that wow your colleagues and impress your managers?",
  },
  {
    id: 4,

    name: "<PERSON> Aqnouch",
    date: "02 Apr 2022",
    image: "/assets/BG2.jpg",
    tags: [BlogTags.thermo, BlogTags.cs],
    title: "Migrating to Linear 101",
    desc: "Linear helps streamline software projects, sprints, tasks, and bug tracking. Here’s how to get...",
  },
  {
    id: 5,

    name: "Hamza Ihind",
    date: "24 Mar 2023",
    image: "/assets/BG1.jpg",
    tags: [BlogTags.chemistry, BlogTags.stats],
    title: "UX review presentation",
    desc: "How do you create compelling presentations that wow your colleagues and impress your managers?",
  },
  {
    id: 6,

    name: "Sara Aqnouch",
    date: "02 Apr 2022",
    image: "/assets/BG2.jpg",
    tags: [BlogTags.thermo, BlogTags.algebra],
    title: "Migrating to Linear 101",
    desc: "Linear helps streamline software projects, sprints, tasks, and bug tracking. Here’s how to get...",
  },
  {
    id: 7,

    name: "Hamza Ihind",
    date: "24 Mar 2023",
    image: "/assets/BG1.jpg",
    tags: [BlogTags.proba, BlogTags.stats],
    title: "UX review presentation",
    desc: "How do you create compelling presentations that wow your colleagues and impress your managers?",
  },
  {
    id: 8,

    name: "Sara Aqnouch",
    date: "02 Apr 2022",
    image: "/assets/BG2.jpg",
    tags: [BlogTags.thermo, BlogTags.cs],
    title: "Migrating to Linear 101",
    desc: "Linear helps streamline software projects, sprints, tasks, and bug tracking. Here’s how to get...",
  },
  {
    id: 9,

    name: "Hamza Ihind",
    date: "24 Mar 2023",
    image: "/assets/BG1.jpg",
    tags: [BlogTags.chemistry, BlogTags.stats],
    title: "UX review presentation",
    desc: "How do you create compelling presentations that wow your colleagues and impress your managers?",
  },
  {
    id: 10,

    name: "Sara Aqnouch",
    date: "02 Apr 2022",
    image: "/assets/BG2.jpg",
    tags: [BlogTags.thermo, BlogTags.algebra],
    title: "Migrating to Linear 101",
    desc: "Linear helps streamline software projects, sprints, tasks, and bug tracking. Here’s how to get...",
  },
  {
    id: 11,

    name: "Hamza Ihind",
    date: "24 Mar 2023",
    image: "/assets/BG1.jpg",
    tags: [BlogTags.proba, BlogTags.stats],
    title: "UX review presentation",
    desc: "How do you create compelling presentations that wow your colleagues and impress your managers?",
  },
  {
    id: 12,

    name: "Sara Aqnouch",
    date: "02 Apr 2022",
    image: "/assets/BG2.jpg",
    tags: [BlogTags.thermo, BlogTags.cs],
    title: "Migrating to Linear 101",
    desc: "Linear helps streamline software projects, sprints, tasks, and bug tracking. Here’s how to get...",
  },
];
