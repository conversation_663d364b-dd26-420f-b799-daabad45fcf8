import React from "react";

const Pattern = () => {
  return (
    <div className="w-[1920px] h-[1440px] relative inset-0 bg-gradient-to-b from-transparent via-transparent to-white opacity-30">
      <div className="left-0 top-0 absolute justify-center items-center inline-flex">
        <div className="self-stretch border border-gray-300 flex-col justify-start items-start inline-flex">
          <div className="justify-start items-start inline-flex">
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
          </div>
          <div className="justify-start items-start inline-flex">
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
          </div>
          <div className="justify-start items-start inline-flex">
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
          </div>
          <div className="justify-start items-start inline-flex">
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
          </div>
          <div className="justify-start items-start inline-flex">
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
          </div>
          <div className="justify-start items-start inline-flex">
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
          </div>
          <div className="justify-start items-start inline-flex">
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
          </div>
          <div className="justify-start items-start inline-flex">
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
          </div>
          <div className="justify-start items-start inline-flex">
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
          </div>
          <div className="justify-start items-start inline-flex">
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
          </div>
          <div className="justify-start items-start inline-flex">
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
          </div>
          <div className="justify-start items-start inline-flex">
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
          </div>
          <div className="justify-start items-start inline-flex">
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
          </div>
          <div className="justify-start items-start inline-flex">
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
          </div>
          <div className="justify-start items-start inline-flex">
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
          </div>
          <div className="justify-start items-start inline-flex">
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
          </div>
          <div className="justify-start items-start inline-flex">
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
          </div>
          <div className="justify-start items-start inline-flex">
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
          </div>
          <div className="justify-start items-start inline-flex">
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
          </div>
          <div className="justify-start items-start inline-flex">
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
          </div>
          <div className="justify-start items-start inline-flex">
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
          </div>
          <div className="justify-start items-start inline-flex">
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative bg-gray-100 border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
            <div className="w-16 h-16 relative border-r border-b border-gray-300" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Pattern;
