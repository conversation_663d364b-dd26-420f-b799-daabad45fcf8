import { db } from "@/lib/db";
import { auth } from "@/auth";

import { redirect } from "next/navigation";
import { IconBadge } from "@/components/shared/icon-badge";
import {
  ArrowLeft,
  CircleDollarSign,
  File,
  Image,
  LayoutDashboard,
  ListChecks,
  PencilLine,
  Tag,
  Text,
} from "lucide-react";

import { Banner } from "@/components/shared/banner";
import { Spotlight } from "@/components/ui/spotlight";
import DashboardTitle from "@/app/(dashboard)/_components/dashboard-page-title";
import { Separator } from "@/components/ui/separator";
import SectionTitle from "../../../student/settings/_components/section-title";
import Link from "next/link";
import TitleBlogForm from "./_components/title-blog-form";
import { DescriptionBlogForm } from "./_components/description-blog-form";
import { ImageBlogForm } from "./_components/image-blog-form";
import { TagsBlogForm } from "./_components/tags-blog-form";
import { Actions } from "./_components/actions";
import DashboardPageTitle from "@/app/(dashboard)/_components/dashboard-page-title";
import { ContentBlogForm } from "./_components/content-blog-form";
import Blog from "@/components/cards/Blog";
import { NotionLogoIcon } from "@radix-ui/react-icons";
import LaTeXRenderer from "@/components/shared/latex-renderer";

const CourseIdPage = async ({ params }: { params: { blogId: string } }) => {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return redirect("/");
  }

  const blog = await db.blog.findUnique({
    where: {
      id: params.blogId,
      userId,
    },
    include: {
      categories: true,
    },
  });

  const categories = await db.category.findMany({
    orderBy: {
      name: "asc",
    },
  });

  if (!blog) {
    return redirect("/");
  }

  const requiredFields = [
    blog.title,
    blog.description,
    blog.image,
    blog.categories,
    blog.content,
  ];

  const totalFields = requiredFields.length;
  const completedFields = requiredFields.filter(Boolean).length;

  const completionText = `(${completedFields}/${totalFields})`;

  const isComplete = requiredFields.every(Boolean);

  return (
    <div className="w-full z-20 flex flex-col">
      <Link
        href={`/teacher/blogs`}
        className="flex items-center text-sm hover:opacity-75 transition mb-6"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Retour au tableau des blogs
      </Link>
      <div className="flex items-start justify-between gap-y-4 max-2xl:flex-col">
        <DashboardPageTitle
          title="Configuration du blog"
          description={`Complétez tous les champs ${completionText}`}
        />
        <Actions
          disabled={!isComplete}
          blogId={params.blogId}
          isPublished={blog.isPublished}
        />
      </div>
      <Separator decorative className="my-8" />
      <div className="w-full h-full flex items-start justify-start gap-16 max-xl:flex-col max-sm:gap-6">
        <div className="w-[60%] max-xl:w-full flex flex-col grow gap-2 justify-between">
          <div className="flex flex-col gap-8">
            <div className="flex items-center gap-x-4">
              <IconBadge icon={Text} />
              <div>
                <h2 className="big-text">Contenu du blog</h2>
                <p className="desc-text">Rédigez le contenu du blog</p>
              </div>
            </div>
            <TitleBlogForm blogId={blog.id} initialData={blog} />
            <ContentBlogForm blogId={blog.id} initialData={blog} />
          </div>
        </div>
        <Separator
          orientation="vertical"
          decorative
          className="h-full bg-white"
        />
        <div className="w-[40%] max-xl:w-full flex flex-col shrink-0 gap-2">
          <div className="flex flex-col gap-8 mb-12">
            <div className="flex items-center gap-x-4">
              <IconBadge icon={PencilLine} />
              <div>
                <h2 className="big-text">Description du blog</h2>
                <p className="desc-text">
                  Rédigez une description claire pour la blog
                </p>
              </div>
            </div>
            <DescriptionBlogForm blogId={blog.id} initialData={blog} />
          </div>
          <div className="flex flex-col gap-8 mb-12">
            <div className="flex items-center gap-x-4">
              <IconBadge icon={Image} />
              <div>
                <h2 className="big-text">Image du blog</h2>
                <p className="desc-text">
                  Rédigez une description claire pour la blog
                </p>
              </div>
            </div>
            <ImageBlogForm blogId={blog.id} initialData={blog} />
          </div>

          <div className="flex flex-col gap-8 mb-12">
            <div className="flex items-center gap-x-4">
              <IconBadge icon={Tag} />
              <div>
                <h2 className="big-text">Tags du blog</h2>
                <p className="desc-text">
                  Rédigez une description claire pour la blog
                </p>
              </div>
            </div>
            <TagsBlogForm
              blogId={blog.id}
              initialData={blog}
              options={categories.map((category) => ({
                label: category.name,
                value: category.id,
                type: category.type,
              }))}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseIdPage;
