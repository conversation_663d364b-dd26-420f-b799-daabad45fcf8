import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/lib/db";

export async function PATCH(
  req: Request,
  { params }: { params: { courseId: string; sectionId: string } }
) {
  try {
    const session = await auth();
    const userId = session?.user.id;

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { title, description, position } = await req.json();

    const section = await db.section.update({
      where: {
        id: params.sectionId,
        courseId: params.courseId,
      },
      data: {
        title,
        description,
        position,
      },
    });

    return NextResponse.json(section);
  } catch (error) {
    console.log("[SECTION_UPDATE_ERROR]: ", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}

export async function DELETE(
  req: Request,
  { params }: { params: { courseId: string; sectionId: string } }
) {
  try {
    const session = await auth();
    const userId = session?.user.id;

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const section = await db.section.delete({
      where: {
        id: params.sectionId,
        courseId: params.courseId,
      },
    });

    return NextResponse.json(section);
  } catch (error) {
    console.log("[SECTION_DELETE_ERROR]: ", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}
