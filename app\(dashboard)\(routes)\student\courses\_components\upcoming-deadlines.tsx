import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar, Clock, AlertTriangle, FileText, HelpCircle, BookOpen, ArrowRight } from "lucide-react";

interface Deadline {
  id: string;
  title: string;
  type: "assignment" | "quiz" | "test";
  course: string;
  dueDate: Date;
  priority: "high" | "medium" | "low";
  description?: string;
}

interface UpcomingDeadlinesProps {
  deadlines: Deadline[];
}

const UpcomingDeadlines = ({ deadlines }: UpcomingDeadlinesProps) => {
  // Dummy data if no deadlines provided
  const defaultDeadlines: Deadline[] = [
    {
      id: "1",
      title: "Quiz Chapitre 5: Intégrales",
      type: "quiz",
      course: "Mathématiques Avancées",
      dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days
      priority: "high",
      description: "Quiz sur les techniques d'intégration"
    },
    {
      id: "2",
      title: "Devoir: <PERSON>",
      type: "assignment",
      course: "Physique Mécanique",
      dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days
      priority: "medium",
      description: "Résoudre 10 problèmes sur les lois de Newton"
    },
    {
      id: "3",
      title: "Examen Final: Algèbre Linéaire",
      type: "test",
      course: "Algèbre Linéaire",
      dueDate: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000), // 10 days
      priority: "high",
      description: "Examen couvrant tous les chapitres"
    },
    {
      id: "4",
      title: "Quiz: Dérivées Partielles",
      type: "quiz",
      course: "Calcul Différentiel",
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      priority: "low",
      description: "Quiz rapide sur les dérivées partielles"
    }
  ];

  const displayDeadlines = deadlines.length > 0 ? deadlines : defaultDeadlines;

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "assignment": return FileText;
      case "quiz": return HelpCircle;
      case "test": return BookOpen;
      default: return Calendar;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "assignment": return "Devoir";
      case "quiz": return "Quiz";
      case "test": return "Examen";
      default: return "Échéance";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "bg-red-100 text-red-800 dark:bg-red-950/20 dark:text-red-400";
      case "medium": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-950/20 dark:text-yellow-400";
      case "low": return "bg-green-100 text-green-800 dark:bg-green-950/20 dark:text-green-400";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-950/20 dark:text-gray-400";
    }
  };

  const formatDueDate = (date: Date) => {
    const now = new Date();
    const diffInDays = Math.ceil((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) return "Aujourd'hui";
    if (diffInDays === 1) return "Demain";
    if (diffInDays < 7) return `Dans ${diffInDays} jours`;
    return date.toLocaleDateString('fr-FR');
  };

  const getDaysUntilDue = (date: Date) => {
    const now = new Date();
    return Math.ceil((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
  };

  // Sort deadlines by due date
  const sortedDeadlines = displayDeadlines.sort((a, b) => a.dueDate.getTime() - b.dueDate.getTime());

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-orange-500" />
            Échéances Prochaines
          </CardTitle>
          <Button variant="ghost" size="sm">
            Voir tout
            <ArrowRight className="w-4 h-4 ml-1" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {sortedDeadlines.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <Calendar className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>Aucune échéance prochaine</p>
          </div>
        ) : (
          <div className="space-y-4">
            {sortedDeadlines.map((deadline, index) => {
              const TypeIcon = getTypeIcon(deadline.type);
              const daysUntil = getDaysUntilDue(deadline.dueDate);
              const isUrgent = daysUntil <= 2;
              
              return (
                <div
                  key={deadline.id}
                  className={`flex flex-col sm:flex-row gap-4 p-4 lg:p-5 rounded-lg border transition-all duration-200 hover:shadow-md ${
                    isUrgent
                      ? "bg-red-50 border-red-200 dark:bg-red-950/10 dark:border-red-800"
                      : "bg-gray-50 border-gray-200 dark:bg-neutral-800/50 dark:border-neutral-700"
                  }`}
                >
                  <div className="flex-shrink-0 sm:self-start">
                    <div className={`w-12 h-12 lg:w-14 lg:h-14 rounded-full flex items-center justify-center ${
                      isUrgent
                        ? "bg-red-500 text-white"
                        : "bg-gradient-to-br from-orange-500 to-orange-600 text-white"
                    }`}>
                      <TypeIcon className="w-6 h-6 lg:w-7 lg:h-7" />
                    </div>
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2 mb-3">
                      <div className="flex-1">
                        <h4 className="font-semibold text-lg lg:text-xl text-gray-900 dark:text-white mb-1">
                          {deadline.title}
                        </h4>
                        <p className="text-sm lg:text-base text-gray-600 dark:text-gray-300 font-medium">
                          {deadline.course}
                        </p>
                      </div>
                      <div className="flex flex-row sm:flex-col items-start sm:items-end gap-2 sm:gap-1">
                        <Badge variant="outline" className={`${getPriorityColor(deadline.priority)} text-xs lg:text-sm`}>
                          {deadline.priority}
                        </Badge>
                        <Badge variant="secondary" className="text-xs lg:text-sm">
                          {getTypeLabel(deadline.type)}
                        </Badge>
                      </div>
                    </div>

                    {deadline.description && (
                      <p className="text-sm lg:text-base text-gray-600 dark:text-gray-300 mb-3 line-clamp-2">
                        {deadline.description}
                      </p>
                    )}

                    <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-sm lg:text-base text-gray-500 dark:text-gray-400">
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4" />
                        <span>{deadline.dueDate.toLocaleDateString('fr-FR')}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4" />
                        <span className={isUrgent ? "text-red-600 font-medium" : ""}>
                          {formatDueDate(deadline.dueDate)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default UpcomingDeadlines;
