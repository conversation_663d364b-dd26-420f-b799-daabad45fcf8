import { auth } from "@/auth";
import { db } from "@/lib/db";
import { Chapter, Course, Section, UserProgress } from "@prisma/client";
import { redirect } from "next/navigation";
import { CourseSidebarItem } from "./course-sidebar-item";
import { CourseProgress } from "@/components/shared/course-progress";

interface CourseSidebarProps {
  course: Course & {
    sections: (Section & {
      chapters: (Chapter & {
        userProgress: UserProgress[] | null;
      })[];
    })[];
  };
  progressCount: number;
}

export const CourseSidebar = async ({
  course,
  progressCount,
}: CourseSidebarProps) => {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return redirect("/");
  }

  const purchase = await db.purchase.findUnique({
    where: {
      userId_courseId: {
        userId,
        courseId: course.id,
      },
    },
  });

  return (
    <div className="h-full border-r flex flex-col overflow-y-auto shadow-sm bg-white dark:bg-gray-900">
      <div className="p-8 flex flex-col border-b">
        <h1 className="font-semibold text-gray-900 dark:text-white">
          {course.title}
        </h1>
        {purchase && (
          <div className="mt-10">
            <CourseProgress variant="success" value={progressCount} />
          </div>
        )}
      </div>
      <div className="flex flex-col w-full">
        {course.sections.map((section) => (
          <div key={section.id} className="mb-4">
            <h2 className="px-6 py-2 text-sm font-medium text-gray-600 dark:text-gray-400 border-b">
              {section.title}
            </h2>
            <div className="mt-1">
              {section.chapters.map((chapter) => (
                <CourseSidebarItem
                  key={chapter.id}
                  id={chapter.id}
                  label={chapter.title}
                  isCompleted={!!chapter.userProgress?.[0]?.isCompleted}
                  courseId={course.id}
                  isLocked={!chapter.isFree && !purchase}
                />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
