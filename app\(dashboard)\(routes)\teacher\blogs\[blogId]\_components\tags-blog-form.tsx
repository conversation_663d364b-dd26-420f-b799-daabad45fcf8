"use client";

import * as z from "zod";
import axios from "axios";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Blog, CategoryType } from "@prisma/client";

interface TagsBlogFormProps {
  initialData: Blog & {
    categories: { id: string; name: string; type: string }[];
  };
  blogId: string;
  options: { label: string; value: string; type: CategoryType }[];
}

const formSchema = z.object({
  categoryIds: z.array(z.string()).min(1, "Select at least one category"),
});

export const TagsBlogForm = ({
  initialData,
  blogId,
  options,
}: TagsBlogFormProps) => {
  const router = useRouter();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      categoryIds: initialData?.categories?.map((cat) => cat.id) || [],
    },
  });

  const { isSubmitting, isValid } = form.formState;

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      await axios.patch(`/api/blogs/${blogId}`, {
        categoryIds: values.categoryIds,
      });
      toast.success("Blog mise à jour");
      router.refresh();
    } catch {
      toast.error("Une erreur s'est produite!");
    }
  };

  const blogOptions = options.filter((option) => option.type === "BLOG");

  return (
    <div className="w-full">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full flex gap-4 items-start justify-between"
        >
          <div className="flex flex-col gap-4">
            {blogOptions.map((option) => (
              <FormField
                key={option.value}
                control={form.control}
                name="categoryIds"
                render={({ field }) => (
                  <FormItem className="flex items-center space-y-0 space-x-4">
                    <FormControl>
                      <input
                        type="checkbox"
                        id={option.value}
                        value={option.value}
                        checked={field.value.includes(option.value)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            field.onChange([...field.value, option.value]);
                          } else {
                            field.onChange(
                              field.value.filter(
                                (id: string) => id !== option.value
                              )
                            );
                          }
                        }}
                        className="cursor-pointer"
                      />
                    </FormControl>
                    <FormLabel
                      htmlFor={option.value}
                      className="cursor-pointer"
                    >
                      {option.label}
                    </FormLabel>
                    <FormMessage />
                  </FormItem>
                )}
              />
            ))}
          </div>
          <div className="flex items-center gap-x-2">
            <Button type="submit" disabled={!isValid || isSubmitting}>
              Enregistrer
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};
