"use client";

import { useState } from "react";
import { posts } from "#site/content";
import Title from "@/components/shared/Title";
import { Spotlight } from "@/components/ui/spotlight";
import { TagSelector } from "./TagSelector";
import { SortSelector } from "./SortSelector";
import { PostList } from "./PostList";
import { getAllTags } from "@/lib/utils";
import { SearchInput } from "./SearchInput";

export default function Page() {
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [sortOption, setSortOption] = useState("Most Recent");
  const allTags = getAllTags(posts);

  const displayPosts = posts
    .filter(
      (post) =>
        (selectedTags.length === 0 ||
          post.tags?.some((tag) => selectedTags.includes(tag))) &&
        (searchQuery === "" ||
          post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          post.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          post.tags?.some((tag) =>
            tag.toLowerCase().includes(searchQuery.toLowerCase())
          ))
    )
    .sort((a: any, b: any) => {
      if (sortOption === "Most Recent") {
        return new Date(b.date).getTime() - new Date(a.date).getTime();
      } else if (sortOption === "Most Popular") {
        // For now, we'll sort by date as a placeholder for popularity
        // Later you can add a popularity field or view count
        return new Date(b.date).getTime() - new Date(a.date).getTime();
      }
      return 0;
    });

  const handleTagClick = (tag: string) => {
    setSelectedTags((prevSelectedTags) => {
      if (prevSelectedTags.includes(tag)) {
        return prevSelectedTags.filter((t) => t !== tag);
      } else {
        return [...prevSelectedTags, tag];
      }
    });
  };

  return (
    <div className="relative w-full flex flex-col items-center justify-center gap-3 p-32 max-lg:p-8 min-h-screen">
      {/* Global unified background - same as homepage */}
      <div className="fixed inset-0 w-full h-full bg-white dark:bg-neutral-950 bg-dot-black/[0.05] dark:bg-dot-white/[0.05] -z-10"></div>

      {/* Global floating glows - same as homepage */}
      <div className="fixed top-20 left-20 w-96 h-96 bg-orange-400/15 rounded-full blur-3xl animate-pulse -z-10"></div>
      <div className="fixed top-1/3 right-10 w-80 h-80 bg-purple-400/15 rounded-full blur-3xl animate-pulse delay-1000 -z-10"></div>
      <div className="fixed bottom-1/4 left-1/3 w-72 h-72 bg-white/10 rounded-full blur-3xl animate-pulse delay-500 -z-10"></div>
      <div className="fixed bottom-20 right-20 w-64 h-64 bg-orange-400/10 rounded-full blur-3xl animate-pulse delay-2000 -z-10"></div>
      <div className="fixed top-2/3 left-10 w-56 h-56 bg-purple-400/10 rounded-full blur-3xl animate-pulse delay-1500 -z-10"></div>

      <div className="z-20 max-w-[100rem] flex flex-col items-center relative">
        {/* Header Section */}
        <div className="text-center mb-12 animate-fade-in-up">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 text-sm font-medium mb-4">
            Microlearning
          </div>
          <h1 className="text-5xl font-bold mb-6 bg-gradient-to-r from-gray-900 via-orange-400 to-gray-900 dark:from-white dark:via-orange-400 dark:to-white bg-clip-text text-transparent">
            Boîte à Outils
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto leading-relaxed">
            Découvrez nos astuces pratiques pour maîtriser rapidement les concepts
            mathématiques et physiques les plus complexes. Recherchez des astuces
            par sujet ou difficulté et commencez à optimiser votre apprentissage
            dès maintenant.
          </p>
        </div>

        {/* Search Section */}
        <div className="w-full mb-12 animate-fade-in-up delay-200">
          <SearchInput
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            className="mb-8"
          />

          {/* Results summary */}
          <div className="text-center text-gray-600 dark:text-gray-400">
            {searchQuery || selectedTags.length > 0 ? (
              <p className="text-sm">
                {displayPosts.length} résultat{displayPosts.length !== 1 ? 's' : ''} trouvé{displayPosts.length !== 1 ? 's' : ''}
                {searchQuery && ` pour "${searchQuery}"`}
                {selectedTags.length > 0 && ` avec ${selectedTags.length} tag${selectedTags.length > 1 ? 's' : ''}`}
              </p>
            ) : (
              <p className="text-sm">{posts.length} astuces disponibles</p>
            )}
          </div>
        </div>

        {/* Filters Section */}
        <div className="mt-8 w-full flex flex-col lg:flex-row justify-between gap-8 animate-fade-in-up delay-400">
          <TagSelector
            allTags={allTags}
            selectedTags={selectedTags}
            onTagClick={handleTagClick}
          />
          <SortSelector sortOption={sortOption} onSortChange={setSortOption} />
        </div>

        {/* Results Section */}
        <div className="mt-16 w-full animate-fade-in-up delay-600">
          {displayPosts.length > 0 ? (
            <PostList posts={displayPosts} />
          ) : (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                Aucun résultat trouvé
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                {searchQuery || selectedTags.length > 0
                  ? "Essayez de modifier vos critères de recherche ou de supprimer certains filtres."
                  : "Aucune astuce disponible pour le moment."
                }
              </p>
              {(searchQuery || selectedTags.length > 0) && (
                <button
                  onClick={() => {
                    setSearchQuery("");
                    setSelectedTags([]);
                  }}
                  className="px-6 py-3 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors duration-200"
                >
                  Effacer tous les filtres
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
