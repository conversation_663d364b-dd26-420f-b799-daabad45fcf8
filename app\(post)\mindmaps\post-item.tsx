import { Calendar } from "lucide-react";
import Link from "next/link";
import { buttonVariants } from "@/components/ui/button";
import { cn, formatDate } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import { ArrowUpRight } from "lucide-react";

interface PostItemProps {
  image: string;
  slug: string;
  title: string;
  description?: string;
  date: string;
  tags?: Array<string>;
}

export function PostItem({
  image,
  slug,
  title,
  description,
  date,
  tags,
}: PostItemProps) {
  return (
    <main className="flex flex-col gap-6 ">
      <Link href={"/" + slug}>
        <Image src={image} alt={title} width={1500} height={500} />
      </Link>
      <div className="flex flex-col gap-6 max-sm:gap-4">
        <div className="flex flex-col gap-2 max-sm:gap-1">
          <p className="card-head">
            Equipe d'ALEPHNULL • <time dateTime={date}>{formatDate(date)}</time>
          </p>
          <Link href={"/" + slug}>
            <div className="flex justify-between items-center">
              <p className="card-title">{title}</p>
              <ArrowUpRight width={35} height={35} />
            </div>
          </Link>
          <p className="card-desc">{description}</p>
        </div>
        <div className="flex flex-wrap gap-2 w-full">
          {tags?.map((tag) => (
            <Badge variant={"outline"} className="text-sm max-lg:text-xs">
              {tag}
            </Badge>
          ))}
        </div>
      </div>
    </main>
  );
}
