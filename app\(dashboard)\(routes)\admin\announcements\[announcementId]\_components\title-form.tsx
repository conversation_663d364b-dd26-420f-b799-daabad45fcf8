"use client";

import { useForm } from "react-hook-form";
import * as z from "zod";
import axios from "axios";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

interface TitleFormProps {
  initialData: {
    title: string;
  };
  announcementId: string;
}

const formSchema = z.object({
  title: z.string().min(1, {
    message: "Le titre est requis",
  }).max(100, {
    message: "Le titre ne peut pas dépasser 100 caractères",
  }),
});

export const TitleForm = ({ initialData, announcementId }: TitleFormProps) => {
  const router = useRouter();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData,
  });

  const { isSubmitting, isValid } = form.formState;

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      await axios.patch(`/api/announcements/${announcementId}`, values);
      toast.success("Titre mis à jour avec succès! ✏️", {
        description: "Le titre de votre annonce a été modifié",
        duration: 3000,
      });
      router.refresh();
    } catch (error) {
      toast.error("Erreur lors de la mise à jour", {
        description: "Impossible de modifier le titre. Veuillez réessayer.",
        duration: 4000,
      });
    }
  };

  return (
    <div className="w-full">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full flex gap-4 items-center max-sm:flex-col max-sm:items-end"
        >
          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormControl>
                  <Input
                    disabled={isSubmitting}
                    placeholder="ex. 'Nouvelle fonctionnalité disponible'"
                    {...field}
                    className="w-full max-md:text-sm"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button
            disabled={!isValid || isSubmitting}
            type="submit"
            className="max-sm:w-full"
          >
            Enregistrer
          </Button>
        </form>
      </Form>
    </div>
  );
};
