import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Eye, EyeOff, Users, Lock } from "lucide-react";
import { cn } from "@/lib/utils";

interface DeckProps {
  name: string;
  //   icon: React.ComponentType<{ className?: string }>;
  color: string;
  description: string;
  //   tags: string[];w
  visibility: "private" | "public";
  className?: string;
}

export function Deck({
  name,
  //   icon: Icon,
  color,
  description,
  //   tags,
  visibility,
  className,
}: DeckProps) {
  return (
    <div className={cn("relative group", className)}>
      {/* Stacked cards effect */}
      <div
        className="absolute inset-0 rounded-lg shadow-sm transform translate-x-1 translate-y-1 opacity-30"
        style={{ backgroundColor: color }}
      />
      <div
        className="absolute inset-0 rounded-lg shadow-md transform translate-x-0.5 translate-y-0.5 opacity-60"
        style={{ backgroundColor: color }}
      />

      {/* Main card */}
      <Card className="relative bg-white border-2 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 cursor-pointer">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <div
                className="p-3 rounded-lg shadow-sm"
                style={{ backgroundColor: `${color}20`, color: color }}
              >
                {/* <Icon className="w-6 h-6" /> */}
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-lg text-gray-900 line-clamp-1">
                  {name}
                </h3>
                <div className="flex items-center gap-1 mt-1">
                  {visibility === "private" ? (
                    <Lock className="w-3 h-3 text-gray-500" />
                  ) : (
                    <Users className="w-3 h-3 text-gray-500" />
                  )}
                  <span className="text-xs text-gray-500 capitalize">
                    {visibility}
                  </span>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-1">
              {visibility === "private" ? (
                <EyeOff className="w-4 h-4 text-gray-400" />
              ) : (
                <Eye className="w-4 h-4 text-gray-400" />
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          <p className="text-sm text-gray-600 mb-4 line-clamp-2">
            {description}
          </p>

          {/* <div className="flex flex-wrap gap-1">
            {tags.slice(0, 3).map((tag, index) => (
              <Badge
                key={index}
                variant="secondary"
                className="text-xs px-2 py-0.5"
                style={{
                  backgroundColor: `${color}15`,
                  color: color,
                  borderColor: `${color}30`,
                }}
              >
                {tag}
              </Badge>
            ))}
            {tags.length > 3 && (
              <Badge variant="outline" className="text-xs px-2 py-0.5">
                +{tags.length - 3}
              </Badge>
            )}
          </div> */}
        </CardContent>
      </Card>
    </div>
  );
}
