import React from "react";
import Logo from "./logo";
import { SidebarRoutes } from "./sidebar-routes";
import AccountCard from "@/components/auth/account-card";
import { ModeToggle } from "@/components/shared/ThemeSwitcher";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Home } from "lucide-react"; // Importing Home icon from lucide-react

const sidebar = () => {
  return (
    <div className="h-full border-r border-color flex flex-col overflow-y-auto shadow-sm justify-between relative">
      <div>
        <div className="p-6">
          <Logo />
        </div>
        <div>
          <AccountCard />
        </div>
        <div className="w-full flex flex-col mt-4">
          <SidebarRoutes />
        </div>
      </div>
      <div className="w-full p-6 flex justify-between">
        <ModeToggle />
        <Link href="/">
          <Button variant="secondary" size="icon">
            <Home />
          </Button>
        </Link>
      </div>
      <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-orange-500 to-transparent blur-[125px] pointer-events-none"></div>
    </div>
  );
};

export default sidebar;
