import { auth } from "@/auth";
import DashboardPageTitle from "@/app/(dashboard)/_components/dashboard-page-title";
import BreadCrumb from "@/components/shared/breadcrumb";
import { IconBadge } from "@/components/shared/icon-badge";

import { Separator } from "@/components/ui/separator";
import { Spotlight } from "@/components/ui/spotlight";
import {
  ChartSpline,
  GraduationCap,
  MonitorPlay,
  Settings2,
  User,
  Users,
} from "lucide-react";
import { redirect } from "next/navigation";
import { db } from "@/lib/db";
import { UserChart } from "./_components/user/user-chart";
import { DataTable } from "./_components/course/data-table";
import { columns } from "./_components/course/columns";
import DashboardSectionTitle from "@/app/(dashboard)/_components/dashboard-section-title";

const page = async () => {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return redirect("/");
  }

  const courses = await db.course.findMany({
    include: {
      categories: true,
    },
  });

  return (
    <div className="w-full z-20 flex flex-col">
      <BreadCrumb />
      <DashboardPageTitle
        title="Métriques et Analyses"
        description="Suivez et analysez vos performances grâce à des données et rapports en temps réel."
      />
      <Separator />
      <DashboardSectionTitle
        icon={Users}
        title="Analytiques des utilisateurs"
      />
      <UserChart />
      <Separator className="mt-16" />
      <DashboardSectionTitle
        icon={GraduationCap}
        title="Analytiques des cours"
      />
      {/* <DataTable columns={columns} data={courses} /> */}
      <Separator className="mt-16" />
      <DashboardSectionTitle
        icon={ChartSpline}
        title="Metrics de Rétention et de Churn"
      />
      <UserChart />
      <Separator className="mt-16" />
    </div>
  );
};

export default page;
