import { db } from "@/lib/db";
import { redirect } from "next/navigation";

const CourseIdPage = async ({ params }: { params: { courseId: string } }) => {
  const course = await db.course.findUnique({
    where: {
      id: params.courseId,
    },
    include: {
      sections: {
        where: {
          isPublished: true,
        },
        include: {
          chapters: {
            where: {
              isPublished: true,
            },
            orderBy: {
              position: "asc",
            },
          },
        },
        orderBy: {
          position: "asc",
        },
      },
    },
  });

  if (!course) {
    return redirect("/");
  }

  // Find the first published section with published chapters
  const firstPublishedSection = course.sections.find(
    (section) => section.chapters.length > 0
  );

  if (!firstPublishedSection || firstPublishedSection.chapters.length === 0) {
    return redirect("/");
  }

  return redirect(
    `/courses/${course.id}/sections/${firstPublishedSection.id}/chapters/${firstPublishedSection.chapters[0].id}`
  );
};

export default CourseIdPage;
