import { db } from "@/lib/db";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import Link from "next/link";
import {
  ArrowLeft,
  Bell,
  FileText,
  ImageIcon,
  Settings,
  Tag,
  AlertTriangle,
  Calendar,
  Users,
} from "lucide-react";
import { Separator } from "@/components/ui/separator";
import DashboardPageTitle from "@/app/(dashboard)/_components/dashboard-page-title";
import { DashboardSection } from "@/app/(dashboard)/_components/dashboard-section";
import { TitleForm } from "./_components/title-form";
import { DescriptionForm } from "./_components/description-form";
import { ContentForm } from "./_components/content-form";
import { ImageForm } from "./_components/image-form";
import { TypeForm } from "./_components/type-form";
import { PriorityForm } from "./_components/priority-form";
import { TargetAudienceForm } from "./_components/target-audience-form";
import { ExpirationForm } from "./_components/expiration-form";
import { PublishForm } from "./_components/publish-form";
import { Actions } from "./_components/actions";

interface EditAnnouncementPageProps {
  params: {
    announcementId: string;
  };
}

const AnnouncementIdPage = async ({ params }: EditAnnouncementPageProps) => {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return redirect("/");
  }

  if (session.user.role !== "ADMIN" && session.user.role !== "TEACHER") {
    return redirect("/");
  }

  const announcement = await db.announcement.findUnique({
    where: {
      id: params.announcementId,
    },
    include: {
      author: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          image: true,
          role: true,
        },
      },
    },
  });

  if (!announcement) {
    return redirect("/admin/announcements");
  }

  // Check if user has permission to edit this announcement
  if (
    announcement.authorId !== session.user.id &&
    session.user.role !== "ADMIN"
  ) {
    return redirect("/admin/announcements");
  }

  const requiredFields = [
    announcement.title,
    announcement.description,
    announcement.type,
    announcement.priority,
  ];

  const totalFields = requiredFields.length;
  const completedFields = requiredFields.filter(Boolean).length;
  const completionText = `(${completedFields}/${totalFields})`;
  const isComplete = requiredFields.every(Boolean);

  return (
    <div className="relative w-full min-h-screen">
      {/* Enhanced background with more glows */}
      <div className="fixed inset-0 w-full h-full bg-white dark:bg-neutral-950 bg-dot-black/[0.03] dark:bg-dot-white/[0.03] -z-10"></div>
      
      {/* Multiple floating glows for dynamic effect */}
      <div className="fixed top-10 left-10 w-96 h-96 bg-orange-400/10 rounded-full blur-3xl animate-pulse -z-10"></div>
      <div className="fixed top-20 right-20 w-80 h-80 bg-purple-400/12 rounded-full blur-3xl animate-pulse delay-1000 -z-10"></div>
      <div className="fixed bottom-20 left-20 w-72 h-72 bg-blue-400/8 rounded-full blur-3xl animate-pulse delay-500 -z-10"></div>
      <div className="fixed bottom-10 right-10 w-64 h-64 bg-orange-400/15 rounded-full blur-3xl animate-pulse delay-2000 -z-10"></div>
      <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-56 h-56 bg-white/8 rounded-full blur-3xl animate-pulse delay-1500 -z-10"></div>
      <div className="fixed top-1/3 left-1/4 w-48 h-48 bg-purple-400/10 rounded-full blur-3xl animate-pulse delay-3000 -z-10"></div>
      <div className="fixed bottom-1/3 right-1/4 w-40 h-40 bg-orange-400/12 rounded-full blur-3xl animate-pulse delay-2500 -z-10"></div>

      <div className="relative z-10 w-full flex flex-col px-6 space-y-6">
        <Link
          href="/admin/announcements"
          className="flex items-center text-sm hover:opacity-75 transition mb-6"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Retour aux annonces
        </Link>

        <div className="flex items-start justify-between gap-y-4 max-2xl:flex-col">
          <DashboardPageTitle
            title="Configuration de l'annonce"
            description={`Champs complétés ${completionText}`}
          />
          <Actions
            disabled={!isComplete}
            announcementId={params.announcementId}
            isPublished={announcement.isPublished}
          />
        </div>

        <Separator className="mt-6 mb-8" />

        <div className="w-full h-full flex items-start justify-start gap-12 max-xl:flex-col max-sm:gap-6">
          {/* Colonne de gauche */}
          <div className="w-full flex flex-col grow gap-2 justify-between">
            <div className="flex flex-col gap-12">
              {/* Titre de l'annonce */}
              <div className="flex flex-col gap-4">
                <DashboardSection
                  icon={Bell}
                  title="Titre de l'annonce"
                  description="Définissez un titre clair pour votre annonce"
                />
                <TitleForm initialData={announcement} announcementId={announcement.id} />
              </div>

              {/* Description de l'annonce */}
              <div className="flex flex-col gap-4">
                <DashboardSection
                  icon={FileText}
                  title="Description courte"
                  description="Rédigez une description concise de votre annonce"
                />
                <DescriptionForm initialData={announcement} announcementId={announcement.id} />
              </div>

              {/* Contenu détaillé */}
              <div className="flex flex-col gap-4">
                <DashboardSection
                  icon={FileText}
                  title="Contenu détaillé"
                  description="Ajoutez du contenu détaillé (optionnel)"
                />
                <ContentForm initialData={announcement} announcementId={announcement.id} />
              </div>

              {/* Type d'annonce */}
              <div className="flex flex-col gap-4">
                <DashboardSection
                  icon={Tag}
                  title="Type d'annonce"
                  description="Sélectionnez le type d'annonce approprié"
                />
                <TypeForm initialData={announcement} announcementId={announcement.id} />
              </div>

              {/* Priorité */}
              <div className="flex flex-col gap-4">
                <DashboardSection
                  icon={AlertTriangle}
                  title="Priorité"
                  description="Définissez le niveau de priorité"
                />
                <PriorityForm initialData={announcement} announcementId={announcement.id} />
              </div>
            </div>
          </div>

          {/* Séparateur vertical */}
          <Separator orientation="vertical" decorative className="h-full" />

          {/* Colonne de droite */}
          <div className="w-full flex flex-col grow gap-2 justify-between">
            <div className="flex flex-col gap-12">
              {/* Image de l'annonce */}
              <div className="flex flex-col gap-4">
                <DashboardSection
                  icon={ImageIcon}
                  title="Image de l'annonce"
                  description="Téléchargez une image pour votre annonce"
                />
                <ImageForm initialData={announcement} announcementId={announcement.id} />
              </div>

              {/* Public cible */}
              <div className="flex flex-col gap-4">
                <DashboardSection
                  icon={Users}
                  title="Public cible"
                  description="Définissez qui peut voir cette annonce"
                />
                <TargetAudienceForm initialData={announcement} announcementId={announcement.id} />
              </div>

              {/* Date d'expiration */}
              <div className="flex flex-col gap-4">
                <DashboardSection
                  icon={Calendar}
                  title="Date d'expiration"
                  description="Définissez quand l'annonce expire (optionnel)"
                />
                <ExpirationForm initialData={announcement} announcementId={announcement.id} />
              </div>

              {/* Options de publication */}
              <div className="flex flex-col gap-4">
                <DashboardSection
                  icon={Settings}
                  title="Options de publication"
                  description="Gérez la publication et l'épinglage"
                />
                <PublishForm initialData={announcement} announcementId={announcement.id} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnnouncementIdPage;
