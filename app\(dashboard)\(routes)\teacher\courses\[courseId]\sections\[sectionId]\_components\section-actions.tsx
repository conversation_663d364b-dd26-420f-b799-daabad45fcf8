"use client";

import { ConfirmModal } from "@/components/modals/confirm-modal";
import { Button } from "@/components/ui/button";
import axios from "axios";
import { Eye, EyeOff, Trash } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

interface SectionActionsProps {
  disabled: boolean;
  courseId: string;
  sectionId: string;
  isPublished: boolean;
}

export const SectionActions = ({
  disabled,
  courseId,
  sectionId,
  isPublished,
}: SectionActionsProps) => {
  const router = useRouter();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const togglePublication = async () => {
    try {
      setIsLoading(true);

      if (isPublished) {
        await axios.patch(
          `/api/courses/${courseId}/sections/${sectionId}/unpublish`
        );
        toast({
          title: "Section dépubliée",
          description: "Cette section n'est plus visible pour les étudiants",
        });
      } else {
        await axios.patch(
          `/api/courses/${courseId}/sections/${sectionId}/publish`
        );
        toast({
          title: "Section publiée",
          description: "Cette section est maintenant visible dans le cours",
        });
      }

      router.refresh();
    } catch {
      toast({
        title: "Erreur",
        description: "Impossible de modifier l'état de publication",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const onDelete = async () => {
    try {
      setIsLoading(true);
      await axios.delete(`/api/courses/${courseId}/sections/${sectionId}`);
      toast({
        title: "Section supprimée",
        description: "La section a été supprimée définitivement",
      });
      router.refresh();
      router.push(`/teacher/courses/${courseId}`);
    } catch {
      toast({
        title: "Erreur",
        description: "Échec de la suppression de la section",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center gap-x-2">
      <Button
        onClick={togglePublication}
        disabled={disabled || isLoading}
        variant={isPublished ? "outline" : "default"}
        size="sm"
        className="flex items-center gap-2"
      >
        {isPublished ? (
          <>
            <EyeOff className="h-4 w-4" />
            Dépublier
          </>
        ) : (
          <>
            <Eye className="h-4 w-4" />
            Publier
          </>
        )}
      </Button>
      <ConfirmModal onConfirm={onDelete}>
        <Button
          size="sm"
          disabled={isLoading}
          className="flex items-center gap-2"
          variant="destructive"
        >
          <Trash className="h-4 w-4" />
          Supprimer
        </Button>
      </ConfirmModal>
    </div>
  );
};
