import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { db } from "@/lib/db";
import { getCourse } from "@/actions/get-course";
import BreadCrumb from "@/components/shared/breadcrumb";
import CourseDetailClient from "./_components/course-detail-client";
import { Section, Chapter } from "@prisma/client";

type SectionWithChapters = Section & {
  chapters: Chapter[];
};

interface CourseDetailPageProps {
  params: {
    courseId: string;
  };
}

export default async function CourseDetailPage({ params }: CourseDetailPageProps) {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return redirect("/");
  }

  // Fetch course data using the getCourse action
  const course = await getCourse(params.courseId);

  if (!course) {
    return redirect("/student/courses");
  }

  // Check if user has purchased the course
  const purchase = await db.purchase.findFirst({
    where: {
      userId,
      courseId: params.courseId,
    },
  });


  // Fetch sections and chapters for this course
  const sections: SectionWithChapters[] = await db.section.findMany({
    where: {
      courseId: params.courseId,
    },
    include: {
      chapters: {
        orderBy: {
          position: "asc",
        },
      },
    },
    orderBy: {
      position: "asc",
    },
  });

  // Get all chapters from all sections
  const allChapters = sections.flatMap(section => section.chapters);

  // Fetch user progress for all chapters
  const userProgress = await db.userProgress.findMany({
    where: {
      userId,
      chapterId: {
        in: allChapters.map(chapter => chapter.id),
      },
    },
  });

  const totalChapters = allChapters.length;
  const completedChapters = userProgress.filter(progress => progress.isCompleted).length;
  const progress = totalChapters > 0 ? (completedChapters / totalChapters) * 100 : 0;

  // Transform course data for the client component
  const courseData = {
    id: course.id,
    title: course.title,
    description: course.description || "",
    imageUrl: course.imageUrl || "",
    instructor: "Dr. Hamza Ihind", // Default instructor
    instructorImage: "/images/default-instructor.jpg",
    category: course.categories?.[0]?.name || "Mathématiques",
    level: "Intermédiaire", // Default level
    duration: "40h", // Default duration
    totalChapters,
    completedChapters,
    progress,
    rating: 4.8, // Default rating
    totalStudents: 1250, // Default student count
    lastAccessed: new Date(),
    enrolledDate: purchase.createdAt,
    certificate: true,
  };

  return (
    <div className="relative w-full min-h-screen">
      {/* Enhanced background with more glows - same as other dashboard pages */}
      <div className="fixed inset-0 w-full h-full bg-white dark:bg-neutral-950 bg-dot-black/[0.03] dark:bg-dot-white/[0.03] -z-10"></div>
      
      {/* Multiple floating glows for dynamic effect */}
      <div className="fixed top-10 left-10 w-96 h-96 bg-orange-400/10 rounded-full blur-3xl animate-pulse -z-10"></div>
      <div className="fixed top-20 right-20 w-80 h-80 bg-purple-400/12 rounded-full blur-3xl animate-pulse delay-1000 -z-10"></div>
      <div className="fixed bottom-20 left-20 w-72 h-72 bg-blue-400/8 rounded-full blur-3xl animate-pulse delay-500 -z-10"></div>
      <div className="fixed bottom-10 right-10 w-64 h-64 bg-orange-400/15 rounded-full blur-3xl animate-pulse delay-2000 -z-10"></div>
      <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-56 h-56 bg-white/8 rounded-full blur-3xl animate-pulse delay-1500 -z-10"></div>
      <div className="fixed top-1/3 left-1/4 w-48 h-48 bg-purple-400/10 rounded-full blur-3xl animate-pulse delay-3000 -z-10"></div>
      <div className="fixed bottom-1/3 right-1/4 w-40 h-40 bg-orange-400/12 rounded-full blur-3xl animate-pulse delay-2500 -z-10"></div>

      <div className="relative z-10 w-full flex flex-col">
        <div className="px-6 space-y-6">
          <BreadCrumb />
          
          {/* Main Content */}
          <CourseDetailClient
            course={courseData}
            chapters={sections}
          />
        </div>
      </div>
    </div>
  );
}
