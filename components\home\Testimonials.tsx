"use client";

import React, { useState, useEffect } from "react";

const Testimonials: React.FC = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  const testimonials = [
    {
      id: 1,
      name: "<PERSON><PERSON>",
      role: "Étudiante en Informatique",
      avatar: "👩‍💻",
      content: "ALEPHNULL a transformé ma façon d'apprendre. Les cours sont interactifs, les professeurs sont passionnés et la communauté est incroyable. J'ai pu décrocher mon stage de rêve grâce aux compétences acquises ici !",
      rating: 5
    },
    {
      id: 2,
      name: "<PERSON><PERSON><PERSON>",
      role: "Étudiant en Mathématiques",
      avatar: "👨‍🎓",
      content: "La qualité des cours et l'accompagnement personnalisé m'ont permis de surmonter mes difficultés en mathématiques. Aujourd'hui, c'est devenu ma matière préférée !",
      rating: 4.5
    },
    {
      id: 3,
      name: "<PERSON><PERSON>",
      role: "Étudiante en Physique",
      avatar: "👩‍🔬",
      content: "L'approche pédagogique d'ALEPHNULL est révolutionnaire. Les concepts complexes deviennent accessibles grâce aux explications claires et aux exemples pratiques.",
      rating: 4
    },
    {
      id: 4,
      name: "Omar Idrissi",
      role: "Étudiant en Ingénierie",
      avatar: "👨‍💼",
      content: "Grâce à ALEPHNULL, j'ai pu approfondir mes connaissances techniques tout en développant mes soft skills. Une expérience complète et enrichissante !",
      rating: 4.5
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [testimonials.length]);

  return (
    <section className="w-full min-h-screen py-24 px-8 relative">

      <div className="relative z-10 max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-16 animate-fade-in-up">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 text-sm font-medium mb-4">
            Témoignages
          </div>
          <h2 className="text-5xl font-bold mb-6 bg-gradient-to-r from-gray-900 via-orange-400 to-gray-900 dark:from-white dark:via-orange-400 dark:to-white bg-clip-text text-transparent">
            Ce que disent nos étudiants
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Découvrez les témoignages authentiques de notre communauté d'apprenants 
            qui ont transformé leur parcours avec ALEPHNULL.
          </p>
        </div>

        {/* Main testimonial */}
        <div className="relative mb-16 overflow-hidden">
          <div className="bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-gray-200/50 dark:border-neutral-700/50 animate-fade-in-up transition-all duration-700 ease-in-out">
            {/* Quote icon */}
            <div className="text-6xl text-orange-500 mb-6 opacity-20 transition-all duration-500">"</div>

            <blockquote className="text-2xl font-medium text-gray-900 dark:text-white leading-relaxed mb-8 transition-all duration-500 transform">
              {testimonials[currentTestimonial].content}
            </blockquote>
            
            <div className="flex items-center justify-between max-md:flex-col max-md:items-start max-md:gap-4 transition-all duration-500">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-full flex items-center justify-center text-2xl transition-all duration-500 transform hover:scale-110">
                  {testimonials[currentTestimonial].avatar}
                </div>
                <div className="transition-all duration-500">
                  <div className="font-bold text-lg text-gray-900 dark:text-white">
                    {testimonials[currentTestimonial].name}
                  </div>
                  <div className="text-gray-600 dark:text-gray-300">
                    {testimonials[currentTestimonial].role}
                  </div>
                </div>
              </div>
              
              {/* Rating */}
              <div className="flex gap-1">
                {[...Array(5)].map((_, i) => {
                  const rating = testimonials[currentTestimonial].rating;
                  if (i < Math.floor(rating)) {
                    return <span key={i} className="text-yellow-400 text-xl">⭐</span>;
                  } else if (i === Math.floor(rating) && rating % 1 !== 0) {
                    return <span key={i} className="text-yellow-400 text-xl">⭐</span>;
                  } else {
                    return <span key={i} className="text-gray-300 dark:text-gray-600 text-xl">⭐</span>;
                  }
                })}
              </div>
            </div>
          </div>
        </div>

        {/* Testimonial navigation */}
        <div className="flex justify-center gap-3 mb-16">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentTestimonial(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentTestimonial 
                  ? 'bg-orange-500 w-8' 
                  : 'bg-gray-300 dark:bg-gray-600 hover:bg-orange-300'
              }`}
            />
          ))}
        </div>

        {/* All testimonials grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 animate-fade-in-up delay-600">
          {testimonials.map((testimonial, index) => (
            <div 
              key={testimonial.id}
              className={`p-6 bg-white/60 dark:bg-neutral-800/60 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-neutral-700/50 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 ${
                index === currentTestimonial ? 'ring-2 ring-orange-500' : ''
              }`}
            >
              <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-full flex items-center justify-center text-lg">
                  {testimonial.avatar}
                </div>
                <div>
                  <div className="font-semibold text-gray-900 dark:text-white">
                    {testimonial.name}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">
                    {testimonial.role}
                  </div>
                </div>
              </div>
              
              <p className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed mb-4">
                {testimonial.content.slice(0, 120)}...
              </p>
              
              <div className="flex gap-1">
                {[...Array(5)].map((_, i) => {
                  const rating = testimonial.rating;
                  if (i < Math.floor(rating)) {
                    return <span key={i} className="text-yellow-400 text-sm">⭐</span>;
                  } else if (i === Math.floor(rating) && rating % 1 !== 0) {
                    return <span key={i} className="text-yellow-400 text-sm">⭐</span>;
                  } else {
                    return <span key={i} className="text-gray-300 dark:text-gray-600 text-sm">⭐</span>;
                  }
                })}
              </div>
            </div>
          ))}
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-20 animate-fade-in-up delay-1000">
          <div className="text-center">
            <div className="text-4xl font-bold text-orange-500 mb-2">4.9/5</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Note moyenne</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-orange-500 mb-2">1000+</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Avis positifs</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-orange-500 mb-2">95%</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Recommandent</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-orange-500 mb-2">100%</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Satisfaction</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
