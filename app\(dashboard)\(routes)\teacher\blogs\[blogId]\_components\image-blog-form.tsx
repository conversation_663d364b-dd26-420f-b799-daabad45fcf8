"use client";

import { useState } from "react";
import axios from "axios";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";
import { ImageIcon, Trash2, Edit } from "lucide-react";
import Image from "next/image";
import { Blog } from "@prisma/client";
import { FileUpload } from "@/components/shared/file-upload";
import { Button } from "@/components/ui/button";

interface ImageBlogFormProps {
  initialData: Blog;
  blogId: string;
}

export const ImageBlogForm = ({ initialData, blogId }: ImageBlogFormProps) => {
  const router = useRouter();
  const [imageUrl, setImageUrl] = useState(initialData.image || "");
  const [isEditing, setIsEditing] = useState(!imageUrl); // Start in edit mode if no image is present

  const handleImageUpload = async (url: string) => {
    try {
      await axios.patch(`/api/blogs/${blogId}`, { image: url });
      toast.success("Blog image est mise à jour");
      setImageUrl(url);
      setIsEditing(false);
      router.refresh();
    } catch {
      toast.error("Something went wrong");
    }
  };

  const handleDeleteImage = async () => {
    try {
      await axios.patch(`/api/blogs/${blogId}`, { image: "" });
      toast.success("blog image deleted");
      setImageUrl("");
      setIsEditing(true);
    } catch {
      toast.error("Something went wrong!");
    }
  };

  return (
    <div className="w-full">
      {imageUrl && !isEditing ? (
        <div>
          <div className="relative aspect-video">
            <Image
              alt="Blog Image"
              fill
              className="object-cover rounded-md"
              src={imageUrl}
            />
          </div>
          <div className="flex gap-2 mt-2">
            <Button variant="secondary" onClick={() => setIsEditing(true)}>
              <Edit className="h-4 w-4 mr-2" /> Modifier
            </Button>
            <Button variant="destructive" onClick={handleDeleteImage}>
              <Trash2 className="h-4 w-4 mr-2" /> Supprimer
            </Button>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center border border-color h-60 rounded-md mt-4">
          <FileUpload
            endpoint="blogImage"
            onChange={(url) => {
              if (url) {
                handleImageUpload(url.url);
              }
            }}
          />
        </div>
      )}
    </div>
  );
};
