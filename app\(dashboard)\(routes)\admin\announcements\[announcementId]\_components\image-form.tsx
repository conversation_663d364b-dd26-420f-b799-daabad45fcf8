"use client";

import * as z from "zod";
import axios from "axios";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { ImageIcon, Edit, Trash2 } from "lucide-react";
import Image from "next/image";

import { Button } from "@/components/ui/button";
import { FileUpload } from "@/components/shared/file-upload";

interface ImageFormProps {
  initialData: {
    imageUrl: string | null;
  };
  announcementId: string;
}

const formSchema = z.object({
  imageUrl: z.string().min(1, {
    message: "L'image est requise",
  }),
});

export const ImageForm = ({ initialData, announcementId }: ImageFormProps) => {
  const router = useRouter();
  const [isEditing, setIsEditing] = useState(false);
  const [imageUrl, setImageUrl] = useState(initialData.imageUrl || "");

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      await axios.patch(`/api/announcements/${announcementId}`, values);
      toast.success("Image mise à jour! 🖼️", {
        description: "L'image de votre annonce a été modifiée",
        duration: 3000,
      });
      setImageUrl(values.imageUrl);
      setIsEditing(false);
      router.refresh();
    } catch (error) {
      toast.error("Erreur lors de la mise à jour", {
        description: "Impossible de modifier l'image. Veuillez réessayer.",
        duration: 4000,
      });
    }
  };

  const handleDeleteImage = async () => {
    try {
      await axios.patch(`/api/announcements/${announcementId}`, {
        imageUrl: "",
      });
      toast.success("Image supprimée! 🗑️", {
        description: "L'image de votre annonce a été supprimée",
        duration: 3000,
      });
      setImageUrl("");
      setIsEditing(true);
      router.refresh();
    } catch (error) {
      toast.error("Erreur lors de la suppression", {
        description: "Impossible de supprimer l'image. Veuillez réessayer.",
        duration: 4000,
      });
    }
  };

  return (
    <div className="w-full">
      {imageUrl && !isEditing ? (
        <div className="space-y-4">
          <div className="relative aspect-video bg-slate-100 dark:bg-slate-800 rounded-md overflow-hidden">
            <Image
              alt="Image de l'annonce"
              fill
              className="object-cover"
              src={imageUrl}
              priority
            />
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setIsEditing(true)}
              className="flex items-center gap-2"
            >
              <Edit className="h-4 w-4" />
              Modifier l'image
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteImage}
              className="flex items-center gap-2"
            >
              <Trash2 className="h-4 w-4" />
              Supprimer
            </Button>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex items-center justify-center h-60 bg-slate-100 dark:bg-slate-800 rounded-md border-2 border-dashed border-slate-300 dark:border-slate-600">
            <div className="text-center">
              <ImageIcon className="h-10 w-10 text-slate-400 mx-auto mb-2" />
              <p className="text-sm text-slate-500">Aucune image</p>
            </div>
          </div>
          <FileUpload
            endpoint="announcementImage"
            onChange={(url) => {
              if (url) {
                onSubmit({ imageUrl: url });
              }
            }}
          />
          <div className="text-xs text-muted-foreground">
            16:9 aspect ratio recommandé
          </div>
        </div>
      )}
    </div>
  );
};
