"use client";

import { FaUser } from "react-icons/fa";
import { useCurrentUser } from "@/hooks/use-current-user";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";

const AccountCard = () => {
  const user = useCurrentUser();

  const formatRole = (role: string) => {
    if (!role) return "";
    return role.charAt(0).toUpperCase() + role.slice(1).toLowerCase();
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="w-full flex flex-col items-center gap-4">
        <Avatar className="w-28 h-28 border-2 border-orange-500">
          <AvatarImage src={user?.image || ""} className="object-cover" />
          <AvatarFallback className="bg-orange-500">
            <FaUser className="text-white" />
          </AvatarFallback>
        </Avatar>
        <div className="flex flex-col items-center justify-start">
          <p className="text-2xl font-medium text-center text-black dark:text-white">
            {user?.firstName} {user?.lastName}
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400 text-center">
            {user?.username ?? ""} • {formatRole(user?.role ?? "")}
          </p>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        <DropdownMenuItem className="text-orange-600">
          Hello there
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default AccountCard;
