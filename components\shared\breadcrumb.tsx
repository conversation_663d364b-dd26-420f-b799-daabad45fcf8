"use client";

import { usePathname } from "next/navigation";
import { useCurrentUser } from "@/hooks/use-current-user";
import Image from "next/image";

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { FaUser } from "react-icons/fa";
import React from "react";

const BreadCrumb: React.FC = () => {
  const user = useCurrentUser();
  const pathname = usePathname();

  const pathnames = pathname.split("/").filter((x) => x);

  return (
    <Breadcrumb className="mb-8 max-md:mb-4">
      <BreadcrumbList>
        {pathnames.map((value, index) => {
          const href = `/${pathnames.slice(0, index + 1).join("/")}`;
          const capitalizedValue =
            value.charAt(0).toUpperCase() + value.slice(1);
          return (
            <React.Fragment key={href}>
              <BreadcrumbItem>
                <BreadcrumbLink href={href} className="text-sm max-md:text-xs">
                  {capitalizedValue}
                </BreadcrumbLink>
              </BreadcrumbItem>
              {index < pathnames.length - 1 && <BreadcrumbSeparator />}
            </React.Fragment>
          );
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
};

export default BreadCrumb;
