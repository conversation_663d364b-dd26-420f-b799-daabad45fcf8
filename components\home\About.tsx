import React from "react";
import { BookOpen, Users, Award, Zap, Target, Heart } from "lucide-react";

const About = () => {
  const features = [
    {
      icon: BookOpen,
      title: "Cours de qualité supérieure",
      desc: "Des contenus pédagogiques conçus par des experts du domaine, régulièrement mis à jour selon les dernières tendances et technologies du marché."
    },
    {
      icon: Users,
      title: "Communauté d'apprenants",
      desc: "Rejoignez une communauté dynamique d'étudiants motivés. Échangez, collaborez et progressez ensemble dans un environnement bienveillant."
    },
    {
      icon: Award,
      title: "Certifications reconnues",
      desc: "Obtenez des certificats valorisés par les employeurs. Nos formations sont alignées sur les besoins réels du marché du travail."
    },
    {
      icon: Zap,
      title: "Apprentissage interactif",
      desc: "Méthodes pédagogiques innovantes avec des exercices pratiques, des projets concrets et un suivi personnalisé de votre progression."
    },
    {
      icon: Target,
      title: "Objectifs personnalisés",
      desc: "Définissez vos propres objectifs d'apprentissage. Notre plateforme s'adapte à votre rythme et à vos besoins spécifiques."
    },
    {
      icon: Heart,
      title: "Support dédié",
      desc: "Une équipe pédagogique disponible pour vous accompagner. Bénéficiez d'un mentorat personnalisé tout au long de votre parcours."
    }
  ];

  return (
    <section className="flex flex-col items-center justify-center w-full min-h-screen py-24 px-8 relative">

      <div className="relative z-10 max-w-7xl mx-auto">
        <div className="text-center mb-16 animate-fade-in-up">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 text-sm font-medium mb-4">
            Notre mission
          </div>
          <h2 className="text-5xl font-bold mb-6 bg-gradient-to-r from-gray-900 via-orange-400 to-gray-900 dark:from-white dark:via-orange-400 dark:to-white bg-clip-text text-transparent">
            Pourquoi choisir ALEPHNULL ?
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Nous révolutionnons l'apprentissage en ligne avec une approche innovante,
            des outils de pointe et une communauté bienveillante pour votre réussite.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-16">
          {features.map((feature, index) => (
            <div
              key={index}
              className="group animate-fade-in-up h-full"
              style={{ animationDelay: `${index * 200}ms` }}
            >
              <div className="relative p-8 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-neutral-700/50 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 hover:scale-105 h-full flex flex-col">
                {/* Glow effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-orange-500/10 to-purple-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                <div className="relative z-10 flex flex-col h-full">
                  <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mb-6 shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                    <feature.icon className="w-8 h-8 text-white" />
                  </div>

                  <h3 className="text-xl font-bold mb-4 text-gray-900 dark:text-white group-hover:text-orange-600 dark:group-hover:text-orange-400 transition-colors duration-300">
                    {feature.title}
                  </h3>

                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed flex-grow">
                    {feature.desc}
                  </p>
                </div>

                {/* Decorative corner */}
                <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-orange-500/20 to-transparent rounded-bl-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>
            </div>
          ))}
        </div>

        {/* Call to action */}
        <div className="text-center mt-20 animate-fade-in-up delay-1000">
          <div className="inline-flex items-center gap-4 px-8 py-4 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 cursor-pointer">
            <span className="font-semibold">Découvrez notre approche unique</span>
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
