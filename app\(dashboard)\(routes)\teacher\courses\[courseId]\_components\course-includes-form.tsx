"use client";

import { useForm } from "react-hook-form";
import * as z from "zod";
import axios from "axios";
import { zodResolver } from "@hookform/resolvers/zod";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Course } from "@prisma/client";
import DynamicInputList from "@/components/shared/dynamic-input-list";

interface CourseIncludesFormProps {
  initialData: Course;
  courseId: string;
}

const formSchema = z.object({
  courseIncludes: z
    .array(
      z.string().min(1, {
        message: "Un élément ne peut pas être vide",
      })
    )
    .min(1, {
      message: "Au moins un élément est requis",
    }),
});

export const CourseIncludesForm = ({
  initialData,
  courseId,
}: CourseIncludesFormProps) => {
  const router = useRouter();
  const { toast } = useToast();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      courseIncludes: initialData?.courseIncludes || [""],
    },
  });

  const { isSubmitting, isValid } = form.formState;

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      await axios.patch(`/api/courses/${courseId}`, {
        courseIncludes: values.courseIncludes.filter(
          (item) => item.trim() !== ""
        ),
      });

      toast({
        title: "Contenu mis à jour",
        description: "Les éléments inclus dans le cours ont été enregistrés",
      });
      router.refresh();
    } catch {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la mise à jour",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="w-full">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="courseIncludes"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <DynamicInputList
                    items={field.value}
                    onAdd={(item) => field.onChange([...field.value, item])}
                    onEdit={(index, newItem) => {
                      const updatedItems = [...field.value];
                      updatedItems[index] = newItem;
                      field.onChange(updatedItems);
                    }}
                    onDelete={(index) => {
                      const updatedItems = field.value.filter(
                        (_, i) => i !== index
                      );
                      field.onChange(
                        updatedItems.length > 0 ? updatedItems : [""]
                      );
                    }}
                    placeholder="Que comprend ce cours ? (ex: Exercices pratiques, Supports PDF, etc.)"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </form>
      </Form>
    </div>
  );
};
