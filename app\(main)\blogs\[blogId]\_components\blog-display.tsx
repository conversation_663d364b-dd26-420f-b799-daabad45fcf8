import React from "react";
import ReactMarkdown from "react-markdown";
import remarkMath from "remark-math";
import rehypeKatex from "rehype-katex";
import "katex/dist/katex.css";

interface BlogDisplayProps {
  content: string;
  className?: string;
}

const BlogDisplay: React.FC<BlogDisplayProps> = ({ content, className }) => {
  return (
    <div
      className={`w-full prose prose-lg dark:prose-invert max-w-none max-md:prose-base ${
        className || ""
      }`}
    >
      <ReactMarkdown
        remarkPlugins={[remarkMath]}
        rehypePlugins={[rehypeKatex]}
        components={{
          code({ className, children, ...props }) {
            const match = /language-(\w+)/.exec(className || "");
            return match ? (
              <pre className={`${className} rounded p-4 overflow-auto`}>
                <code {...props} className={match[1]}>
                  {String(children).replace(/\n$/, "")}
                </code>
              </pre>
            ) : (
              <code
                className={`${className} px-1 py-0.5 rounded bg-gray-100 dark:bg-gray-800`}
                {...props}
              >
                {children}
              </code>
            );
          },
          // Add custom styling for headings
          h1: ({ ...props }) => (
            <h1 className="text-3xl font-bold mt-8 mb-4" {...props} />
          ),
          h2: ({ ...props }) => (
            <h2 className="text-2xl font-bold mt-6 mb-3" {...props} />
          ),
          h3: ({ ...props }) => (
            <h3 className="text-xl font-bold mt-5 mb-2" {...props} />
          ),
          // Style blockquotes
          blockquote: ({ ...props }) => (
            <blockquote
              className="border-l-4 border-primary pl-4 italic my-4"
              {...props}
            />
          ),
          // Style links
          a: ({ ...props }) => (
            <a
              className="text-blue-500 hover:text-blue-700 underline transition-colors"
              {...props}
            />
          ),
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};

export default BlogDisplay;
