import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { getAnnouncementById } from "@/actions/get-announcements";
import BreadCrumb from "@/components/shared/breadcrumb";
import { AnnouncementDetail } from "./_components/announcement-detail";

interface AnnouncementDetailPageProps {
  params: {
    announcementId: string;
  };
}

const AnnouncementDetailPage = async ({ params }: AnnouncementDetailPageProps) => {
  const session = await auth();

  if (!session?.user?.id) {
    return redirect("/");
  }

  if (session.user.role !== "USER") {
    return redirect("/");
  }

  const announcement = await getAnnouncementById(params.announcementId);

  if (!announcement) {
    return redirect("/student/announcements");
  }

  // Vérifier si l'annonce est publiée
  if (!announcement.isPublished) {
    return redirect("/student/announcements");
  }

  // Vérifier si l'annonce n'a pas expiré
  if (announcement.expiresAt && new Date(announcement.expiresAt) < new Date()) {
    return redirect("/student/announcements");
  }

  return (
    <div className="w-full min-h-screen">
      <BreadCrumb />
      <AnnouncementDetail announcement={announcement} />
    </div>
  );
};

export default AnnouncementDetailPage;
