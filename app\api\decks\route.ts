import { auth } from "@/auth";
import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { Color, Icon, Tag } from "@prisma/client";

export async function POST(req: Request) {
  const session = await auth();
  try {
    const userId = session?.user.id;
    const { name, tag, icon, color, description } = await req.json();

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const deck = await db.deck.create({
      data: {
        name,
        tag,
        icon,
        color,
        description: description || "",
        userId,
      },
    });

    return NextResponse.json(deck);
  } catch (error) {
    console.log("[DECKS]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}

export async function GET() {
  const session = await auth();

  try {
    const userId = session?.user.id;

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const deck = await db.deck.findMany({
      where: {
        userId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json(deck);
  } catch (error) {
    console.log("[DECKS]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}
