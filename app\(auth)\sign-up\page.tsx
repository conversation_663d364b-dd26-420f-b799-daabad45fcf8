import React from "react";
import RegisterForm from "@/components/auth/RegisterForm";
import { Hero<PERSON>ighlight } from "@/components/ui/hero-highlight";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import User from "@/components/cards/User";
import PageQuote from "@/components/auth/PageQuote";

const Signup = () => {
  return (
    <main className="w-full flex justify-evenly items-center max-xl:flex-col max-xl:gap-16 mb-20">
      <PageQuote />
      <RegisterForm />
    </main>
  );
};

export default Signup;
