import { auth } from "@/auth";
import { redirect } from "next/navigation";
import BreadCrumb from "@/components/shared/breadcrumb";
import DashboardTitle from "@/app/(dashboard)/_components/dashboard-page-title";
import { Separator } from "@/components/ui/separator";
import { ActivityChart } from "./_components/activity-chart";
import Teachers from "./_components/teachers";

export default async function Courses() {
  const session = await auth();
  const userId = session?.user.id;
  const userRole = session?.user.role;

  if (!userId) {
    return redirect("/");
  }

  // Ensure only admins can access this page
  if (userRole !== "ADMIN") {
    return redirect("/dashboard");
  }

  return (
    <div className="z-10 w-full h-[100vh] flex flex-col">
      <BreadCrumb />
      <DashboardTitle
        title={`Bienvenue sur votre tableau de bord des cours, ${session.user?.firstName}`}
        description="<PERSON><PERSON><PERSON><PERSON> et gérez vos cours en toute simplicité."
      />
      <Separator />
      <ActivityChart />
      <Separator />
      <Teachers />
    </div>
  );
}
