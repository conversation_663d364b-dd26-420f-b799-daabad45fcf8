import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/lib/db";
import Mux from "@mux/mux-node";

const mux = new Mux({
  tokenId: process.env.MUX_TOKEN_ID,
  tokenSecret: process.env.MUX_TOKEN_SECRET,
});

export async function DELETE(
  req: Request,
  { params }: { params: { courseId: string } }
) {
  try {
    const session = await auth();
    const userId = session?.user.id;

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Fetch the course with sections and their chapters
    const course = await db.course.findUnique({
      where: {
        id: params.courseId,
        userId,
      },
      include: {
        sections: {
          include: {
            chapters: {
              include: {
                muxData: true, // Include Mux data for cleanup
              },
            },
          },
        },
        enrollments: true,
      },
    });

    if (!course) {
      return new NextResponse("Not Found", { status: 404 });
    }

    // Handle Mux cleanup for chapters
    for (const section of course.sections) {
      for (const chapter of section.chapters) {
        if (chapter.muxData?.assetId) {
          await mux.video.assets.delete(chapter.muxData.assetId); // Delete Mux asset
        }
      }
    }

    // Optional: Calculate Completion Rate Before Deletion
    const totalEnrolled = course.enrollments.length || 1; // Avoid division by zero
    const completedUsers = await db.userProgress.groupBy({
      by: ["userId"],
      where: {
        chapter: {
          section: {
            courseId: course.id,
          },
        },
        isCompleted: true,
      },
    });

    const completionRate = (
      (completedUsers.length / totalEnrolled) *
      100
    ).toFixed(2);

    // Delete the course (cascading deletes sections and chapters)
    const deletedCourse = await db.course.delete({
      where: {
        id: params.courseId,
      },
    });

    return NextResponse.json({
      ...deletedCourse,
      completionRate,
    });
  } catch (error) {
    console.log("[COURSE_ID_DELETE_ERROR]: ", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}

export async function PATCH(
  req: Request,
  { params }: { params: { courseId: string } }
) {
  const session = await auth();

  try {
    const userId = session?.user.id;
    const { courseId } = params;
    const values = await req.json();

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { categoryIds, ...rest } = values;

    const updateData: any = { ...rest };

    // Update categories if provided
    if (categoryIds && Array.isArray(categoryIds)) {
      updateData.categories = {
        set: categoryIds.map((id: string) => ({ id })),
      };
    }

    // Update the course
    const course = await db.course.update({
      where: {
        id: courseId,
        userId,
      },
      data: updateData,
      include: {
        enrollments: true,
        sections: {
          include: {
            chapters: true,
          },
        },
      },
    });

    // Calculate Completion Rate
    const totalEnrolled = course.enrollments.length || 1; // Avoid division by zero
    const completedUsers = await db.userProgress.groupBy({
      by: ["userId"],
      where: {
        chapter: {
          section: {
            courseId,
          },
        },
        isCompleted: true,
      },
    });

    const completionRate = (
      (completedUsers.length / totalEnrolled) *
      100
    ).toFixed(2);

    return NextResponse.json({
      ...course,
      completionRate,
    });
  } catch (error) {
    console.log("[COURSE_ID_PATCH_ERROR]: ", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}
