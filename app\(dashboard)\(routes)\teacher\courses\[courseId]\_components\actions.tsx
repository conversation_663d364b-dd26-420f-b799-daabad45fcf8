"use client";

import { ConfirmModal } from "@/components/modals/confirm-modal";
import { Button } from "@/components/ui/button";
import axios from "axios";
import { Eye, EyeOff, Trash2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

interface ActionsProps {
  disabled: boolean;
  courseId: string;
  isPublished: boolean;
}

export const Actions = ({ disabled, courseId, isPublished }: ActionsProps) => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const togglePublication = async () => {
    try {
      setIsLoading(true);
      const action = isPublished ? "unpublish" : "publish";

      await axios.patch(`/api/courses/${courseId}/${action}`);

      toast.success(isPublished ? "Cours dépublié! 📝" : "Cours publié! 🚀", {
        description: isPublished
          ? "Ce cours n'est plus visible par les étudiants"
          : "Ce cours est maintenant disponible pour les étudiants",
        duration: 4000,
      });

      router.refresh();
    } catch (error) {
      toast.error("Erreur de publication", {
        description: "Impossible de modifier l'état de publication du cours",
        duration: 4000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    try {
      setIsLoading(true);
      await axios.delete(`/api/courses/${courseId}`);

      toast.success("Cours supprimé! 🗑️", {
        description: "Le cours a été supprimé définitivement",
        duration: 4000,
      });

      router.refresh();
      router.push(`/teacher/courses`);
    } catch (error) {
      toast.error("Erreur de suppression", {
        description: "Échec de la suppression du cours. Veuillez réessayer.",
        duration: 4000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center gap-x-2">
      <Button
        onClick={togglePublication}
        disabled={disabled || isLoading}
        variant={isPublished ? "outline" : "default"}
        size="sm"
        className="flex items-center gap-2"
      >
        {isPublished ? (
          <>
            <EyeOff className="h-4 w-4" />
            Dépublier
          </>
        ) : (
          <>
            <Eye className="h-4 w-4" />
            Publier
          </>
        )}
      </Button>

      <ConfirmModal onConfirm={handleDelete}>
        <Button
          size="sm"
          disabled={isLoading}
          variant="destructive"
          className="flex items-center gap-2"
        >
          <Trash2 className="h-4 w-4" />
          Supprimer
        </Button>
      </ConfirmModal>
    </div>
  );
};
