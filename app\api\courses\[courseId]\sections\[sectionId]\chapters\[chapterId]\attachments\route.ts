import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/lib/db";

export async function POST(
  req: Request,
  { params }: { params: { chapterId: string } }
) {
  try {
    const session = await auth();
    const userId = session?.user.id;
    const { url } = await req.json();

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Verify the user owns the chapter's course
    const chapter = await db.chapter.findUnique({
      where: {
        id: params.chapterId,
        section: {
          course: {
            userId: userId,
          },
        },
      },
    });

    if (!chapter) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const attachment = await db.attachment.create({
      data: {
        url,
        name: url.split("/").pop() || "file",
        chapterId: params.chapterId,
      },
    });

    return NextResponse.json(attachment);
  } catch (error) {
    console.log("CHAPTER_ATTACHMENT_POST", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}
